<?php

namespace App\Http\Controllers;

use App\Models\Camp;
use App\Models\Post;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CampController extends Controller
{
    public function index()
    {
        // Get public camps and camps user is a member of
        $camps = Camp::where('type', 'public')
            ->orWhereHas('members', function($query) {
                $query->where('user_id', Auth::id());
            })
            ->withCount('members')
            ->with('owner')
            ->latest()
            ->paginate(10);
            
        return view('camp.index', compact('camps'));
    }
    
    public function create()
    {
        return view('camp.create');
    }
    
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'type' => 'required|in:public,private',
        ]);
        
        $camp = Camp::create([
            'name' => $validated['name'],
            'description' => $validated['description'],
            'type' => $validated['type'],
            'created_by' => Auth::id(),
        ]);
        
        // Add creator as a member
        $camp->members()->attach(Auth::id());
        
        return redirect()->route('camps.show', $camp)
            ->with('success', 'Camp created successfully!');
    }
    
    public function show(Camp $camp)
    {
        $camp->load(['posts.user', 'members', 'owner']);
        $camp->loadCount('members');
        
        // Check if user can access this camp
        if ($camp->type === 'private' && !$camp->isMember(Auth::user())) {
            abort(403, 'This is a private camp.');
        }
        
        return view('camp.show', compact('camp'));
    }
    
    public function join(Camp $camp)
    {
        // Check if already a member
        if ($camp->isMember(Auth::user())) {
            return redirect()->route('camps.show', $camp)
                ->with('info', 'You are already a member of this camp.');
        }
        
        // Check if camp is private
        if ($camp->type === 'private') {
            // For now, allow joining private camps directly
            // In a real app, you might want to implement invitation system
        }
        
        $camp->members()->attach(Auth::id());
        
        return redirect()->route('camps.show', $camp)
            ->with('success', 'You have joined the camp!');
    }
    
    public function leave(Camp $camp)
    {
        // Check if creator is trying to leave
        if ($camp->created_by === Auth::id()) {
            return back()->with('error', 'As the creator, you cannot leave the camp.');
        }
        
        $camp->members()->detach(Auth::id());
        
        return redirect()->route('camps.index')
            ->with('success', 'You have left the camp.');
    }
    
    public function destroy(Camp $camp)
    {
        // Check if user is the creator
        if ($camp->created_by !== Auth::id()) {
            abort(403, 'Only the camp creator can delete this camp.');
        }
        
        $camp->delete();
        
        return redirect()->route('camps.index')
            ->with('success', 'Camp deleted successfully.');
    }
    
    public function storePost(Request $request, Camp $camp)
    {
        $validated = $request->validate([
            'content' => 'required|string',
        ]);
        
        // Check if user is a member
        if (!$camp->isMember(Auth::user())) {
            abort(403, 'You must be a member to post in this camp.');
        }
        
        Post::create([
            'camp_id' => $camp->id,
            'user_id' => Auth::id(),
            'content' => $validated['content'],
        ]);
        
        return back()->with('success', 'Post created successfully!');
    }
} 