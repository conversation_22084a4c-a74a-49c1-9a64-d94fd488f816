{"__meta": {"id": "01K0T0EVR5RSCFEWGNAYX6HZJC", "datetime": "2025-07-22 21:25:59", "utime": **********.17385, "method": "GET", "uri": "/home", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.807907, "end": **********.173859, "duration": 0.3659517765045166, "duration_str": "366ms", "measures": [{"label": "Booting", "start": **********.807907, "relative_start": 0, "end": **********.93852, "relative_end": **********.93852, "duration": 0.*****************, "duration_str": "131ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.93853, "relative_start": 0.*****************, "end": **********.17386, "relative_end": 1.1920928955078125e-06, "duration": 0.*****************, "duration_str": "235ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.953675, "relative_start": 0.****************, "end": **********.955608, "relative_end": **********.955608, "duration": 0.0019328594207763672, "duration_str": "1.93ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.022027, "relative_start": 0.*****************, "end": **********.172218, "relative_end": **********.172218, "duration": 0.***************, "duration_str": "150ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: home", "start": **********.023707, "relative_start": 0.*****************, "end": **********.023707, "relative_end": **********.023707, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.036413, "relative_start": 0.*****************, "end": **********.036413, "relative_end": **********.036413, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.043888, "relative_start": 0.23598098754882812, "end": **********.043888, "relative_end": **********.043888, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.049946, "relative_start": 0.24203896522521973, "end": **********.049946, "relative_end": **********.049946, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.055843, "relative_start": 0.24793601036071777, "end": **********.055843, "relative_end": **********.055843, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.062338, "relative_start": 0.25443100929260254, "end": **********.062338, "relative_end": **********.062338, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.068193, "relative_start": 0.2602858543395996, "end": **********.068193, "relative_end": **********.068193, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.074458, "relative_start": 0.26655077934265137, "end": **********.074458, "relative_end": **********.074458, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.081492, "relative_start": 0.27358484268188477, "end": **********.081492, "relative_end": **********.081492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.087496, "relative_start": 0.2795889377593994, "end": **********.087496, "relative_end": **********.087496, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.094071, "relative_start": 0.2861638069152832, "end": **********.094071, "relative_end": **********.094071, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.099652, "relative_start": 0.2917449474334717, "end": **********.099652, "relative_end": **********.099652, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.106305, "relative_start": 0.2983977794647217, "end": **********.106305, "relative_end": **********.106305, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.1123, "relative_start": 0.30439281463623047, "end": **********.1123, "relative_end": **********.1123, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.118499, "relative_start": 0.3105919361114502, "end": **********.118499, "relative_end": **********.118499, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.125308, "relative_start": 0.3174009323120117, "end": **********.125308, "relative_end": **********.125308, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.131497, "relative_start": 0.3235898017883301, "end": **********.131497, "relative_end": **********.131497, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.138874, "relative_start": 0.3309669494628906, "end": **********.138874, "relative_end": **********.138874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.145414, "relative_start": 0.3375070095062256, "end": **********.145414, "relative_end": **********.145414, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.151531, "relative_start": 0.3436238765716553, "end": **********.151531, "relative_end": **********.151531, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.157485, "relative_start": 0.3495779037475586, "end": **********.157485, "relative_end": **********.157485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.app", "start": **********.164169, "relative_start": 0.3562619686126709, "end": **********.164169, "relative_end": **********.164169, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 26990088, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 22, "nb_templates": 22, "templates": [{"name": "home", "param_count": null, "params": [], "start": **********.023665, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.phphome", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=1", "ajax": false, "filename": "home.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.036367, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.043841, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.049906, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.055806, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.062301, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.068155, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.07442, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.081448, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.087458, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.094034, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.099613, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.106264, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.11226, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.118447, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.125271, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.131461, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.138838, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.145377, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.151495, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.157448, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "layouts.app", "param_count": null, "params": [], "start": **********.164132, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}]}, "queries": {"count": 75, "nb_statements": 75, "nb_visible_statements": 75, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.04039, "accumulated_duration_str": "40.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'ysQ2OZdBk91GzyxsvUNjStsqoUKCd3cWniDGhfUE' limit 1", "type": "query", "params": [], "bindings": ["ysQ2OZdBk91GzyxsvUNjStsqoUKCd3cWniDGhfUE"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.963548, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "instapwa", "explain": null, "start_percent": 0, "width_percent": 5.125}, {"sql": "select * from `users` where `id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.977725, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "instapwa", "explain": null, "start_percent": 5.125, "width_percent": 1.312}, {"sql": "select * from `live_streams` where `is_live` = 1 and `visibility` = 'public' order by `started_at` desc limit 10", "type": "query", "params": [], "bindings": [1, "public"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 21}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.983563, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:21", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=21", "ajax": false, "filename": "HomeController.php", "line": "21"}, "connection": "instapwa", "explain": null, "start_percent": 6.437, "width_percent": 1.337}, {"sql": "select * from `posts` where `user_id` in (select `followed_id` from `followers` where `follower_id` = 28 and `approved` = 1) or `user_id` = 28 or `visibility` = 'public' order by `created_at` desc limit 20", "type": "query", "params": [], "bindings": [28, 1, 28, "public"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.9866078, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:35", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=35", "ajax": false, "filename": "HomeController.php", "line": "35"}, "connection": "instapwa", "explain": null, "start_percent": 7.774, "width_percent": 2.377}, {"sql": "select * from `users` where `users`.`id` in (1, 3, 4, 6, 7, 8, 27)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.990613, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:35", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=35", "ajax": false, "filename": "HomeController.php", "line": "35"}, "connection": "instapwa", "explain": null, "start_percent": 10.151, "width_percent": 1.733}, {"sql": "select * from `user_profiles` where `user_profiles`.`user_id` in (1, 3, 4, 6, 7, 8, 27)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.995567, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:35", "source": {"index": 25, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=35", "ajax": false, "filename": "HomeController.php", "line": "35"}, "connection": "instapwa", "explain": null, "start_percent": 11.884, "width_percent": 1.164}, {"sql": "select * from `love` where `love`.`post_id` in (28, 29, 30, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.997978, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:35", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=35", "ajax": false, "filename": "HomeController.php", "line": "35"}, "connection": "instapwa", "explain": null, "start_percent": 13.048, "width_percent": 0.768}, {"sql": "select * from `users` where `id` != 28 and `id` not in (select `followed_id` from `followers` where `follower_id` = 28) order by RAND() limit 5", "type": "query", "params": [], "bindings": [28, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 47}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.999795, "duration": 0.009460000000000001, "duration_str": "9.46ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "instapwa", "explain": null, "start_percent": 13.815, "width_percent": 23.422}, {"sql": "select * from `user_profiles` where `user_profiles`.`user_id` in (4, 6, 9, 21, 23)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.011512, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "instapwa", "explain": null, "start_percent": 37.237, "width_percent": 0.99}, {"sql": "select * from `live_streams` where `has_recording` = 1 and `recording_path` is not null and `visibility` = 'public' and `ended_at` > '2025-07-21 21:25:59' order by `ended_at` desc limit 8", "type": "query", "params": [], "bindings": [1, "public", "2025-07-21 21:25:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 57}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.0134492, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:57", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=57", "ajax": false, "filename": "HomeController.php", "line": "57"}, "connection": "instapwa", "explain": null, "start_percent": 38.227, "width_percent": 1.139}, {"sql": "select * from `users` where `users`.`id` in (27)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 57}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.0158112, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:57", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=57", "ajax": false, "filename": "HomeController.php", "line": "57"}, "connection": "instapwa", "explain": null, "start_percent": 39.366, "width_percent": 1.089}, {"sql": "select * from `user_profiles` where `user_profiles`.`user_id` = 28 and `user_profiles`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 508}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.024343, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "home:508", "source": {"index": 21, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 508}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=508", "ajax": false, "filename": "home.blade.php", "line": "508"}, "connection": "instapwa", "explain": null, "start_percent": 40.456, "width_percent": 1.213}, {"sql": "select * from `user_profiles` where `user_profiles`.`user_id` = 27 and `user_profiles`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 566}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.03333, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "home:566", "source": {"index": 21, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 566}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=566", "ajax": false, "filename": "home.blade.php", "line": "566"}, "connection": "instapwa", "explain": null, "start_percent": 41.669, "width_percent": 1.238}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 70 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [70, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.036882, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 42.907, "width_percent": 0.99}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 70 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [70, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.038881, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 43.897, "width_percent": 1.089}, {"sql": "select * from `comments` where `comments`.`post_id` = 70 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [70], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.0414078, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 44.986, "width_percent": 1.04}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 69 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [69, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.044269, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 46.026, "width_percent": 1.164}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 69 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [69, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.046243, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 47.19, "width_percent": 0.768}, {"sql": "select * from `comments` where `comments`.`post_id` = 69 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [69], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.0479438, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 47.957, "width_percent": 0.619}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 68 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [68, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.050311, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 48.576, "width_percent": 1.114}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 68 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [68, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.052264, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 49.691, "width_percent": 0.817}, {"sql": "select * from `comments` where `comments`.`post_id` = 68 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [68], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.053999, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 50.508, "width_percent": 0.768}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 67 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [67, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.0563629, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 51.275, "width_percent": 1.065}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 67 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [67, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.058727, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 52.34, "width_percent": 0.99}, {"sql": "select * from `comments` where `comments`.`post_id` = 67 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [67], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.060658, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 53.33, "width_percent": 0.545}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 66 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [66, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.062666, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 53.875, "width_percent": 0.545}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 66 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [66, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.064633, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 54.419, "width_percent": 0.916}, {"sql": "select * from `comments` where `comments`.`post_id` = 66 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [66], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.06647, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 55.335, "width_percent": 0.495}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 65 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [65, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.068517, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 55.831, "width_percent": 0.495}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 65 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [65, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.070082, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 56.326, "width_percent": 0.842}, {"sql": "select * from `comments` where `comments`.`post_id` = 65 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.072485, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 57.168, "width_percent": 1.04}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 64 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [64, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.07481, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 58.207, "width_percent": 0.99}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 64 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [64, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.076684, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 59.198, "width_percent": 1.065}, {"sql": "select * from `comments` where `comments`.`post_id` = 64 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [64], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.0794332, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 60.262, "width_percent": 1.139}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 63 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [63, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.081848, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 61.401, "width_percent": 0.941}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 63 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [63, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.083587, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 62.342, "width_percent": 0.792}, {"sql": "select * from `comments` where `comments`.`post_id` = 63 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [63], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.085528, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 63.134, "width_percent": 0.891}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 62 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [62, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.087883, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 64.026, "width_percent": 0.941}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 62 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [62, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.089852, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 64.967, "width_percent": 0.768}, {"sql": "select * from `comments` where `comments`.`post_id` = 62 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [62], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.091984, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 65.734, "width_percent": 1.263}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 61 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [61, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.0943868, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 66.997, "width_percent": 0.619}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 61 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [61, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.0960689, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 67.616, "width_percent": 0.495}, {"sql": "select * from `comments` where `comments`.`post_id` = 61 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [61], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.097581, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 68.111, "width_percent": 0.47}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 60 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [60, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.099998, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 68.581, "width_percent": 0.867}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 60 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [60, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.1019368, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 69.448, "width_percent": 0.99}, {"sql": "select * from `comments` where `comments`.`post_id` = 60 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [60], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.103922, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 70.438, "width_percent": 1.065}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 59 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [59, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.106698, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 71.503, "width_percent": 1.114}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 59 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [59, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.10862, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 72.617, "width_percent": 0.594}, {"sql": "select * from `comments` where `comments`.`post_id` = 59 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [59], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.1102679, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 73.211, "width_percent": 0.569}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 58 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [58, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.112745, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 73.781, "width_percent": 1.238}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 58 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [58, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.114882, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 75.019, "width_percent": 0.817}, {"sql": "select * from `comments` where `comments`.`post_id` = 58 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [58], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.1166668, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 75.836, "width_percent": 0.644}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 57 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [57, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.118906, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 76.479, "width_percent": 1.139}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 57 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [57, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.121232, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 77.618, "width_percent": 1.164}, {"sql": "select * from `comments` where `comments`.`post_id` = 57 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [57], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.123355, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 78.782, "width_percent": 1.065}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 56 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [56, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.1256778, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 79.846, "width_percent": 1.015}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 56 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [56, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.127756, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 80.862, "width_percent": 1.089}, {"sql": "select * from `comments` where `comments`.`post_id` = 56 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [56], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.129641, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 81.951, "width_percent": 0.867}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 55 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [55, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.1318002, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 82.818, "width_percent": 0.768}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 55 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [55, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.134525, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 83.585, "width_percent": 1.114}, {"sql": "select * from `comments` where `comments`.`post_id` = 55 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [55], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.136852, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 84.699, "width_percent": 1.287}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 54 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [54, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.139208, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 85.987, "width_percent": 0.941}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 54 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [54, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.141505, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 86.927, "width_percent": 1.263}, {"sql": "select * from `comments` where `comments`.`post_id` = 54 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [54], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.143578, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 88.19, "width_percent": 0.867}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 28 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [28, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.1457841, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 89.057, "width_percent": 0.718}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 28 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [28, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.147744, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 89.775, "width_percent": 0.966}, {"sql": "select * from `comments` where `comments`.`post_id` = 28 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.149761, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 90.74, "width_percent": 0.867}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 29 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [29, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.151832, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 91.607, "width_percent": 0.743}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 29 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [29, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.1534948, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 92.35, "width_percent": 1.015}, {"sql": "select * from `comments` where `comments`.`post_id` = 29 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.1555212, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 93.365, "width_percent": 1.164}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 30 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [30, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.1578, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 94.528, "width_percent": 0.817}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 30 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [30, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.1596, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 95.345, "width_percent": 0.891}, {"sql": "select * from `comments` where `comments`.`post_id` = 30 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.1616762, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 96.237, "width_percent": 1.114}, {"sql": "select count(*) as aggregate from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = 28 and `notifications`.`notifiable_id` is not null and `read_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\User", 28], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "layouts.app", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/layouts/app.blade.php", "line": 382}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.16725, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "layouts.app:382", "source": {"index": 19, "namespace": "view", "name": "layouts.app", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/layouts/app.blade.php", "line": 382}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=382", "ajax": false, "filename": "app.blade.php", "line": "382"}, "connection": "instapwa", "explain": null, "start_percent": 97.351, "width_percent": 1.337}, {"sql": "select * from `love_wallets` where `love_wallets`.`user_id` = 28 and `love_wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "layouts.app", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/layouts/app.blade.php", "line": 413}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.169562, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "layouts.app:413", "source": {"index": 21, "namespace": "view", "name": "layouts.app", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/layouts/app.blade.php", "line": 413}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=413", "ajax": false, "filename": "app.blade.php", "line": "413"}, "connection": "instapwa", "explain": null, "start_percent": 98.688, "width_percent": 1.312}]}, "models": {"data": {"App\\Models\\Post": {"value": 20, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=1", "ajax": false, "filename": "Post.php", "line": "?"}}, "App\\Models\\User": {"value": 14, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\UserProfile": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FUserProfile.php&line=1", "ajax": false, "filename": "UserProfile.php", "line": "?"}}, "App\\Models\\LiveStream": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FLiveStream.php&line=1", "ajax": false, "filename": "LiveStream.php", "line": "?"}}}, "count": 43, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/home", "action_name": "home", "controller_action": "App\\Http\\Controllers\\HomeController@index", "uri": "GET home", "controller": "App\\Http\\Controllers\\HomeController@index<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=13\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=13\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/HomeController.php:13-60</a>", "middleware": "web, auth", "duration": "376ms", "peak_memory": "28MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1247458530 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1247458530\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1390955799 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1390955799\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1841696326 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Android&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"131 characters\">Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Mobile Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Im01R3hyRTB3VmxiNVVxOWw5a042NEE9PSIsInZhbHVlIjoiOEVFM2dBL0h3eE5xa08zN0NPMGlHbERmejVydVhrVW5IVkZoaGx5eW9iMXFsd2dTS2JhKzNhb0lDMllqZmJLRm5kek1KQ0NsUUI3ckhmMXBReVlBeElSZUp0RDhrbHhVVGlxWDgzbFR0bE5UUUc5V3p4N3NlYkZvSFQwQlhFWWsiLCJtYWMiOiI4ZGM5MGFlM2ZlNjFhYjg3ZmZkMDA1MzJhMzY1NGVhZjY5MzIwMDA5NjAwZDRkNWZkODM4OTU1ZTRjY2MwY2VjIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlFJRmVRQ0RIZmRyR1Zhc3VsVER1Unc9PSIsInZhbHVlIjoiOUt3T1VvR2tFaVB1L0hMLytQRC9oS3BLV3pLbXB2KzFSMUxSemRjT1RqQi9FN3FWb0lnSkFReE03d01Wb3hpb282aUxLOUp0SG1rYkxnRndjTmV3SU85MEdjVHBqeFk4eGZrRFA4N2JKTHhuQWxHY1BSRnVRNElMZk9VY0VmSjAiLCJtYWMiOiJkNmVhZTU1ODJjZWMzYmRjNDlhZDE1ZGI3Njk3MTMyYzk5MjJlZjY3YjkxYWJmNzQwNDEwZmU2OWJiOWFlZjRjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1841696326\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-953004284 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HKkQb009ur9juY1euzSJdZerzJQUHXdiHWRWdESt</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ysQ2OZdBk91GzyxsvUNjStsqoUKCd3cWniDGhfUE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-953004284\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-709915017 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 22 Jul 2025 21:25:59 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-709915017\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1414421883 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HKkQb009ur9juY1euzSJdZerzJQUHXdiHWRWdESt</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>28</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1414421883\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/home", "action_name": "home", "controller_action": "App\\Http\\Controllers\\HomeController@index"}, "badge": null}}