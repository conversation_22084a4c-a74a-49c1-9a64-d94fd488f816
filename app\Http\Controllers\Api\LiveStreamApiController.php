<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\LiveStream;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LiveStreamApiController extends Controller
{
    /**
     * Get active live streams
     */
    public function getActiveLiveStreams()
    {
        $streams = LiveStream::with(['user.profile'])
            ->where('is_live', true)
            ->where('visibility', 'public')
            ->orderBy('viewer_count', 'desc')
            ->orderBy('started_at', 'desc')
            ->get()
            ->map(function ($stream) {
                return [
                    'id' => $stream->id,
                    'title' => $stream->title,
                    'description' => $stream->description,
                    'viewer_count' => $stream->viewer_count,
                    'started_at' => $stream->started_at->diffForHumans(),
                    'user' => [
                        'id' => $stream->user->id,
                        'name' => $stream->user->name,
                        'username' => $stream->user->username,
                        'avatar' => $stream->user->profile && $stream->user->profile->photo 
                            ? asset('storage/' . $stream->user->profile->photo) 
                            : asset('images/default.png')
                    ]
                ];
            });

        return response()->json([
            'success' => true,
            'streams' => $streams
        ]);
    }

    /**
     * Get recent recorded live streams
     */
    public function getRecentRecordedStreams(Request $request)
    {
        $limit = $request->get('limit', 10);
        $hours = $request->get('hours', 168); // Default last 7 days

        $streams = LiveStream::with(['user.profile'])
            ->where('has_recording', true)
            ->whereNotNull('recording_path')
            ->where('visibility', 'public')
            ->where('ended_at', '>', now()->subHours($hours))
            ->orderBy('ended_at', 'desc')
            ->take($limit)
            ->get()
            ->map(function ($stream) {
                return [
                    'id' => $stream->id,
                    'title' => $stream->title,
                    'description' => $stream->description,
                    'duration' => $stream->formatted_duration,
                    'ended_at' => $stream->ended_at->diffForHumans(),
                    'thumbnail_url' => $stream->thumbnail_url,
                    'recording_url' => $stream->recording_url,
                    'user' => [
                        'id' => $stream->user->id,
                        'name' => $stream->user->name,
                        'username' => $stream->user->username,
                        'avatar' => $stream->user->profile && $stream->user->profile->photo 
                            ? asset('storage/' . $stream->user->profile->photo) 
                            : asset('images/default.png')
                    ]
                ];
            });

        return response()->json([
            'success' => true,
            'streams' => $streams
        ]);
    }

    /**
     * Get user's live streams (both active and recorded)
     */
    public function getUserStreams(Request $request, $userId)
    {
        $user = User::findOrFail($userId);
        $limit = $request->get('limit', 20);

        // Current live stream
        $currentLiveStream = $user->liveStreams()
            ->where('is_live', true)
            ->first();

        // Recent recorded streams
        $recentStreams = $user->liveStreams()
            ->where('has_recording', true)
            ->whereNotNull('recording_path')
            ->orderBy('ended_at', 'desc')
            ->take($limit)
            ->get()
            ->map(function ($stream) {
                return [
                    'id' => $stream->id,
                    'title' => $stream->title,
                    'duration' => $stream->formatted_duration,
                    'ended_at' => $stream->ended_at->diffForHumans(),
                    'thumbnail_url' => $stream->thumbnail_url,
                    'recording_url' => $stream->recording_url,
                    'is_live' => $stream->is_live
                ];
            });

        return response()->json([
            'success' => true,
            'current_live_stream' => $currentLiveStream ? [
                'id' => $currentLiveStream->id,
                'title' => $currentLiveStream->title,
                'viewer_count' => $currentLiveStream->viewer_count,
                'started_at' => $currentLiveStream->started_at->diffForHumans()
            ] : null,
            'recent_streams' => $recentStreams,
            'total_streams' => $user->liveStreams()->count()
        ]);
    }

    /**
     * Search live streams
     */
    public function searchStreams(Request $request)
    {
        $query = $request->get('q', '');
        $type = $request->get('type', 'all'); // 'live', 'recorded', 'all'

        $streamsQuery = LiveStream::with(['user.profile'])
            ->where('visibility', 'public');

        if ($query) {
            $streamsQuery->where(function ($q) use ($query) {
                $q->where('title', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%")
                  ->orWhereHas('user', function ($userQuery) use ($query) {
                      $userQuery->where('username', 'like', "%{$query}%")
                               ->orWhere('name', 'like', "%{$query}%");
                  });
            });
        }

        if ($type === 'live') {
            $streamsQuery->where('is_live', true);
        } elseif ($type === 'recorded') {
            $streamsQuery->where('has_recording', true)
                        ->whereNotNull('recording_path');
        }

        $streams = $streamsQuery->orderBy('created_at', 'desc')
            ->take(50)
            ->get()
            ->map(function ($stream) {
                return [
                    'id' => $stream->id,
                    'title' => $stream->title,
                    'description' => $stream->description,
                    'is_live' => $stream->is_live,
                    'viewer_count' => $stream->viewer_count,
                    'duration' => $stream->formatted_duration,
                    'thumbnail_url' => $stream->thumbnail_url,
                    'recording_url' => $stream->recording_url,
                    'started_at' => $stream->started_at->diffForHumans(),
                    'user' => [
                        'id' => $stream->user->id,
                        'name' => $stream->user->name,
                        'username' => $stream->user->username,
                        'avatar' => $stream->user->profile && $stream->user->profile->photo 
                            ? asset('storage/' . $stream->user->profile->photo) 
                            : asset('images/default.png')
                    ]
                ];
            });

        return response()->json([
            'success' => true,
            'streams' => $streams,
            'query' => $query,
            'type' => $type
        ]);
    }
} 