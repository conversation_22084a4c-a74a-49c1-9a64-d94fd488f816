// Real-time viewer count
Echo.channel(`stream.${streamId}`)
    .listen('ViewerCountUpdated', (data) => {
        document.getElementById('viewerCount').textContent = data.count;
    });

// Handle reactions
document.querySelectorAll('.heart-btn').forEach(btn => {
    btn.addEventListener('click', () => {
        btn.classList.add('animate-heart');
        setTimeout(() => btn.classList.remove('animate-heart'), 800);
        axios.post(`/stream/${streamId}/react`, { type: 'heart' });
    });
});

// Chat functionality
const commentInput = document.querySelector('.comment-input input');
commentInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
        axios.post(`/stream/${streamId}/comment`, {
            content: commentInput.value
        });
        commentInput.value = '';
    }
});

Echo.channel(`stream.${streamId}.comments`)
    .listen('NewComment', (comment) => {
        const commentsContainer = document.getElementById('commentsContainer');
        const commentElement = document.createElement('div');
        commentElement.className = 'comment';
        commentElement.innerHTML = `
            <strong>${comment.user.name}</strong>: ${comment.content}
        `;
        commentsContainer.appendChild(commentElement);
    }); 