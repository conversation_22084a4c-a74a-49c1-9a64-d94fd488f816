<div class="love-container" id="love-container-{{ $post->id }}" data-post-id="{{ $post->id }}">
    <button class="love-button {{ $post->isLovedByUser(Auth::id()) ? 'loved' : '' }}" id="love-button-{{ $post->id }}" data-post-id="{{ $post->id }}">
        <i class="fas fa-heart"></i>
    </button>
    <div class="love-count" id="love-count-{{ $post->id }}">{{ $post->loves->count() }}</div>
    <div class="love-value" id="love-value-{{ $post->id }}">${{ number_format($post->loves->count() * 0.01, 2) }}</div>
    <div class="love-message" id="love-message-{{ $post->id }}" style="display: {{ $post->isLovedByUser(Auth::id()) ? 'block' : 'none' }}; color: #ff0066; font-size: 12px; margin-left: 5px;">
        You loved this
    </div>
</div>

<style>
    .love-container {
        display: flex;
        align-items: center;
        gap: 8px;
        margin: 15px 0;
    }
    
    .love-button {
        background: none;
        border: none;
        color: #666;
        font-size: 24px;
        cursor: pointer;
        transition: transform 0.3s ease;
        padding: 0;
    }
    
    .love-button.loved {
        color: #ff0066;
    }
    
    .love-button:hover {
        transform: scale(1.2);
    }
    
    .love-button:active {
        transform: scale(0.9);
    }
    
    .love-count {
        font-weight: 500;
    }
    
    .love-value {
        color: #FFD700;
        font-size: 14px;
    }
</style>

<script>
    // Define showBuyLovesModal if it doesn't exist yet
    if (typeof showBuyLovesModal !== 'function') {
        window.showBuyLovesModal = function(postId = null) {
            // Store the post ID for later use
            window.lastLovedPostId = postId;
            
            // Check if modal exists, if not create a simple alert
            const modal = document.getElementById('buyLovesModal');
            if (modal) {
                modal.style.display = 'flex';
            } else {
                alert('You need more loves to perform this action. Please visit the loves page to purchase more.');
                window.location.href = '/loves/buy';
            }
        };
    }

    document.addEventListener('DOMContentLoaded', function() {
        const postId = {{ $post->id }};
        const loveButton = document.getElementById(`love-button-${postId}`);
        const loveCount = document.getElementById(`love-count-${postId}`);
        const loveValue = document.getElementById(`love-value-${postId}`);
        const loveMessage = document.getElementById(`love-message-${postId}`);
        
        if (loveButton) {
            loveButton.addEventListener('click', function() {
                fetch(`/posts/${postId}/love`, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                        'Accept': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (data.loved) {
                            loveButton.classList.add('loved');
                            loveMessage.style.display = 'block';
                        } else {
                            loveButton.classList.remove('loved');
                            loveMessage.style.display = 'none';
                        }
                        
                        loveCount.textContent = data.loveCount;
                        
                        // Safely format the love value
                        const loveValueFormatted = typeof data.loveValue === 'number' 
                            ? '$' + data.loveValue.toFixed(2) 
                            : '$' + parseFloat(data.loveCount * 0.01).toFixed(2);
                            
                        loveValue.textContent = loveValueFormatted;
                    } else if (data.needsLoves) {
                        showBuyLovesModal(postId);
                    } else if (data.message) {
                        alert(data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while processing your love.');
                });
            });
        }
    });

    document.querySelectorAll('.love-button').forEach(button => {
        button.addEventListener('click', function() {
            const postId = this.dataset.postId;
            
            fetch(`/posts/${postId}/love`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.classList.toggle('loved');
                    this.nextElementSibling.textContent = data.loveCount;
                } else if (data.needsLoves) {
                    showBuyLovesModal(postId);
                }
            });
        });
    });
</script> 