<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;

class AuthController extends Controller
{
    public function showEmailSignup()
    {
        return view('auth.signup');
    }

    public function emailSignup(Request $request)
    {
        $request->validate([
            'email' => 'required|email|unique:users',
            'password' => 'required|min:6|confirmed',
        ]);

        try {
            $user = User::create([
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'signup_method' => 'email'
            ]);

            Auth::login($user);
            
            return redirect()->route('setup.username');
        } catch (\Exception $e) {
            return back()
                ->withErrors(['email' => 'Error creating account. Please try again.'])
                ->withInput($request->except('password', 'password_confirmation'));
        }
    }

    public function showLogin()
    {
        return view('auth.login');
    }

    public function login(Request $request)
    {
        $credentials = $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        if (Auth::attempt($credentials)) {
            $request->session()->regenerate();
            
            // Check if user has completed onboarding
            $user = Auth::user();
            if (!$user->onboarding_completed) {
                return redirect()->route('onboarding');
            }
            
            return redirect()->route('home');
        }

        return back()
            ->withErrors(['email' => 'The provided credentials do not match our records.'])
            ->withInput($request->except('password'));
    }

    public function googleSignup()
    {
        // For demonstration, creating a sample user
        // In production, you'd implement Google OAuth
        $user = User::create([
            'name' => 'Google User',
            'email' => 'google_' . time() . '@example.com',
            'password' => Hash::make(uniqid()),
            'signup_method' => 'google'
        ]);

        Auth::login($user);

        return redirect()->route('setup.username');
    }

    public function phoneSignup(Request $request)
    {
        $request->validate([
            'phone' => 'required',
        ]);

        // Store phone in session for OTP verification
        Session::put('phone_signup', $request->phone);
        
        // Generate a simple 4-digit OTP for demo purposes
        $otp = rand(1000, 9999);
        Session::put('phone_otp', $otp);
        
        // In a real app, you would send this OTP via SMS
        // For demonstration, we'll just return it
        return response()->json([
            'message' => 'OTP sent to your phone',
            'otp' => $otp // Remove this in production
        ]);
    }

    public function verifyOTP(Request $request)
    {
        $request->validate([
            'otp' => 'required|numeric',
        ]);

        $storedOTP = Session::get('phone_otp');
        $phone = Session::get('phone_signup');

        if ($request->otp == $storedOTP) {
            $user = User::create([
                'phone' => $phone,
                'password' => Hash::make(uniqid()),
                'signup_method' => 'phone'
            ]);

            Auth::login($user);
            
            Session::forget(['phone_signup', 'phone_otp']);
            
            return redirect()->route('setup.username');
        }

        return back()->withErrors([
            'otp' => 'Invalid OTP entered.',
        ]);
    }

    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect('/');
    }

    public function showForgotPassword()
    {
        return view('auth.forgot-password');
    }

    public function sendResetLink(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
        ]);

        $status = Password::sendResetLink(
            $request->only('email')
        );

        return $status === Password::RESET_LINK_SENT
            ? back()->with(['status' => __($status)])
            : back()->withErrors(['email' => __($status)]);
    }

    public function showResetPassword($token)
    {
        return view('auth.reset-password', ['token' => $token]);
    }

    public function resetPassword(Request $request)
    {
        $request->validate([
            'token' => 'required',
            'email' => 'required|email',
            'password' => 'required|min:6|confirmed',
        ]);

        $status = Password::reset(
            $request->only('email', 'password', 'password_confirmation', 'token'),
            function ($user, $password) {
                $user->forceFill([
                    'password' => Hash::make($password),
                    'remember_token' => Str::random(60),
                ])->save();
            }
        );

        return $status === Password::PASSWORD_RESET
            ? redirect()->route('login')->with('status', __($status))
            : back()->withErrors(['email' => __($status)]);
    }
} 