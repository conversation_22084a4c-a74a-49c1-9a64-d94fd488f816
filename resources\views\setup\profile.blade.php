<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Profile - InstaPWA</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            background-color: #d31414;
            color: white;
            min-height: 100vh;
        }

        .container {
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .logo {
            width: 150px;
            margin: 40px 0;
        }

        .title {
            font-size: 24px;
            margin-bottom: 30px;
        }

        form {
            width: 100%;
            max-width: 400px;
        }

        .input-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
        }

        input, textarea {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            background: rgba(0, 0, 0, 0.3);
            color: white;
            font-size: 16px;
            box-sizing: border-box;
        }

        textarea {
            height: 120px;
            resize: none;
        }

        .next-btn {
            background-color: #FFD700;
            color: black;
            border: none;
            padding: 15px 40px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <img src="/images/logo.png" alt="InstaPWA Logo" class="logo">
        
        <div class="title">Profile</div>

        <form action="{{ route('setup.profile') }}" method="POST">
            @csrf
            <div class="input-group">
                <label>Full name</label>
                <input type="text" name="full_name" required value="{{ old('full_name') }}" placeholder="Enter full name">
            </div>

            <div class="input-group">
                <label>Bio</label>
                <textarea name="bio" placeholder="Display a short bio">{{ old('bio') }}</textarea>
            </div>

            <button type="submit" class="next-btn">Continue</button>
        </form>
    </div>
</body>
</html> 