<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class StreamEndedEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function broadcastOn()
    {
        return [
            new Channel('live-streams'),
            new Channel('livestream.' . $this->data['stream_id'])
        ];
    }

    public function broadcastAs()
    {
        return 'stream.ended';
    }

    public function broadcastWith()
    {
        return $this->data;
    }
} 