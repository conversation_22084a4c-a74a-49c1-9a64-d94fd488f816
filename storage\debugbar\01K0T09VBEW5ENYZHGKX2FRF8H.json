{"__meta": {"id": "01K0T09VBEW5ENYZHGKX2FRF8H", "datetime": "2025-07-22 21:23:14", "utime": **********.927124, "method": "GET", "uri": "/home", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.484648, "end": **********.927135, "duration": 0.4424870014190674, "duration_str": "442ms", "measures": [{"label": "Booting", "start": **********.484648, "relative_start": 0, "end": **********.63023, "relative_end": **********.63023, "duration": 0.*****************, "duration_str": "146ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.630238, "relative_start": 0.*****************, "end": **********.927136, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "297ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.645821, "relative_start": 0.*****************, "end": **********.648253, "relative_end": **********.648253, "duration": 0.0024318695068359375, "duration_str": "2.43ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.74894, "relative_start": 0.*****************, "end": **********.925655, "relative_end": **********.925655, "duration": 0.*****************, "duration_str": "177ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: home", "start": **********.750614, "relative_start": 0.*****************, "end": **********.750614, "relative_end": **********.750614, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.770414, "relative_start": 0.****************, "end": **********.770414, "relative_end": **********.770414, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.783444, "relative_start": 0.2987959384918213, "end": **********.783444, "relative_end": **********.783444, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.78972, "relative_start": 0.3050720691680908, "end": **********.78972, "relative_end": **********.78972, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.795626, "relative_start": 0.3109779357910156, "end": **********.795626, "relative_end": **********.795626, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.801255, "relative_start": 0.3166069984436035, "end": **********.801255, "relative_end": **********.801255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.807844, "relative_start": 0.3231959342956543, "end": **********.807844, "relative_end": **********.807844, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.813164, "relative_start": 0.32851600646972656, "end": **********.813164, "relative_end": **********.813164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.818871, "relative_start": 0.33422303199768066, "end": **********.818871, "relative_end": **********.818871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.824806, "relative_start": 0.34015798568725586, "end": **********.824806, "relative_end": **********.824806, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.831344, "relative_start": 0.3466958999633789, "end": **********.831344, "relative_end": **********.831344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.839327, "relative_start": 0.3546791076660156, "end": **********.839327, "relative_end": **********.839327, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.853087, "relative_start": 0.3684389591217041, "end": **********.853087, "relative_end": **********.853087, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.860407, "relative_start": 0.3757591247558594, "end": **********.860407, "relative_end": **********.860407, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.868135, "relative_start": 0.3834869861602783, "end": **********.868135, "relative_end": **********.868135, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.876448, "relative_start": 0.3917999267578125, "end": **********.876448, "relative_end": **********.876448, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.883822, "relative_start": 0.3991739749908447, "end": **********.883822, "relative_end": **********.883822, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.891114, "relative_start": 0.406466007232666, "end": **********.891114, "relative_end": **********.891114, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.897693, "relative_start": 0.41304492950439453, "end": **********.897693, "relative_end": **********.897693, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.904361, "relative_start": 0.41971302032470703, "end": **********.904361, "relative_end": **********.904361, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.910652, "relative_start": 0.42600393295288086, "end": **********.910652, "relative_end": **********.910652, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.app", "start": **********.918159, "relative_start": 0.43351101875305176, "end": **********.918159, "relative_end": **********.918159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 26987928, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 22, "nb_templates": 22, "templates": [{"name": "home", "param_count": null, "params": [], "start": **********.750571, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.phphome", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=1", "ajax": false, "filename": "home.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.770371, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.783396, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.789681, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.795586, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.801215, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.807806, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.813127, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.818832, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.824766, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.831305, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.839267, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.853048, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.86037, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.868081, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.876394, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.883783, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.891067, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.897645, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.904321, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.9106, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "layouts.app", "param_count": null, "params": [], "start": **********.918097, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}]}, "queries": {"count": 75, "nb_statements": 75, "nb_visible_statements": 75, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.08128000000000002, "accumulated_duration_str": "81.28ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'ysQ2OZdBk91GzyxsvUNjStsqoUKCd3cWniDGhfUE' limit 1", "type": "query", "params": [], "bindings": ["ysQ2OZdBk91GzyxsvUNjStsqoUKCd3cWniDGhfUE"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.657834, "duration": 0.02357, "duration_str": "23.57ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "instapwa", "explain": null, "start_percent": 0, "width_percent": 28.999}, {"sql": "select * from `users` where `id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.696041, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "instapwa", "explain": null, "start_percent": 28.999, "width_percent": 0.554}, {"sql": "select * from `live_streams` where `is_live` = 1 and `visibility` = 'public' order by `started_at` desc limit 10", "type": "query", "params": [], "bindings": [1, "public"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 21}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.700912, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:21", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=21", "ajax": false, "filename": "HomeController.php", "line": "21"}, "connection": "instapwa", "explain": null, "start_percent": 29.552, "width_percent": 0.591}, {"sql": "select * from `posts` where `user_id` in (select `followed_id` from `followers` where `follower_id` = 28 and `approved` = 1) or `user_id` = 28 or `visibility` = 'public' order by `created_at` desc limit 20", "type": "query", "params": [], "bindings": [28, 1, 28, "public"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.704659, "duration": 0.005059999999999999, "duration_str": "5.06ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:35", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=35", "ajax": false, "filename": "HomeController.php", "line": "35"}, "connection": "instapwa", "explain": null, "start_percent": 30.143, "width_percent": 6.225}, {"sql": "select * from `users` where `users`.`id` in (1, 3, 4, 6, 7, 8, 27)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.7134109, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:35", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=35", "ajax": false, "filename": "HomeController.php", "line": "35"}, "connection": "instapwa", "explain": null, "start_percent": 36.368, "width_percent": 0.701}, {"sql": "select * from `user_profiles` where `user_profiles`.`user_id` in (1, 3, 4, 6, 7, 8, 27)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.7174559, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:35", "source": {"index": 25, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=35", "ajax": false, "filename": "HomeController.php", "line": "35"}, "connection": "instapwa", "explain": null, "start_percent": 37.069, "width_percent": 0.492}, {"sql": "select * from `love` where `love`.`post_id` in (28, 29, 30, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.720118, "duration": 0.00468, "duration_str": "4.68ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:35", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=35", "ajax": false, "filename": "HomeController.php", "line": "35"}, "connection": "instapwa", "explain": null, "start_percent": 37.562, "width_percent": 5.758}, {"sql": "select * from `users` where `id` != 28 and `id` not in (select `followed_id` from `followers` where `follower_id` = 28) order by RAND() limit 5", "type": "query", "params": [], "bindings": [28, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 47}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.726429, "duration": 0.009130000000000001, "duration_str": "9.13ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "instapwa", "explain": null, "start_percent": 43.319, "width_percent": 11.233}, {"sql": "select * from `user_profiles` where `user_profiles`.`user_id` in (1, 6, 11, 21, 23)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.737429, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "instapwa", "explain": null, "start_percent": 54.552, "width_percent": 0.726}, {"sql": "select * from `live_streams` where `has_recording` = 1 and `recording_path` is not null and `visibility` = 'public' and `ended_at` > '2025-07-21 21:23:14' order by `ended_at` desc limit 8", "type": "query", "params": [], "bindings": [1, "public", "2025-07-21 21:23:14"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 57}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.739675, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:57", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=57", "ajax": false, "filename": "HomeController.php", "line": "57"}, "connection": "instapwa", "explain": null, "start_percent": 55.278, "width_percent": 0.775}, {"sql": "select * from `users` where `users`.`id` in (27)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 57}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.741869, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:57", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=57", "ajax": false, "filename": "HomeController.php", "line": "57"}, "connection": "instapwa", "explain": null, "start_percent": 56.053, "width_percent": 0.48}, {"sql": "select * from `user_profiles` where `user_profiles`.`user_id` = 28 and `user_profiles`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 508}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.751778, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "home:508", "source": {"index": 21, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 508}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=508", "ajax": false, "filename": "home.blade.php", "line": "508"}, "connection": "instapwa", "explain": null, "start_percent": 56.533, "width_percent": 0.689}, {"sql": "select * from `user_profiles` where `user_profiles`.`user_id` = 27 and `user_profiles`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 566}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.765559, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "home:566", "source": {"index": 21, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 566}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=566", "ajax": false, "filename": "home.blade.php", "line": "566"}, "connection": "instapwa", "explain": null, "start_percent": 57.222, "width_percent": 0.75}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 70 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [70, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.771009, "duration": 0.0023, "duration_str": "2.3ms", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 57.972, "width_percent": 2.83}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 70 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [70, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.77535, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 60.802, "width_percent": 0.418}, {"sql": "select * from `comments` where `comments`.`post_id` = 70 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [70], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.777603, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 61.22, "width_percent": 4.995}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 69 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [69, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.783866, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 66.216, "width_percent": 0.529}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 69 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [69, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.785807, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 66.745, "width_percent": 0.517}, {"sql": "select * from `comments` where `comments`.`post_id` = 69 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [69], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.78769, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 67.261, "width_percent": 0.504}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 68 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [68, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.7900488, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 67.766, "width_percent": 0.517}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 68 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [68, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.7919111, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 68.282, "width_percent": 0.406}, {"sql": "select * from `comments` where `comments`.`post_id` = 68 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [68], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.793771, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 68.688, "width_percent": 0.394}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 67 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [67, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.7959628, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 69.082, "width_percent": 0.295}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 67 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [67, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.7976172, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 69.377, "width_percent": 0.332}, {"sql": "select * from `comments` where `comments`.`post_id` = 67 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [67], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.799253, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 69.71, "width_percent": 0.443}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 66 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [66, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.801656, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 70.153, "width_percent": 0.578}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 66 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [66, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.80366, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 70.731, "width_percent": 0.517}, {"sql": "select * from `comments` where `comments`.`post_id` = 66 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [66], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.8057191, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 71.248, "width_percent": 0.554}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 65 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [65, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.808183, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 71.801, "width_percent": 0.381}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 65 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [65, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.809898, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 72.183, "width_percent": 0.271}, {"sql": "select * from `comments` where `comments`.`post_id` = 65 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.811475, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 72.453, "width_percent": 0.271}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 64 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [64, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.813483, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 72.724, "width_percent": 0.443}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 64 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [64, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.815386, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 73.167, "width_percent": 0.431}, {"sql": "select * from `comments` where `comments`.`post_id` = 64 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [64], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.8171709, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 73.597, "width_percent": 0.258}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 63 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [63, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.819186, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 73.856, "width_percent": 0.271}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 63 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [63, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.821043, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 74.126, "width_percent": 0.468}, {"sql": "select * from `comments` where `comments`.`post_id` = 63 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [63], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.8229032, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 74.594, "width_percent": 0.357}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 62 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [62, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.825169, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 74.951, "width_percent": 0.431}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 62 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [62, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.8271248, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 75.381, "width_percent": 0.517}, {"sql": "select * from `comments` where `comments`.`post_id` = 62 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [62], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.829261, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 75.898, "width_percent": 0.541}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 61 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [61, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.831711, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 76.439, "width_percent": 0.566}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 61 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [61, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.833735, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 77.005, "width_percent": 0.418}, {"sql": "select * from `comments` where `comments`.`post_id` = 61 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [61], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.835951, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 77.424, "width_percent": 0.64}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 60 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [60, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.8398628, "duration": 0.00234, "duration_str": "2.34ms", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 78.063, "width_percent": 2.879}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 60 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [60, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.845462, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 80.942, "width_percent": 1.501}, {"sql": "select * from `comments` where `comments`.`post_id` = 60 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [60], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.8492389, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 82.443, "width_percent": 1.403}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 59 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [59, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.853566, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 83.846, "width_percent": 0.677}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 59 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [59, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.855882, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 84.523, "width_percent": 0.615}, {"sql": "select * from `comments` where `comments`.`post_id` = 59 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [59], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.8580751, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 85.138, "width_percent": 0.566}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 58 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [58, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.860778, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 85.704, "width_percent": 0.578}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 58 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [58, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.863331, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 86.282, "width_percent": 0.627}, {"sql": "select * from `comments` where `comments`.`post_id` = 58 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [58], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.865709, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 86.909, "width_percent": 0.566}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 57 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [57, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.868637, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 87.475, "width_percent": 0.652}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 57 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [57, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.871691, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 88.127, "width_percent": 0.677}, {"sql": "select * from `comments` where `comments`.`post_id` = 57 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [57], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.873863, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 88.804, "width_percent": 0.664}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 56 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [56, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.8769848, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 89.469, "width_percent": 0.701}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 56 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [56, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.879192, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 90.17, "width_percent": 0.554}, {"sql": "select * from `comments` where `comments`.`post_id` = 56 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [56], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.8814218, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 90.723, "width_percent": 0.529}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 55 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [55, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.884232, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 91.252, "width_percent": 0.652}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 55 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [55, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.8867, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 91.905, "width_percent": 0.64}, {"sql": "select * from `comments` where `comments`.`post_id` = 55 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [55], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.888837, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 92.544, "width_percent": 0.566}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 54 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [54, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.891508, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 93.11, "width_percent": 0.554}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 54 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [54, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.893584, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 93.664, "width_percent": 0.517}, {"sql": "select * from `comments` where `comments`.`post_id` = 54 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [54], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.89558, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 94.181, "width_percent": 0.541}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 28 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [28, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.898084, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 94.722, "width_percent": 0.701}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 28 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [28, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.900513, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 95.423, "width_percent": 0.431}, {"sql": "select * from `comments` where `comments`.`post_id` = 28 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.902475, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 95.854, "width_percent": 0.357}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 29 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [29, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.9047449, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 96.211, "width_percent": 0.664}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 29 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [29, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.906866, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 96.875, "width_percent": 0.381}, {"sql": "select * from `comments` where `comments`.`post_id` = 29 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.9086518, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 97.256, "width_percent": 0.504}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 30 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [30, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.9110858, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 97.761, "width_percent": 0.517}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 30 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [30, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.913112, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 98.278, "width_percent": 0.418}, {"sql": "select * from `comments` where `comments`.`post_id` = 30 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.915476, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 98.696, "width_percent": 0.418}, {"sql": "select count(*) as aggregate from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = 28 and `notifications`.`notifiable_id` is not null and `read_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\User", 28], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "layouts.app", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/layouts/app.blade.php", "line": 382}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.9211009, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "layouts.app:382", "source": {"index": 19, "namespace": "view", "name": "layouts.app", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/layouts/app.blade.php", "line": 382}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=382", "ajax": false, "filename": "app.blade.php", "line": "382"}, "connection": "instapwa", "explain": null, "start_percent": 99.114, "width_percent": 0.492}, {"sql": "select * from `love_wallets` where `love_wallets`.`user_id` = 28 and `love_wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "layouts.app", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/layouts/app.blade.php", "line": 413}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.923293, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "layouts.app:413", "source": {"index": 21, "namespace": "view", "name": "layouts.app", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/layouts/app.blade.php", "line": 413}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=413", "ajax": false, "filename": "app.blade.php", "line": "413"}, "connection": "instapwa", "explain": null, "start_percent": 99.606, "width_percent": 0.394}]}, "models": {"data": {"App\\Models\\Post": {"value": 20, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=1", "ajax": false, "filename": "Post.php", "line": "?"}}, "App\\Models\\User": {"value": 14, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\UserProfile": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FUserProfile.php&line=1", "ajax": false, "filename": "UserProfile.php", "line": "?"}}, "App\\Models\\LiveStream": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FLiveStream.php&line=1", "ajax": false, "filename": "LiveStream.php", "line": "?"}}}, "count": 42, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/home", "action_name": "home", "controller_action": "App\\Http\\Controllers\\HomeController@index", "uri": "GET home", "controller": "App\\Http\\Controllers\\HomeController@index<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=13\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=13\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/HomeController.php:13-60</a>", "middleware": "web, auth", "duration": "452ms", "peak_memory": "28MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-653542755 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-653542755\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-545616 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-545616\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-104161556 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Android&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"131 characters\">Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Mobile Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost:8000/chats</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkdFNTMvK056ZjAzRzlxN21OblJQSGc9PSIsInZhbHVlIjoib3VySVlGNTJRUTVaMGdIY0N5Sm5nc2dtakkzRUo0ME1kanFOVUxhcFFVdWRHcEVkNTh0OGxZdU41SExreUlhdDc2VkFZd01ONVdIZGp1NlE4bVdWQndJb2JHeHlFVTR6djExdHl5S2d3alY5RjFpUFUrMWMwcFEzUmdYS2h2K2wiLCJtYWMiOiI2MjM0YmQxOWUwZTZiMTE1M2VkY2UyZDQwNzU4ZWQyODkzYjg1N2E2YWM2ZTQ1ZTIyODUyNDEyY2FhZDljZmE1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjRUOWF2RER0a3pPd2hZeTg2ZDZLREE9PSIsInZhbHVlIjoiRmFGaVVqYVY2QStZMXltY2xPZVZzbVZnTW96Zk55SlpxVzBMcVJwVE5idFdhd2JaeGJWaUUreXB1T0FnbGFrZTVHMTVKaklSWjltaTFDVCsxRThiK1RNV0RiOUllUC9oTWMyT3dscEh2bmFySVlndU1FSWt6QU92RnVreGd4VHkiLCJtYWMiOiJmNGJiMjZhYmNiODAwNmE3ZDk2MDA3ZGE4YmE1YWI0ZWEwMzU4YWM4ZDc5MWY0ZTYwMDc5ZDhlNjI3ZjU0ZDA5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-104161556\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-289934339 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HKkQb009ur9juY1euzSJdZerzJQUHXdiHWRWdESt</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ysQ2OZdBk91GzyxsvUNjStsqoUKCd3cWniDGhfUE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-289934339\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1970548897 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 22 Jul 2025 21:23:14 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1970548897\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-151227670 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HKkQb009ur9juY1euzSJdZerzJQUHXdiHWRWdESt</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://localhost:8000/api/chat/unread-count</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>28</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-151227670\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/home", "action_name": "home", "controller_action": "App\\Http\\Controllers\\HomeController@index"}, "badge": null}}