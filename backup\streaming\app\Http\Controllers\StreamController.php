<?php

namespace App\Http\Controllers;

use App\Models\Stream;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class StreamController extends Controller
{
    public function index()
    {
        $streams = Stream::where('is_live', true)->with('user')->get();
        return view('streams.index', compact('streams'));
    }

    public function create()
    {
        return view('streams.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
        ]);

        $stream = $request->user()->streams()->create([
            'title' => $validated['title'],
            'description' => $validated['description'],
            'stream_key' => Str::random(20),
        ]);

        return redirect()->route('streams.show', $stream)
            ->with('success', 'Stream created successfully.');
    }

    public function show(Stream $stream)
    {
        return view('streams.show', compact('stream'));
    }

    public function startStream(Stream $stream)
    {
        if ($stream->user_id !== auth()->id()) {
            abort(403);
        }

        $stream->update(['is_live' => true]);
        
        return response()->json(['message' => 'Stream started successfully']);
    }

    public function stopStream(Stream $stream)
    {
        if ($stream->user_id !== auth()->id()) {
            abort(403);
        }

        $stream->update(['is_live' => false, 'viewer_count' => 0]);
        
        return response()->json(['message' => 'Stream stopped successfully']);
    }
} 