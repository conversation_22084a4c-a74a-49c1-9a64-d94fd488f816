<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('live_streams', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('title');
            $table->text('description')->nullable();
            $table->enum('visibility', ['public', 'private', 'subscribers'])->default('public');
            $table->string('stream_key')->unique();
            $table->string('stream_url')->nullable();
            $table->string('recording_path')->nullable();
            $table->string('thumbnail_path')->nullable();
            $table->boolean('has_recording')->default(false);
            $table->foreignId('post_id')->nullable()->constrained()->onDelete('set null');
            $table->integer('duration_seconds')->default(0);
            $table->boolean('is_live')->default(false);
            $table->integer('viewer_count')->default(0);
            $table->timestamp('started_at')->nullable();
            $table->timestamp('ended_at')->nullable();
            $table->string('broadcaster_socket_id')->nullable();
            $table->json('ice_servers')->nullable();
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('live_streams');
    }
}; 