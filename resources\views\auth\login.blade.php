<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - InstaPWA</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            background: #000;
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            width: 90%;
            max-width: 400px;
            background: #111;
            padding: 30px;
            border-radius: 10px;
        }

        .logo {
            width: 200px;
            margin: 0 auto 30px;
            display: block;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-control {
            width: 100%;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 5px;
            background: #222;
            color: white;
            font-size: 16px;
            box-sizing: border-box;
        }

        .form-control:focus {
            outline: none;
            border-color: #FFD700;
        }

        .btn {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin-bottom: 15px;
        }

        .btn-primary {
            background: #FFD700;
            color: black;
        }

        .btn-outline {
            background: transparent;
            border: 1px solid #FFD700;
            color: #FFD700;
        }

        .divider {
            display: flex;
            align-items: center;
            text-align: center;
            margin: 20px 0;
            color: #666;
        }

        .divider::before,
        .divider::after {
            content: '';
            flex: 1;
            border-bottom: 1px solid #333;
        }

        .divider span {
            padding: 0 10px;
        }

        .social-login {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .error-message {
            color: #ff4444;
            margin-bottom: 20px;
            text-align: center;
        }

        .forgot-password {
            color: #FFD700;
            text-decoration: none;
            text-align: center;
            display: block;
            margin-top: 15px;
        }

        .signup-link {
            text-align: center;
            margin-top: 20px;
            color: #666;
        }

        .signup-link a {
            color: #FFD700;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <img src="/images/logo.png" alt="InstaPWA" class="logo">

        @if($errors->any())
            <div class="error-message">
                {{ $errors->first() }}
            </div>
        @endif

        <form action="{{ route('login') }}" method="POST">
            @csrf
            <div class="form-group">
                <input type="text" name="email" class="form-control" placeholder="Email or Phone" value="{{ old('email') }}" required>
            </div>

            <div class="form-group">
                <input type="password" name="password" class="form-control" placeholder="Password" required>
            </div>

            <button type="submit" class="btn btn-primary">Log In</button>
        </form>

        <div class="divider">
            <span>OR</span>
        </div>

        <div class="social-login">
            <button onclick="window.location.href='{{ route('signup.google') }}'" class="btn btn-outline">
                Continue with Google
            </button>
        </div>

        <a href="{{ route('password.request') }}" class="forgot-password">Forgot Password?</a>

        <div class="signup-link">
            Don't have an account? <a href="{{ route('signup.email') }}">Sign Up</a>
        </div>
    </div>
</body>
</html> 