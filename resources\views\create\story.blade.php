<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Story - InstaPWA</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            background: #000;
            color: white;
            min-height: 100vh;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: #111;
        }

        .back-btn {
            color: white;
            text-decoration: none;
            font-size: 24px;
        }

        .title {
            font-size: 18px;
            font-weight: bold;
        }

        .post-btn {
            color: #FFD700;
            border: none;
            background: none;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
        }

        .camera-container {
            height: calc(100vh - 180px);
            position: relative;
            background: #111;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        #camera-preview {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .camera-controls {
            position: absolute;
            bottom: 20px;
            left: 0;
            right: 0;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding: 0 20px;
        }

        .control-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 24px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .capture-btn {
            width: 70px;
            height: 70px;
            border: 4px solid white;
        }

        .type-selector {
            display: flex;
            justify-content: center;
            padding: 15px;
            gap: 20px;
            background: #111;
        }

        .type-btn {
            padding: 8px 20px;
            border: none;
            border-radius: 20px;
            background: none;
            color: white;
            font-weight: bold;
            cursor: pointer;
        }

        .type-btn.active {
            background: #FFD700;
            color: black;
        }

        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 2px;
            padding: 2px;
            background: #111;
        }

        .gallery-item {
            aspect-ratio: 1;
            object-fit: cover;
            width: 100%;
            cursor: pointer;
        }

        .gallery-item.selected {
            border: 2px solid #FFD700;
        }
    </style>
</head>
<body>
    <div class="header">
        <a href="{{ route('home') }}" class="back-btn">←</a>
        <div class="title">Create Story</div>
        <button type="submit" form="storyForm" class="post-btn" id="postBtn" disabled>Next</button>
    </div>

    <div class="type-selector">
        <button class="type-btn active">Story</button>
        <button class="type-btn">Post</button>
        <button class="type-btn">Reel</button>
    </div>

    <div class="camera-container" id="cameraContainer">
        <video id="camera-preview" autoplay playsinline></video>
        <div class="camera-controls">
            <button class="control-btn" onclick="switchCamera()">⟲</button>
            <button class="control-btn capture-btn" onclick="capturePhoto()"></button>
            <button class="control-btn" onclick="document.getElementById('fileInput').click()">🖼️</button>
        </div>
    </div>

    <form id="storyForm" action="{{ route('story.store') }}" method="POST" enctype="multipart/form-data" style="display: none;">
        @csrf
        <input type="file" id="fileInput" name="media" accept="image/*,video/*" onchange="handleFileSelect(this)">
        <input type="hidden" name="type" value="image">
    </form>

    <script>
        // Camera initialization and control code similar to create.post.blade.php
        // Additional story-specific features...
    </script>
</body>
</html> 