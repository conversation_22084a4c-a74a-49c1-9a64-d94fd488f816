@extends('layouts.app')

@section('content')
<style>
    .notifications-container {
        background: #111;
        min-height: calc(100vh - 120px);
    }

    .notifications-header {
        padding: 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #222;
    }

    .date-header {
        padding: 10px 15px;
        background: #222;
        font-size: 14px;
        color: #888;
    }

    .notification-item {
        display: flex;
        padding: 15px;
        gap: 15px;
        border-bottom: 1px solid #222;
        position: relative;
    }

    .notification-avatar {
        width: 44px;
        height: 44px;
        border-radius: 50%;
        object-fit: cover;
    }

    .notification-content {
        flex: 1;
        font-size: 14px;
        line-height: 1.4;
    }

    .notification-time {
        color: #666;
        font-size: 12px;
        margin-top: 5px;
    }

    .notification-media {
        width: 44px;
        height: 44px;
        object-fit: cover;
    }

    .unread-indicator {
        width: 8px;
        height: 8px;
        background: #FFD700;
        border-radius: 50%;
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
    }

    .mark-all-read {
        color: #FFD700;
        background: none;
        border: none;
        font-size: 14px;
        cursor: pointer;
    }
</style>

<div class="notifications-container">
    <div class="notifications-header">
        <h2>Notifications</h2>
        <button class="mark-all-read" onclick="markAllAsRead()">Mark all as read</button>
    </div>

    @forelse($notifications->groupBy(function($notification) {
        return $notification->created_at->format('Y-m-d');
    }) as $date => $groupedNotifications)
        <div class="date-header">
            {{ \Carbon\Carbon::parse($date)->diffForHumans(['parts' => 1]) }}
        </div>

        @foreach($groupedNotifications as $notification)
            <div class="notification-item {{ $notification->read_at ? '' : 'unread' }}">
                @if($notification->type == 'love')
                    @php
                        $data = json_decode($notification->data);
                        $amount = $data->amount ?? 0.01;
                    @endphp
                    <div class="notification-icon love">
                        <i class="fas fa-heart"></i>
                    </div>
                    <div class="notification-content">
                        <p><strong>{{ '@' . $data->username }}</strong> loved your post and you earned ${{ number_format($amount, 2) }}</p>
                        <small>{{ $notification->created_at->diffForHumans() }}</small>
                    </div>
                @endif
                
                <!-- Other notification types -->
            </div>
        @endforeach
    @empty
        <div style="text-align: center; padding: 40px; color: #666;">
            No notifications yet
        </div>
    @endforelse
</div>

<script>
function markAsRead(id) {
    fetch(`/notifications/${id}/mark-as-read`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}',
            'Accept': 'application/json'
        }
    });
}

function markAllAsRead() {
    fetch('/notifications/mark-all-as-read', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}',
            'Accept': 'application/json'
        }
    }).then(() => {
        location.reload();
    });
}
</script>
@endsection 