{"__meta": {"id": "01K0T0ECVNHH3CG6QZ9KKQ6Y7Q", "datetime": "2025-07-22 21:25:43", "utime": **********.926119, "method": "GET", "uri": "/home", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.532463, "end": **********.926141, "duration": 0.3936779499053955, "duration_str": "394ms", "measures": [{"label": "Booting", "start": **********.532463, "relative_start": 0, "end": **********.65284, "relative_end": **********.65284, "duration": 0.****************, "duration_str": "120ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.652859, "relative_start": 0.*****************, "end": **********.926142, "relative_end": 9.5367431640625e-07, "duration": 0.****************, "duration_str": "273ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.674221, "relative_start": 0.*****************, "end": **********.677026, "relative_end": **********.677026, "duration": 0.002804994583129883, "duration_str": "2.8ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.760291, "relative_start": 0.****************, "end": **********.924509, "relative_end": **********.924509, "duration": 0.*****************, "duration_str": "164ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: home", "start": **********.761845, "relative_start": 0.*****************, "end": **********.761845, "relative_end": **********.761845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.774731, "relative_start": 0.*****************, "end": **********.774731, "relative_end": **********.774731, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.781661, "relative_start": 0.24919795989990234, "end": **********.781661, "relative_end": **********.781661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.7885, "relative_start": 0.25603699684143066, "end": **********.7885, "relative_end": **********.7885, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.794959, "relative_start": 0.2624959945678711, "end": **********.794959, "relative_end": **********.794959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.802139, "relative_start": 0.26967597007751465, "end": **********.802139, "relative_end": **********.802139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.80852, "relative_start": 0.27605700492858887, "end": **********.80852, "relative_end": **********.80852, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.815528, "relative_start": 0.2830648422241211, "end": **********.815528, "relative_end": **********.815528, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.821977, "relative_start": 0.28951382637023926, "end": **********.821977, "relative_end": **********.821977, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.829003, "relative_start": 0.2965400218963623, "end": **********.829003, "relative_end": **********.829003, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.835318, "relative_start": 0.3028550148010254, "end": **********.835318, "relative_end": **********.835318, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.841439, "relative_start": 0.3089759349822998, "end": **********.841439, "relative_end": **********.841439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.84791, "relative_start": 0.3154468536376953, "end": **********.84791, "relative_end": **********.84791, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.854662, "relative_start": 0.32219886779785156, "end": **********.854662, "relative_end": **********.854662, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.861586, "relative_start": 0.32912302017211914, "end": **********.861586, "relative_end": **********.861586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.868075, "relative_start": 0.33561182022094727, "end": **********.868075, "relative_end": **********.868075, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.876191, "relative_start": 0.34372782707214355, "end": **********.876191, "relative_end": **********.876191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.884716, "relative_start": 0.3522529602050781, "end": **********.884716, "relative_end": **********.884716, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.893, "relative_start": 0.3605368137359619, "end": **********.893, "relative_end": **********.893, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.900046, "relative_start": 0.3675830364227295, "end": **********.900046, "relative_end": **********.900046, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.907691, "relative_start": 0.3752279281616211, "end": **********.907691, "relative_end": **********.907691, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.app", "start": **********.915955, "relative_start": 0.38349199295043945, "end": **********.915955, "relative_end": **********.915955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 26990072, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 22, "nb_templates": 22, "templates": [{"name": "home", "param_count": null, "params": [], "start": **********.761803, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.phphome", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=1", "ajax": false, "filename": "home.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.774692, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.781619, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.788461, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.794921, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.80209, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.808483, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.815491, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.821936, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.828964, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.835279, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.841402, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.847871, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.854623, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.861525, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.868038, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.876141, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.884665, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.892941, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.900008, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.907654, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "layouts.app", "param_count": null, "params": [], "start": **********.91589, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}]}, "queries": {"count": 75, "nb_statements": 75, "nb_visible_statements": 75, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.06300999999999997, "accumulated_duration_str": "63.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'ysQ2OZdBk91GzyxsvUNjStsqoUKCd3cWniDGhfUE' limit 1", "type": "query", "params": [], "bindings": ["ysQ2OZdBk91GzyxsvUNjStsqoUKCd3cWniDGhfUE"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.687721, "duration": 0.01808, "duration_str": "18.08ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "instapwa", "explain": null, "start_percent": 0, "width_percent": 28.694}, {"sql": "select * from `users` where `id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.717989, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "instapwa", "explain": null, "start_percent": 28.694, "width_percent": 0.746}, {"sql": "select * from `live_streams` where `is_live` = 1 and `visibility` = 'public' order by `started_at` desc limit 10", "type": "query", "params": [], "bindings": [1, "public"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 21}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.722326, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:21", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=21", "ajax": false, "filename": "HomeController.php", "line": "21"}, "connection": "instapwa", "explain": null, "start_percent": 29.44, "width_percent": 0.762}, {"sql": "select * from `posts` where `user_id` in (select `followed_id` from `followers` where `follower_id` = 28 and `approved` = 1) or `user_id` = 28 or `visibility` = 'public' order by `created_at` desc limit 20", "type": "query", "params": [], "bindings": [28, 1, 28, "public"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.7250562, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:35", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=35", "ajax": false, "filename": "HomeController.php", "line": "35"}, "connection": "instapwa", "explain": null, "start_percent": 30.202, "width_percent": 1.047}, {"sql": "select * from `users` where `users`.`id` in (1, 3, 4, 6, 7, 8, 27)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.728681, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:35", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=35", "ajax": false, "filename": "HomeController.php", "line": "35"}, "connection": "instapwa", "explain": null, "start_percent": 31.249, "width_percent": 1.047}, {"sql": "select * from `user_profiles` where `user_profiles`.`user_id` in (1, 3, 4, 6, 7, 8, 27)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.732468, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:35", "source": {"index": 25, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=35", "ajax": false, "filename": "HomeController.php", "line": "35"}, "connection": "instapwa", "explain": null, "start_percent": 32.296, "width_percent": 0.841}, {"sql": "select * from `love` where `love`.`post_id` in (28, 29, 30, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.7351859, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:35", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=35", "ajax": false, "filename": "HomeController.php", "line": "35"}, "connection": "instapwa", "explain": null, "start_percent": 33.138, "width_percent": 0.873}, {"sql": "select * from `users` where `id` != 28 and `id` not in (select `followed_id` from `followers` where `follower_id` = 28) order by RAND() limit 5", "type": "query", "params": [], "bindings": [28, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 47}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.737477, "duration": 0.00983, "duration_str": "9.83ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "instapwa", "explain": null, "start_percent": 34.01, "width_percent": 15.601}, {"sql": "select * from `user_profiles` where `user_profiles`.`user_id` in (1, 3, 7, 15, 22)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.7491221, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "instapwa", "explain": null, "start_percent": 49.611, "width_percent": 1}, {"sql": "select * from `live_streams` where `has_recording` = 1 and `recording_path` is not null and `visibility` = 'public' and `ended_at` > '2025-07-21 21:25:43' order by `ended_at` desc limit 8", "type": "query", "params": [], "bindings": [1, "public", "2025-07-21 21:25:43"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 57}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.751581, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:57", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=57", "ajax": false, "filename": "HomeController.php", "line": "57"}, "connection": "instapwa", "explain": null, "start_percent": 50.611, "width_percent": 1.079}, {"sql": "select * from `users` where `users`.`id` in (27)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 57}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.753883, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:57", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=57", "ajax": false, "filename": "HomeController.php", "line": "57"}, "connection": "instapwa", "explain": null, "start_percent": 51.69, "width_percent": 0.603}, {"sql": "select * from `user_profiles` where `user_profiles`.`user_id` = 28 and `user_profiles`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 508}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.76246, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "home:508", "source": {"index": 21, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 508}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=508", "ajax": false, "filename": "home.blade.php", "line": "508"}, "connection": "instapwa", "explain": null, "start_percent": 52.293, "width_percent": 0.873}, {"sql": "select * from `user_profiles` where `user_profiles`.`user_id` = 27 and `user_profiles`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 566}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.7719631, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "home:566", "source": {"index": 21, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 566}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=566", "ajax": false, "filename": "home.blade.php", "line": "566"}, "connection": "instapwa", "explain": null, "start_percent": 53.166, "width_percent": 0.778}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 70 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [70, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.775104, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 53.944, "width_percent": 0.73}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 70 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [70, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.7772079, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 54.674, "width_percent": 0.619}, {"sql": "select * from `comments` where `comments`.`post_id` = 70 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [70], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.779478, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 55.293, "width_percent": 0.762}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 69 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [69, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.782003, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 56.055, "width_percent": 0.635}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 69 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [69, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.783962, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 56.689, "width_percent": 0.778}, {"sql": "select * from `comments` where `comments`.`post_id` = 69 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [69], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.786161, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 57.467, "width_percent": 0.825}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 68 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [68, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.78888, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 58.292, "width_percent": 0.762}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 68 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [68, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.7909682, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 59.054, "width_percent": 0.698}, {"sql": "select * from `comments` where `comments`.`post_id` = 68 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [68], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.792986, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 59.752, "width_percent": 0.603}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 67 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [67, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.795339, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 60.355, "width_percent": 0.809}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 67 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [67, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.797749, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 61.165, "width_percent": 0.762}, {"sql": "select * from `comments` where `comments`.`post_id` = 67 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [67], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.799994, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 61.927, "width_percent": 0.746}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 66 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [66, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.8025038, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 62.673, "width_percent": 0.635}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 66 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [66, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.804409, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 63.307, "width_percent": 0.603}, {"sql": "select * from `comments` where `comments`.`post_id` = 66 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [66], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.806433, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 63.91, "width_percent": 0.841}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 65 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [65, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.8088942, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 64.752, "width_percent": 0.746}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 65 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [65, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.8108711, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 65.498, "width_percent": 0.714}, {"sql": "select * from `comments` where `comments`.`post_id` = 65 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.8132808, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 66.212, "width_percent": 0.952}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 64 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [64, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.815909, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 67.164, "width_percent": 0.603}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 64 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [64, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.817655, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 67.767, "width_percent": 0.46}, {"sql": "select * from `comments` where `comments`.`post_id` = 64 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [64], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.8196082, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 68.227, "width_percent": 0.984}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 63 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [63, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.822461, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 69.211, "width_percent": 0.873}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 63 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [63, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.824521, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 70.084, "width_percent": 0.619}, {"sql": "select * from `comments` where `comments`.`post_id` = 63 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [63], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.826652, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 70.703, "width_percent": 0.857}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 62 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [62, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.829363, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 71.56, "width_percent": 0.73}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 62 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [62, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.8313031, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 72.29, "width_percent": 0.508}, {"sql": "select * from `comments` where `comments`.`post_id` = 62 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [62], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.833265, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 72.798, "width_percent": 0.555}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 61 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [61, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.8356829, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 73.353, "width_percent": 0.682}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 61 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [61, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.837536, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 74.036, "width_percent": 0.492}, {"sql": "select * from `comments` where `comments`.`post_id` = 61 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [61], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.839319, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 74.528, "width_percent": 0.794}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 60 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [60, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.841815, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 75.321, "width_percent": 0.778}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 60 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [60, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.843837, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 76.099, "width_percent": 0.746}, {"sql": "select * from `comments` where `comments`.`post_id` = 60 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [60], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.845797, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 76.845, "width_percent": 0.571}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 59 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [59, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.848305, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 77.416, "width_percent": 0.889}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 59 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [59, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.850388, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 78.305, "width_percent": 0.746}, {"sql": "select * from `comments` where `comments`.`post_id` = 59 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [59], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.852297, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 79.051, "width_percent": 0.714}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 58 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [58, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.85503, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 79.765, "width_percent": 0.778}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 58 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [58, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.8569949, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 80.543, "width_percent": 0.635}, {"sql": "select * from `comments` where `comments`.`post_id` = 58 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [58], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.858891, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 81.178, "width_percent": 0.667}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 57 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [57, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.861923, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 81.844, "width_percent": 0.889}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 57 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [57, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.863975, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 82.733, "width_percent": 0.778}, {"sql": "select * from `comments` where `comments`.`post_id` = 57 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [57], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.865882, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 83.511, "width_percent": 0.651}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 56 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [56, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.868448, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 84.161, "width_percent": 0.952}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 56 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [56, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.870692, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 85.113, "width_percent": 0.889}, {"sql": "select * from `comments` where `comments`.`post_id` = 56 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [56], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.87285, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 86.002, "width_percent": 0.905}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 55 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [55, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.876644, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 86.907, "width_percent": 0.857}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 55 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [55, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.879559, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 87.764, "width_percent": 0.809}, {"sql": "select * from `comments` where `comments`.`post_id` = 55 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [55], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.882185, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 88.573, "width_percent": 0.809}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 54 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [54, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.885139, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 89.383, "width_percent": 0.778}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 54 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [54, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.8873968, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 90.16, "width_percent": 0.92}, {"sql": "select * from `comments` where `comments`.`post_id` = 54 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [54], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.8903022, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 91.081, "width_percent": 0.746}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 28 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [28, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.8934822, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 91.827, "width_percent": 0.73}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 28 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [28, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.8961532, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 92.557, "width_percent": 0.698}, {"sql": "select * from `comments` where `comments`.`post_id` = 28 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.898108, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 93.255, "width_percent": 0.651}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 29 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [29, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.9003808, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 93.906, "width_percent": 0.635}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 29 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [29, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.902927, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 94.541, "width_percent": 0.857}, {"sql": "select * from `comments` where `comments`.`post_id` = 29 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.9056451, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 95.398, "width_percent": 0.794}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 30 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [30, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.9080799, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 96.191, "width_percent": 0.667}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 30 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [30, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.910486, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 96.858, "width_percent": 0.762}, {"sql": "select * from `comments` where `comments`.`post_id` = 30 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.912535, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 97.619, "width_percent": 0.778}, {"sql": "select count(*) as aggregate from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = 28 and `notifications`.`notifiable_id` is not null and `read_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\User", 28], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "layouts.app", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/layouts/app.blade.php", "line": 382}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.9187121, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "layouts.app:382", "source": {"index": 19, "namespace": "view", "name": "layouts.app", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/layouts/app.blade.php", "line": 382}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=382", "ajax": false, "filename": "app.blade.php", "line": "382"}, "connection": "instapwa", "explain": null, "start_percent": 98.397, "width_percent": 0.746}, {"sql": "select * from `love_wallets` where `love_wallets`.`user_id` = 28 and `love_wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "layouts.app", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/layouts/app.blade.php", "line": 413}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.921464, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "layouts.app:413", "source": {"index": 21, "namespace": "view", "name": "layouts.app", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/layouts/app.blade.php", "line": 413}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=413", "ajax": false, "filename": "app.blade.php", "line": "413"}, "connection": "instapwa", "explain": null, "start_percent": 99.143, "width_percent": 0.857}]}, "models": {"data": {"App\\Models\\Post": {"value": 20, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=1", "ajax": false, "filename": "Post.php", "line": "?"}}, "App\\Models\\User": {"value": 14, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\UserProfile": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FUserProfile.php&line=1", "ajax": false, "filename": "UserProfile.php", "line": "?"}}, "App\\Models\\LiveStream": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FLiveStream.php&line=1", "ajax": false, "filename": "LiveStream.php", "line": "?"}}}, "count": 43, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/home", "action_name": "home", "controller_action": "App\\Http\\Controllers\\HomeController@index", "uri": "GET home", "controller": "App\\Http\\Controllers\\HomeController@index<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=13\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=13\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/HomeController.php:13-60</a>", "middleware": "web, auth", "duration": "403ms", "peak_memory": "28MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2104434201 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2104434201\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-914302376 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-914302376\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Android&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"131 characters\">Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Mobile Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Inh5VE1VQ0ZiNDNoS0JjcDFSTUpxdVE9PSIsInZhbHVlIjoiRTJMRHdoNXNtZjRlbkhRbzR0YjdURmFyNk9EbDlHSFVnbTdOczZDREFYOWpna1VRNStpMTV5di8vS21QU0xLQzF3UzNyUzNsSEVMTnhWUE5WclE4L0dnbXVvVGNTQjFKdDJvamNOcTVkdGlSV1RTY281SjA3c1Q2TDFXRUdhYm4iLCJtYWMiOiI1NGJhMzI0ZjQ4OGM3MDBmNzEwMTg5ZDcyYzc1OTY0NWIwYWI1ZDRlMWExMWM1YjA5Y2Y5NjQzNTBmMGRhNDI4IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImFKaTN3Z1B1UC9JQmVXeXN2aW5scXc9PSIsInZhbHVlIjoiWlFGeENVdkdMMHRHVUwyL0dOeERBbVp2S3hPK29SajZQcVFWbGNESWxXN0lqVW5oU2M4Mi9oK1VoSjUvdlJtTHJwUDVTQTM1TGJyNnNPcTZoUXlPTFBMcy9IOXFwNWxRMVpjR2FNaWNNVWVQbUthRUs1OTk0NVkrUHhQai81aisiLCJtYWMiOiI3MTI2ZDFlZDJhN2FhYzk3N2Q1N2VjNDI1ODhkZDE5NTE2ZGQyNmM5N2QzNmNjMTJmMDg3YmJiMzAwM2U2MjZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-466730208 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HKkQb009ur9juY1euzSJdZerzJQUHXdiHWRWdESt</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ysQ2OZdBk91GzyxsvUNjStsqoUKCd3cWniDGhfUE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-466730208\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 22 Jul 2025 21:25:43 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-556808302 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HKkQb009ur9juY1euzSJdZerzJQUHXdiHWRWdESt</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>28</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-556808302\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/home", "action_name": "home", "controller_action": "App\\Http\\Controllers\\HomeController@index"}, "badge": null}}