@extends('layouts.app')

@section('content')
<div class="revenue-container">
    <div class="revenue-header">
        <h1>Revenue Dashboard</h1>
    </div>
    
    <div class="revenue-summary">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-dollar-sign"></i>
            </div>
            <div class="stat-details">
                <div class="stat-value">${{ number_format($totalRevenue, 2) }}</div>
                <div class="stat-label">Total Earned</div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-wallet"></i>
            </div>
            <div class="stat-details">
                <div class="stat-value">${{ number_format($availableForWithdrawal, 2) }}</div>
                <div class="stat-label">Available Balance</div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-hand-holding-usd"></i>
            </div>
            <div class="stat-details">
                <div class="stat-value">${{ number_format($totalWithdrawn, 2) }}</div>
                <div class="stat-label">Total Withdrawn</div>
            </div>
        </div>
    </div>
    
    <div class="withdraw-section">
        <div class="withdraw-info">
            <h3>Withdraw Your Earnings</h3>
            <p>Payouts are processed on the 1st of every month. Minimum withdrawal amount is $5.00.</p>
            
            @if($availableForWithdrawal >= 5.00)
                <div class="withdraw-eligible">
                    <p>You have <strong>${{ number_format($availableForWithdrawal, 2) }}</strong> available for withdrawal.</p>
                    <button id="withdrawButton" class="withdraw-button">Withdraw Now</button>
                </div>
            @else
                <div class="withdraw-ineligible">
                    <p>You need at least $5.00 to request a withdrawal.</p>
                    <p>Current balance: <strong>${{ number_format($availableForWithdrawal, 2) }}</strong></p>
                    <button class="withdraw-button disabled" disabled>Withdraw Now</button>
                </div>
            @endif
        </div>
    </div>
    
    <div class="revenue-tabs">
        <button class="tab-button active" data-tab="earnings">Recent Earnings</button>
        <button class="tab-button" data-tab="withdrawals">Withdrawal History</button>
    </div>
    
    <div id="earnings-tab" class="tab-content active">
        <div class="earnings-list">
            @if($recentEarnings->count() > 0)
                @foreach($recentEarnings as $earning)
                    <div class="earning-item">
                        <div class="earning-details">
                            <div class="earning-source">
                                @if($earning->source === 'loves')
                                    <i class="fas fa-heart"></i>
                                    <span>Love on post</span>
                                @else
                                    <i class="fas fa-star"></i>
                                    <span>Subscription</span>
                                @endif
                            </div>
                            <div class="earning-meta">
                                <span class="earning-date">{{ $earning->earned_at->format('M j, Y') }}</span>
                                <span class="earning-amount">${{ number_format($earning->amount, 2) }}</span>
                            </div>
                        </div>
                    </div>
                @endforeach
            @else
                <div class="no-earnings">
                    <p>You don't have any earnings yet.</p>
                </div>
            @endif
        </div>
    </div>
    
    <div id="withdrawals-tab" class="tab-content">
        <div class="withdrawals-list">
            @if($pastWithdrawals->count() > 0)
                @foreach($pastWithdrawals as $withdrawal)
                    <div class="withdrawal-item">
                        <div class="withdrawal-details">
                            <div class="withdrawal-info">
                                <span class="withdrawal-date">{{ $withdrawal->created_at->format('M j, Y') }}</span>
                                <span class="withdrawal-amount">${{ number_format($withdrawal->amount, 2) }}</span>
                            </div>
                            <div class="withdrawal-status {{ $withdrawal->status }}">
                                {{ ucfirst($withdrawal->status) }}
                            </div>
                        </div>
                    </div>
                @endforeach
            @else
                <div class="no-withdrawals">
                    <p>You haven't made any withdrawals yet.</p>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Withdrawal Confirmation Modal -->
<div id="withdrawalModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Confirm Withdrawal</h3>
            <span class="close-modal">&times;</span>
        </div>
        <div class="modal-body">
            <p>Are you sure you want to withdraw <strong>${{ number_format($availableForWithdrawal, 2) }}</strong>?</p>
            <p class="modal-note">This amount will be processed on the next payout date.</p>
        </div>
        <div class="modal-footer">
            <button id="cancelWithdrawal" class="cancel-button">Cancel</button>
            <button id="confirmWithdrawal" class="confirm-button">Confirm Withdrawal</button>
        </div>
    </div>
</div>

<style>
.revenue-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.revenue-header {
    margin-bottom: 30px;
}

.revenue-header h1 {
    font-size: 24px;
    font-weight: bold;
    color: #fff;
}

.revenue-summary {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: #222;
    border-radius: 10px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.stat-icon {
    width: 40px;
    height: 40px;
    background: rgba(255, 215, 0, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #FFD700;
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #fff;
}

.stat-label {
    color: #888;
    font-size: 14px;
}

.withdraw-section {
    background: #222;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
}

.withdraw-button {
    background: #FFD700;
    color: #000;
    border: none;
    border-radius: 5px;
    padding: 10px 20px;
    font-weight: bold;
    cursor: pointer;
}

.withdraw-button.disabled {
    background: #444;
    color: #888;
    cursor: not-allowed;
}

.revenue-tabs {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.tab-button {
    background: none;
    border: none;
    color: #888;
    cursor: pointer;
    padding-bottom: 10px;
    position: relative;
}

.tab-button.active {
    color: #FFD700;
}

.tab-button.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: #FFD700;
}

.earning-item, .withdrawal-item {
    background: #222;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
}

.earning-source {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #fff;
}

.earning-meta {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    color: #888;
}

.withdrawal-status {
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.withdrawal-status.pending {
    background: rgba(255, 215, 0, 0.1);
    color: #FFD700;
}

.withdrawal-status.completed {
    background: rgba(0, 255, 0, 0.1);
    color: #00FF00;
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: #222;
    border-radius: 10px;
    padding: 20px;
    max-width: 400px;
    width: 90%;
}

.close-modal {
    float: right;
    cursor: pointer;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.cancel-button {
    background: #444;
    color: #fff;
    border: none;
    border-radius: 5px;
    padding: 10px 15px;
    cursor: pointer;
}

.confirm-button {
    background: #FFD700;
    color: #000;
    border: none;
    border-radius: 5px;
    padding: 10px 15px;
    font-weight: bold;
    cursor: pointer;
}

@media (max-width: 768px) {
    .revenue-summary {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab switching
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tabName = this.dataset.tab;
            
            // Update active tab button
            tabButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Show active tab content
            tabContents.forEach(content => content.classList.remove('active'));
            document.getElementById(`${tabName}-tab`).classList.add('active');
        });
    });
    
    // Withdrawal modal
    const withdrawButton = document.getElementById('withdrawButton');
    const withdrawalModal = document.getElementById('withdrawalModal');
    const closeModal = document.querySelector('.close-modal');
    const cancelWithdrawal = document.getElementById('cancelWithdrawal');
    const confirmWithdrawal = document.getElementById('confirmWithdrawal');
    
    if (withdrawButton) {
        withdrawButton.addEventListener('click', function() {
            withdrawalModal.style.display = 'flex';
        });
    }
    
    if (closeModal) {
        closeModal.addEventListener('click', function() {
            withdrawalModal.style.display = 'none';
        });
    }
    
    if (cancelWithdrawal) {
        cancelWithdrawal.addEventListener('click', function() {
            withdrawalModal.style.display = 'none';
        });
    }
    
    if (confirmWithdrawal) {
        confirmWithdrawal.addEventListener('click', async function() {
            try {
                const response = await fetch('/api/withdraw-request', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // Reload page to show updated balance
                    window.location.reload();
                } else {
                    alert(data.message || 'Failed to process withdrawal request.');
                }
            } catch (error) {
                console.error('Error:', error);
                alert('An error occurred while processing your request.');
            }
            
            withdrawalModal.style.display = 'none';
        });
    }
});
</script>
@endsection 