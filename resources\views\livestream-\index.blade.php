@extends('layouts.app')

@section('content')
<style>
    .livestream-container {
        padding: 15px;
    }
    
    .livestream-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }
    
    .create-livestream-btn {
        background-color: #FFD700;
        color: black;
        padding: 10px 15px;
        border-radius: 5px;
        font-weight: bold;
        border: none;
        cursor: pointer;
        text-decoration: none;
    }
    
    .livestream-list {
        display: grid;
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .livestream-item {
        background-color: #222;
        border-radius: 10px;
        overflow: hidden;
    }
    
    .livestream-thumbnail {
        position: relative;
        width: 100%;
        height: 0;
        padding-bottom: 56.25%; /* 16:9 aspect ratio */
        background-color: #333;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .livestream-thumbnail i {
        font-size: 48px;
        color: #FF0000;
    }
    
    .live-badge {
        position: absolute;
        top: 10px;
        left: 10px;
        background-color: #FF0000;
        color: white;
        padding: 3px 8px;
        border-radius: 3px;
        font-size: 12px;
        font-weight: bold;
    }
    
    .viewer-count {
        position: absolute;
        bottom: 10px;
        right: 10px;
        background-color: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 3px 8px;
        border-radius: 3px;
        font-size: 12px;
    }
    
    .livestream-info {
        padding: 15px;
    }
    
    .livestream-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .livestream-user {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 10px;
    }
    
    .user-avatar {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        object-fit: cover;
    }
    
    .user-name {
        font-size: 14px;
        color: #BBB;
    }
    
    .no-livestreams {
        text-align: center;
        padding: 40px;
        color: #666;
    }
</style>

<div class="livestream-container">
    <div class="livestream-header">
        <h1>Live Streams</h1>
        <a href="{{ route('livestream.create') }}" class="create-livestream-btn">Go Live</a>
    </div>
    
    <div class="livestream-list">
        @forelse($livestreams as $livestream)
            <a href="{{ route('livestream.show', $livestream->id) }}" class="livestream-item">
                <div class="livestream-thumbnail">
                    <i class="fas fa-broadcast-tower"></i>
                    <div class="live-badge">LIVE</div>
                    <div class="viewer-count">
                        <i class="fas fa-eye"></i> {{ $livestream->viewer_count }}
                    </div>
                </div>
                <div class="livestream-info">
                    <div class="livestream-title">{{ $livestream->title }}</div>
                    <div class="livestream-user">
                        @if($livestream->user->profile && $livestream->user->profile->photo)
                            <img src="{{ asset('storage/' . $livestream->user->profile->photo) }}" alt="{{ $livestream->user->username }}" class="user-avatar">
                        @else
                            <img src="/images/default-avatar.png" alt="{{ $livestream->user->username }}" class="user-avatar">
                        @endif
                        <div class="user-name">{{ $livestream->user->username }}</div>
                    </div>
                </div>
            </a>
        @empty
            <div class="no-livestreams">
                No one is streaming right now. Be the first to go live!
            </div>
        @endforelse
    </div>
</div>
@endsection 