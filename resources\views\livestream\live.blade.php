@extends('layouts.app')

@section('content')
<div class="live-stream-container">
    <!-- Stream Header -->
    <div class="stream-header">
        <div class="streamer-info">
            <img src="{{ $stream->user->profile_photo ?? asset('images/default.png') }}" 
                 class="streamer-avatar" alt="Streamer">
            <div class="streamer-details">
                <h4 class="streamer-name">{{ $stream->user->name }}</h4>
                <span class="stream-title">{{ $stream->title }}</span>
            </div>
        </div>
        
        <div class="stream-stats">
            <div class="live-indicator">
                <span class="live-dot"></span>
                LIVE
            </div>
            <div class="viewer-count">
                <i class="fas fa-eye"></i>
                <span id="viewerCount">{{ $stream->viewer_count ?? 0 }}</span>
            </div>
        </div>

        @if(Auth::id() === $stream->user_id)
        <button class="end-stream-btn" id="endStreamBtn">
            <i class="fas fa-times"></i>
        </button>
        @else
        <button class="close-stream-btn" onclick="window.history.back()">
            <i class="fas fa-times"></i>
        </button>
        @endif
    </div>

    <!-- Main Video Container -->
    <div class="video-container">
        <video id="liveVideo" autoplay playsinline muted></video>
        
        <!-- Floating Hearts Animation -->
        <div class="hearts-container" id="heartsContainer"></div>
        
        <!-- Stream Controls (for streamer) -->
        @if(Auth::id() === $stream->user_id)
        <div class="stream-controls">
            <button class="control-btn" id="toggleAudioBtn">
                <i class="fas fa-microphone" id="audioIcon"></i>
            </button>
            <button class="control-btn" id="toggleVideoBtn">
                <i class="fas fa-video" id="videoIcon"></i>
            </button>
            <button class="control-btn" id="flipCameraBtn">
                <i class="fas fa-camera-rotate"></i>
            </button>
        </div>
        @endif
    </div>

    <!-- Comments Section -->
    <div class="comments-section">
        <div class="comments-list" id="commentsList">
            <!-- Comments will be dynamically loaded here -->
        </div>
        
        <div class="comment-input-container">
            <input type="text" 
                   class="comment-input" 
                   id="commentInput" 
                   placeholder="Add a comment..." 
                   maxlength="100">
            <button class="send-comment-btn" id="sendCommentBtn">
                <i class="fas fa-paper-plane"></i>
            </button>
            <button class="heart-reaction-btn" id="heartBtn">
                <i class="fas fa-heart"></i>
            </button>
        </div>
    </div>
</div>

<style>
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.live-stream-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: #000;
    color: white;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    overflow: hidden;
}

/* Header Styles */
.stream-header {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: linear-gradient(180deg, rgba(0,0,0,0.8) 0%, transparent 100%);
}

.streamer-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.streamer-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid #fff;
    object-fit: cover;
}

.streamer-details h4 {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.stream-title {
    font-size: 14px;
    opacity: 0.8;
}

.stream-stats {
    display: flex;
    align-items: center;
    gap: 15px;
}

.live-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    background: #ff3040;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
}

.live-dot {
    width: 8px;
    height: 8px;
    background: white;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.viewer-count {
    display: flex;
    align-items: center;
    gap: 5px;
    background: rgba(0,0,0,0.5);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 14px;
}

.end-stream-btn, .close-stream-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: rgba(255,255,255,0.2);
    color: white;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s;
}

.end-stream-btn:hover, .close-stream-btn:hover {
    background: rgba(255,255,255,0.3);
}

/* Video Container */
.video-container {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
}

#liveVideo {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Stream Controls */
.stream-controls {
    position: absolute;
    bottom: 120px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.control-btn {
    width: 50px;
    height: 50px;
    border: none;
    background: rgba(0,0,0,0.6);
    color: white;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    transition: all 0.3s;
}

.control-btn:hover {
    background: rgba(0,0,0,0.8);
    transform: scale(1.1);
}

.control-btn.disabled {
    background: rgba(255,0,0,0.6);
}

/* Comments Section */
.comments-section {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 50;
    background: linear-gradient(0deg, rgba(0,0,0,0.8) 0%, transparent 100%);
    padding: 20px;
    max-height: 40vh;
}

.comments-list {
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 15px;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.comments-list::-webkit-scrollbar {
    display: none;
}

.comment-item {
    background: rgba(0,0,0,0.5);
    margin-bottom: 8px;
    padding: 8px 12px;
    border-radius: 18px;
    max-width: 80%;
    animation: slideInLeft 0.3s ease;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.comment-username {
    font-weight: 600;
    font-size: 14px;
    margin-right: 8px;
    color: #fff;
}

.comment-text {
    font-size: 14px;
    color: rgba(255,255,255,0.9);
}

.comment-input-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.comment-input {
    flex: 1;
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    border-radius: 25px;
    padding: 12px 16px;
    color: white;
    font-size: 16px;
}

.comment-input::placeholder {
    color: rgba(255,255,255,0.6);
}

.comment-input:focus {
    outline: none;
    border-color: rgba(255,255,255,0.4);
    background: rgba(255,255,255,0.15);
}

.send-comment-btn, .heart-reaction-btn {
    width: 45px;
    height: 45px;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    transition: all 0.3s;
}

.send-comment-btn {
    background: #007bff;
    color: white;
}

.heart-reaction-btn {
    background: #ff3040;
    color: white;
}

.send-comment-btn:hover, .heart-reaction-btn:hover {
    transform: scale(1.1);
}

/* Hearts Animation */
.hearts-container {
    position: absolute;
    bottom: 0;
    right: 20px;
    width: 50px;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.floating-heart {
    position: absolute;
    font-size: 30px;
    color: #ff3040;
    animation: floatUp 3s ease-out forwards;
    bottom: 0;
    right: 0;
}

@keyframes floatUp {
    0% {
        opacity: 1;
        transform: translateY(0) rotate(0deg) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-100vh) rotate(180deg) scale(0.5);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .stream-header {
        padding: 10px 15px;
    }
    
    .streamer-avatar {
        width: 35px;
        height: 35px;
    }
    
    .comments-section {
        padding: 15px;
    }
    
    .comment-input {
        font-size: 14px;
        padding: 10px 14px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const streamId = {{ $stream->id }};
    const isStreamer = {{ Auth::id() === $stream->user_id ? 'true' : 'false' }};
    const currentUserId = {{ Auth::id() }};
    const currentUsername = '{{ Auth::user()->username }}';
    
    let mediaStream;
    let mediaRecorder;
    let recordedChunks = [];
    let audioEnabled = true;
    let videoEnabled = true;
    let facingMode = 'user';

    // Initialize WebSocket for real-time features
    initializeWebSocket();
    
    // Initialize stream
    if (isStreamer) {
        initializeStreamerView();
    } else {
        initializeViewerView();
    }

    // WebSocket initialization
    function initializeWebSocket() {
        // You can use Laravel Echo here
        // Echo.channel(`stream.${streamId}`)
        //     .listen('NewComment', handleNewComment)
        //     .listen('ViewerCountUpdated', updateViewerCount)
        //     .listen('HeartReaction', showHeartAnimation);
    }

    // Streamer view initialization
    async function initializeStreamerView() {
        try {
            mediaStream = await navigator.mediaDevices.getUserMedia({
                video: { 
                    facingMode,
                    width: { ideal: 1280 },
                    height: { ideal: 720 }
                },
                audio: true
            });

            document.getElementById('liveVideo').srcObject = mediaStream;
            startRecording();
            
            // Initialize controls
            initializeStreamControls();
            
        } catch (error) {
            console.error('Error accessing camera:', error);
            alert('Unable to access camera. Please check permissions.');
        }
    }

    // Viewer view initialization  
    function initializeViewerView() {
        // In a real implementation, this would connect to the streamer's feed
        // For now, we'll show a placeholder or demo feed
        updateViewerCount();
    }

    // Stream controls for streamer
    function initializeStreamControls() {
        // Toggle audio
        document.getElementById('toggleAudioBtn').addEventListener('click', () => {
            audioEnabled = !audioEnabled;
            const audioTracks = mediaStream.getAudioTracks();
            audioTracks.forEach(track => track.enabled = audioEnabled);
            
            const icon = document.getElementById('audioIcon');
            icon.className = audioEnabled ? 'fas fa-microphone' : 'fas fa-microphone-slash';
            
            const btn = document.getElementById('toggleAudioBtn');
            btn.classList.toggle('disabled', !audioEnabled);
        });

        // Toggle video
        document.getElementById('toggleVideoBtn').addEventListener('click', () => {
            videoEnabled = !videoEnabled;
            const videoTracks = mediaStream.getVideoTracks();
            videoTracks.forEach(track => track.enabled = videoEnabled);
            
            const icon = document.getElementById('videoIcon');
            icon.className = videoEnabled ? 'fas fa-video' : 'fas fa-video-slash';
            
            const btn = document.getElementById('toggleVideoBtn');
            btn.classList.toggle('disabled', !videoEnabled);
        });

        // Flip camera
        document.getElementById('flipCameraBtn').addEventListener('click', async () => {
            facingMode = facingMode === 'user' ? 'environment' : 'user';
            await reinitializeCamera();
        });
    }

    // Recording functions
    function startRecording() {
        if (!mediaStream) return;

        try {
            recordedChunks = [];
            mediaRecorder = new MediaRecorder(mediaStream);
            
            mediaRecorder.ondataavailable = event => {
                if (event.data.size > 0) {
                    recordedChunks.push(event.data);
                }
            };

            mediaRecorder.start(1000);
        } catch (error) {
            console.error('Error starting recording:', error);
        }
    }

    async function stopRecording() {
        return new Promise(resolve => {
            if (!mediaRecorder || mediaRecorder.state === 'inactive') {
                resolve();
                return;
            }

            mediaRecorder.onstop = async () => {
                const blob = new Blob(recordedChunks, { type: 'video/webm' });
                const formData = new FormData();
                formData.append('recording', blob, `stream-${streamId}.webm`);
                
                try {
                    await fetch(`/live/upload/${streamId}`, {
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                        },
                        body: formData
                    });
                } catch (error) {
                    console.error('Error uploading recording:', error);
                }
                resolve();
            };
            
            mediaRecorder.stop();
        });
    }

    // Comment system
    document.getElementById('sendCommentBtn').addEventListener('click', sendComment);
    document.getElementById('commentInput').addEventListener('keypress', (e) => {
        if (e.key === 'Enter') sendComment();
    });

    function sendComment() {
        const input = document.getElementById('commentInput');
        const comment = input.value.trim();
        
        if (!comment) return;

        // Add comment to UI immediately
        addCommentToUI(currentUsername, comment);
        
        // Send to server
        fetch(`/live/${streamId}/comment`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({ comment })
        });

        input.value = '';
    }

    function addCommentToUI(username, text) {
        const commentsList = document.getElementById('commentsList');
        const commentElement = document.createElement('div');
        commentElement.className = 'comment-item';
        commentElement.innerHTML = `
            <span class="comment-username">${username}</span>
            <span class="comment-text">${text}</span>
        `;
        
        commentsList.appendChild(commentElement);
        commentsList.scrollTop = commentsList.scrollHeight;
        
        // Remove old comments to prevent memory issues
        while (commentsList.children.length > 50) {
            commentsList.removeChild(commentsList.firstChild);
        }
    }

    // Heart reaction
    document.getElementById('heartBtn').addEventListener('click', () => {
        sendHeartReaction();
        showHeartAnimation();
    });

    function sendHeartReaction() {
        fetch(`/live/${streamId}/heart`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });
    }

    function showHeartAnimation() {
        const heartsContainer = document.getElementById('heartsContainer');
        const heart = document.createElement('div');
        heart.className = 'floating-heart';
        heart.innerHTML = '❤️';
        heart.style.left = Math.random() * 20 + 'px';
        
        heartsContainer.appendChild(heart);
        
        setTimeout(() => {
            heart.remove();
        }, 3000);
    }

    // End stream
    if (isStreamer) {
        document.getElementById('endStreamBtn').addEventListener('click', async () => {
            if (confirm('Are you sure you want to end this live stream?')) {
                try {
                    await stopRecording();
                    
                    const response = await fetch(`/live/${streamId}/end`, {
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                        }
                    });
                    
                    const data = await response.json();
                    if (data.success) {
                        window.location.href = '/home';
                    }
                } catch (error) {
                    console.error('Error ending stream:', error);
                    alert('Failed to end stream. Please try again.');
                }
            }
        });
    }

    // Helper functions
    async function reinitializeCamera() {
        if (mediaStream) {
            mediaStream.getTracks().forEach(track => track.stop());
        }
        
        try {
            mediaStream = await navigator.mediaDevices.getUserMedia({
                video: { 
                    facingMode,
                    width: { ideal: 1280 },
                    height: { ideal: 720 }
                },
                audio: true
            });
            
            document.getElementById('liveVideo').srcObject = mediaStream;
            startRecording();
        } catch (error) {
            console.error('Error reinitializing camera:', error);
        }
    }

    function updateViewerCount() {
        // This would be updated via WebSocket in real implementation
        const count = Math.floor(Math.random() * 100) + 1;
        document.getElementById('viewerCount').textContent = count;
    }

    // Update viewer count every 30 seconds
    setInterval(updateViewerCount, 30000);
});
</script>
@endsection 