@extends('layouts.app')

@section('content')
<style>
    .camp-container {
        padding: 15px;
    }
    
    .camp-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }
    
    .create-camp-btn {
        background-color: #FFD700;
        color: black;
        padding: 10px 15px;
        border-radius: 5px;
        font-weight: bold;
        border: none;
        cursor: pointer;
        text-decoration: none;
    }
    
    .camp-list {
        display: grid;
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .camp-item {
        background-color: #222;
        border-radius: 10px;
        padding: 15px;
        display: flex;
        flex-direction: column;
        gap: 10px;
    }
    
    .camp-name {
        font-size: 18px;
        font-weight: bold;
    }
    
    .camp-description {
        color: #BBB;
        font-size: 14px;
    }
    
    .camp-details {
        display: flex;
        justify-content: space-between;
        color: #999;
        font-size: 12px;
    }
    
    .camp-actions {
        display: flex;
        gap: 10px;
        margin-top: 10px;
    }
    
    .camp-btn {
        padding: 8px 12px;
        border-radius: 5px;
        text-align: center;
        text-decoration: none;
        cursor: pointer;
        font-size: 14px;
    }
    
    .camp-join-btn {
        background-color: #333;
        color: white;
    }
    
    .camp-view-btn {
        background-color: #FFD700;
        color: black;
    }
    
    .camp-delete-btn {
        background-color: #FF4136;
        color: white;
    }
    
    .camp-type-badge {
        display: inline-block;
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 12px;
        margin-left: 8px;
    }
    
    .camp-type-private {
        background-color: #FF4136;
        color: white;
    }
    
    .camp-type-public {
        background-color: #2ECC40;
        color: white;
    }
</style>

<div class="camp-container">
    <div class="camp-header">
        <h1>Camps</h1>
        <a href="{{ route('camps.create') }}" class="create-camp-btn">Create Camp</a>
    </div>
    
    @if(session('success'))
        <div style="background-color: #2ECC40; color: white; padding: 10px; border-radius: 5px; margin-bottom: 15px;">
            {{ session('success') }}
        </div>
    @endif
    
    @if(session('error'))
        <div style="background-color: #FF4136; color: white; padding: 10px; border-radius: 5px; margin-bottom: 15px;">
            {{ session('error') }}
        </div>
    @endif
    
    <div class="camp-list">
        @foreach($camps as $camp)
            <div class="camp-item">
                <div>
                    <span class="camp-name">{{ $camp->name }}</span>
                    <span class="camp-type-badge camp-type-{{ $camp->type }}">{{ ucfirst($camp->type) }}</span>
                </div>
                <div class="camp-description">{{ Str::limit($camp->description, 150) }}</div>
                <div class="camp-details">
                    
                    <span>Members: {{ $camp->members_count }}</span>
               
                        <span>Created by: {{ $camp->name }}</span>
                   
                    <span>Created: {{ $camp->created_at->diffForHumans() }}</span>
                </div>
                <div class="camp-actions">
                    <a href="{{ route('camps.show', $camp) }}" class="camp-btn camp-view-btn">View Camp</a>
                    
                    @if(!$camp->isMember(Auth::user()))
                        <form action="{{ route('camps.join', $camp) }}" method="POST" style="display: inline;">
                            @csrf
                            <button type="submit" class="camp-btn camp-join-btn">Join Camp</button>
                        </form>
                    @endif
                    
                    @if($camp->created_by === Auth::id())
                        <form action="{{ route('camps.destroy', $camp) }}" method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this camp?');">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="camp-btn camp-delete-btn">Delete Camp</button>
                        </form>
                    @endif
                </div>
            </div>
        @endforeach
    </div>
    
    <div style="margin-top: 20px;">
        {{ $camps->links() }}
    </div>
</div>
@endsection 