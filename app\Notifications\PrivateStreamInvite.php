<?php

namespace App\Notifications;

use App\Models\LiveStream;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Support\Facades\Auth;


class PrivateStreamInvite extends Notification
{
    public function __construct(public LiveStream $stream) {}

    public function via($notifiable)
    {
        return ['database', 'mail'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Private Stream Invitation')
            ->line("You've been invited to a private stream by {$this->stream->user->name}")
            ->action('Join Stream', route('stream.show', $this->stream))
            ->line('This invitation will expire when the stream ends');
    }
} 
