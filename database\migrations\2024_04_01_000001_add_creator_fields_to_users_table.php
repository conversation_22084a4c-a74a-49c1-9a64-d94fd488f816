<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->boolean('is_creator')->default(false);
            $table->decimal('subscription_price', 8, 2)->nullable();
            $table->text('creator_bio')->nullable();
            $table->decimal('earned_balance', 10, 2)->default(0.00);
            $table->decimal('paid_balance', 10, 2)->default(0.00);
            $table->string('payment_email')->nullable();
            $table->string('payment_method')->nullable();
            $table->string('tax_id')->nullable();
        });
    }

    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'is_creator', 
                'subscription_price', 
                'creator_bio',
                'earned_balance',
                'paid_balance',
                'payment_email',
                'payment_method',
                'tax_id'
            ]);
        });
    }
}; 