<?php

namespace App\Notifications;

use App\Models\LiveStream;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Messages\BroadcastMessage;

class LiveStreamStarted extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(public LiveStream $stream)
    {
    }

    public function via($notifiable)
    {
        return ['database', 'broadcast'];
    }

    public function toArray($notifiable)
    {
        return [
            'message' => $this->stream->user->name.' started a live stream: '.$this->stream->title,
            'url' => route('stream.show', $this->stream),
            'stream_id' => $this->stream->id
        ];
    }

    public function toBroadcast($notifiable)
    {
        return new BroadcastMessage($this->toArray($notifiable));
    }
} 