<?php

namespace App\Http\Controllers;

use App\Models\Post;
use App\Models\User;
use App\Models\LiveStream;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class HomeController extends Controller
{
    public function index()
    {
        // Get active live streams for stories-like display
        $liveStreams = LiveStream::with(['user'])
            ->where('is_live', true)
            ->where('visibility', 'public')
            ->orderBy('started_at', 'desc')
            ->take(10)
            ->get();

        // Get posts from followed users and own posts
        $posts = Post::with(['user.profile', 'loves'])
            ->whereIn('user_id', function($query) {
                $query->select('followed_id')
                      ->from('followers')
                      ->where('follower_id', Auth::id())
                      ->where('approved', true);
            })
            ->orWhere('user_id', Auth::id())
            ->orWhere('visibility', 'public')
            ->latest()
            ->take(20)
            ->get();
            
        // Get suggested users
        $suggestedUsers = User::with('profile')
            ->where('id', '!=', Auth::id())
            ->whereNotIn('id', function($query) {
                $query->select('followed_id')
                      ->from('followers')
                      ->where('follower_id', Auth::id());
            })
            ->inRandomOrder()
            ->take(5)
            ->get();

        // Get recent live streams (ended ones with recordings)
        $recentStreams = LiveStream::with(['user'])
            ->where('has_recording', true)
            ->whereNotNull('recording_path')
            ->where('visibility', 'public')
            ->where('ended_at', '>', now()->subHours(24)) // Last 24 hours
            ->orderBy('ended_at', 'desc')
            ->take(8)
            ->get();
            
        return view('home', compact('posts', 'suggestedUsers', 'liveStreams', 'recentStreams'));
    }
} 