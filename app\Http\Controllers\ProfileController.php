<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\LiveStream;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class ProfileController extends Controller
{
    /**
     * Display the user's profile.
     */
    public function index(Request $request)
    {
        $username = $request->username;
        
        if ($username) {
            $user = User::where('username', $username)->firstOrFail();
        } else {
            $user = Auth::user();
        }
        
        // Get counts for display with error handling
        $postCount = $user->posts()->count();
        $posts = $user->posts()->latest()->get();
        
        // Check if followers relationship exists
        $followersCount = method_exists($user, 'followers') ? $user->followers()->count() : 0;
        
        // Check if following relationship exists
        $followingCount = method_exists($user, 'following') ? $user->following()->count() : 0;
        
        // Check if subscribers relationship exists
        $subscribersCount = method_exists($user, 'subscribers') ? $user->subscribers()->count() : 0;

        // Get user's live streams
        $currentLiveStream = $user->liveStreams()
            ->where('is_live', true)
            ->first();

        $recentLiveStreams = $user->liveStreams()
            ->where('has_recording', true)
            ->whereNotNull('recording_path')
            ->orderBy('ended_at', 'desc')
            ->take(6)
            ->get();

        $totalLiveStreams = $user->liveStreams()->count();
        
        return view('profile.index', compact(
            'user', 
            'postCount', 
            'posts',
            'followersCount', 
            'followingCount',
            'subscribersCount',
            'currentLiveStream',
            'recentLiveStreams',
            'totalLiveStreams'
        ));
    }

    /**
     * Show the form for editing the user's profile.
     */
    public function edit()
    {
        $user = Auth::user();
        return view('profile.edit', compact('user'));
    }

    /**
     * Update the user's profile.
     */
    public function update(Request $request)
    {
        $user = Auth::user();
        
        $request->validate([
            'name' => 'required|string|max:255',
            'username' => 'required|string|max:255|unique:users,username,' . $user->id,
            'bio' => 'nullable|string|max:500',
            'profile_photo' => 'nullable|image|max:5120', // 5MB max
        ]);
        
        if ($request->hasFile('avatar')) {
            // Delete old avatar if exists
            if ($user->avatar) {
                Storage::disk('public')->delete($user->avatar);
            }
            
            $path = $request->file('avatar')->store('avatars', 'public');
            $user->avatar = $path;
        }
        
        // Update user details
        $user->name = $request->name;
        $user->username = $request->username;
        
        // Create or update profile
        if (!$user->profile) {
            $profile = new UserProfile();
            $profile->user_id = $user->id;
            $profile->bio = $request->bio;
            
            if ($request->hasFile('profile_photo')) {
                $path = $request->file('profile_photo')->store('profiles', 'public');
                $profile->photo = $path;
            }
            
            $profile->save();
        } else {
            $user->profile->bio = $request->bio;
            
            if ($request->hasFile('profile_photo')) {
                $path = $request->file('profile_photo')->store('profiles', 'public');
                $user->profile->photo = $path;
            }
            
            $user->profile->save();
        }
        
        $user->save();
        
        return redirect()->route('profile')->with('success', 'Profile updated successfully');
    }
} 