<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ $stream->title }}
            </h2>
            <div class="flex items-center gap-2">
                <span id="connection-status" class="px-3 py-1 rounded text-sm">⚪ Connecting...</span>
                @if($stream->is_live)
                    <span class="inline-block w-2 h-2 rounded-full bg-red-500 animate-pulse"></span>
                    <span class="text-red-500 font-semibold">Live</span>
                @else
                    <span class="text-gray-500">Offline</span>
                @endif
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="mb-4">
                        <p class="text-gray-600">{{ $stream->description }}</p>
                        <p class="text-sm text-gray-500 mt-2">Streamed by {{ $stream->user->name }}</p>
                        <p class="text-sm text-red-500" id="viewer-count">Viewers: {{ $stream->viewer_count }}</p>
                    </div>

                    @if($stream->user_id === auth()->id())
                        <div class="mb-4 p-4 bg-gray-100 rounded">
                            <h3 class="font-semibold mb-2">Stream Information</h3>
                            <p class="mb-2">Your Stream Key: <span class="font-mono bg-gray-200 px-2 py-1 rounded">{{ $stream->stream_key }}</span></p>
                            <div class="flex gap-4 mt-4">
                                <button id="start-stream" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded" {{ $stream->is_live ? 'disabled' : '' }}>
                                    Start Stream
                                </button>
                                <button id="stop-stream" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded" {{ !$stream->is_live ? 'disabled' : '' }}>
                                    Stop Stream
                                </button>
                            </div>
                        </div>

                        <div class="mb-4">
                            <video id="local-video" autoplay playsinline muted class="w-full aspect-video bg-black"></video>
                            <div id="stream-status" class="mt-2 text-sm"></div>
                        </div>
                    
                        <div class="mb-4">
                            <div class="relative">
                                <video id="remote-video" autoplay playsinline class="w-full aspect-video bg-black"></video>
                                <div id="loading-overlay" class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 text-white">
                                    <div class="text-center">
                                        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white mx-auto"></div>
                                        <p class="mt-2">Connecting to stream...</p>
                                    </div>
                                </div>
                            </div>
                            <div id="stream-status" class="mt-2 text-sm"></div>
                        </div>
                    @else
                        <div class="mb-4">
                            <div class="relative">
                                <video id="remote-video" autoplay playsinline class="w-full aspect-video bg-black"></video>
                                <div id="loading-overlay" class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 text-white">
                                    <div class="text-center">
                                        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white mx-auto"></div>
                                        <p class="mt-2">Connecting to stream...</p>
                                    </div>
                                </div>
                            </div>
                            <div id="stream-status" class="mt-2 text-sm"></div>
                        </div>
                    @endif

                    <!-- Debug Information Panel -->
                    <div class="mt-4 p-4 bg-gray-100 rounded">
                        <h3 class="font-semibold mb-2">Connection Status</h3>
                        <div id="debug-info" class="text-sm font-mono"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script src="https://webrtc.github.io/adapter/adapter-latest.js"></script>
    <script src="https://cdn.socket.io/4.7.4/socket.io.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const isStreamer = {{ $stream->user_id === auth()->id() ? 'true' : 'false' }};
            const streamId = {{ $stream->id }};
            let peerConnection;
            let localStream;
            let socket;
            
            const connectionStatus = document.getElementById('connection-status');
            const debugInfo = document.getElementById('debug-info');
            const streamStatus = document.getElementById('stream-status');
            const loadingOverlay = document.getElementById('loading-overlay');

            function updateDebugInfo(message) {
                const timestamp = new Date().toLocaleTimeString();
                debugInfo.innerHTML += `<div>[${timestamp}] ${message}</div>`;
                debugInfo.scrollTop = debugInfo.scrollHeight;
            }

            function updateStreamStatus(message) {
                if (streamStatus) {
                    streamStatus.textContent = message;
                }
            }

            function updateConnectionStatus(status, color) {
                connectionStatus.className = `px-3 py-1 rounded text-sm bg-${color}-100 text-${color}-800`;
                connectionStatus.textContent = status;
            }

            // WebRTC configuration
            const configuration = {
                iceServers: [
                    { urls: 'stun:stun.l.google.com:19302' }
                ]
            };

            // Store pending ICE candidates
            let pendingIceCandidates = [];
            let isSettingRemoteDescription = false;

            // Connect to signaling server
            try {
                updateDebugInfo('Connecting to signaling server...');
                socket = io('http://localhost:3000');
            } catch (error) {
                updateDebugInfo(`Failed to connect to signaling server: ${error.message}`);
                updateConnectionStatus('❌ Connection Failed', 'red');
            }

            socket.on('connect', () => {
                updateDebugInfo('Connected to signaling server');
                updateConnectionStatus('🟢 Connected', 'green');
                socket.emit('join-stream', streamId);
            });

            socket.on('connect_error', (error) => {
                updateDebugInfo(`Socket connection error: ${error.message}`);
                updateConnectionStatus('❌ Connection Failed', 'red');
            });

            // Handle viewer count updates
            socket.on('viewer-count', ({ count }) => {
                document.getElementById('viewer-count').textContent = `Viewers: ${count}`;
                updateDebugInfo(`Viewer count updated: ${count}`);
            });

            // Handle stream ended
            socket.on('stream-ended', () => {
                if (!isStreamer) {
                    const remoteVideo = document.getElementById('remote-video');
                    remoteVideo.srcObject = null;
                    updateDebugInfo('Stream ended by broadcaster');
                    updateStreamStatus('Stream has ended');
                    updateConnectionStatus('⭕ Stream Ended', 'yellow');
                    if (loadingOverlay) {
                        loadingOverlay.style.display = 'flex';
                    }
                    alert('Stream has ended');
                }
            });

            socket.on('stream-started', ({ streamId }) => {
                updateDebugInfo(`Stream ${streamId} started`);
                if (!isStreamer) {
                    updateStreamStatus('Stream started, establishing connection...');
                }
            });

            // Handle incoming signals
            socket.on('signal', async ({ signal, senderId }) => {
                try {
                    updateDebugInfo(`Received signal: ${signal.type || 'ICE candidate'} from ${senderId}`);
                    
                    if (!peerConnection) {
                        updateDebugInfo('Initializing WebRTC for incoming signal...');
                        await initWebRTC();
                    }

                    if (signal.type === 'offer') {
                        isSettingRemoteDescription = true;
                        updateDebugInfo('Processing offer...');
                        await peerConnection.setRemoteDescription(new RTCSessionDescription(signal));
                        updateDebugInfo('Set remote description from offer');
                        
                        // Process any pending ICE candidates
                        while (pendingIceCandidates.length > 0) {
                            const candidate = pendingIceCandidates.shift();
                            try {
                                await peerConnection.addIceCandidate(new RTCIceCandidate(candidate));
                                updateDebugInfo('Added pending ICE candidate');
                            } catch (error) {
                                updateDebugInfo(`Failed to add pending ICE candidate: ${error.message}`);
                            }
                        }

                        const answer = await peerConnection.createAnswer();
                        await peerConnection.setLocalDescription(answer);
                        socket.emit('signal', {
                            streamId,
                            signal: answer
                        });
                        updateDebugInfo('Sent answer to offer');
                        isSettingRemoteDescription = false;
                    } else if (signal.type === 'answer') {
                        isSettingRemoteDescription = true;
                        updateDebugInfo('Processing answer...');
                        await peerConnection.setRemoteDescription(new RTCSessionDescription(signal));
                        updateDebugInfo('Set remote description from answer');
                        
                        // Process any pending ICE candidates
                        while (pendingIceCandidates.length > 0) {
                            const candidate = pendingIceCandidates.shift();
                            try {
                                await peerConnection.addIceCandidate(new RTCIceCandidate(candidate));
                                updateDebugInfo('Added pending ICE candidate');
                            } catch (error) {
                                updateDebugInfo(`Failed to add pending ICE candidate: ${error.message}`);
                            }
                        }
                        isSettingRemoteDescription = false;
                    } else if (signal.candidate) {
                        updateDebugInfo(`Processing ICE candidate: ${signal.candidate}`);
                        
                        // If we're in the middle of setting remote description, queue the candidate
                        if (isSettingRemoteDescription || !peerConnection.remoteDescription) {
                            updateDebugInfo('Queuing ICE candidate for later');
                            pendingIceCandidates.push(signal);
                            return;
                        }

                        try {
                            await peerConnection.addIceCandidate(new RTCIceCandidate(signal));
                            updateDebugInfo('Added ICE candidate successfully');
                        } catch (error) {
                            updateDebugInfo(`Failed to add ICE candidate: ${error.message}`);
                            pendingIceCandidates.push(signal);
                        }
                    }
                } catch (error) {
                    updateDebugInfo(`Error handling signal: ${error.message}`);
                    console.error('Error handling signal:', error);
                }
            });

            // Initialize WebRTC
            async function initWebRTC() {
                try {
                    updateDebugInfo('Initializing WebRTC...');
                    peerConnection = new RTCPeerConnection(configuration);

                    // Log initial connection states
                    updateDebugInfo(`Initial connection state: ${peerConnection.connectionState}`);
                    updateDebugInfo(`Initial ICE connection state: ${peerConnection.iceConnectionState}`);
                    updateDebugInfo(`Initial signaling state: ${peerConnection.signalingState}`);

                    // Clear any existing candidates
                    pendingIceCandidates = [];
                    isSettingRemoteDescription = false;

                    peerConnection.oniceconnectionstatechange = () => {
                        const state = peerConnection.iceConnectionState;
                        updateDebugInfo(`ICE Connection State changed to: ${state}`);
                        updateStreamStatus(`Connection state: ${state}`);
                        
                        // Handle different ICE connection states
                        switch(state) {
                            case 'checking':
                                updateConnectionStatus('🔄 Connecting...', 'yellow');
                                break;
                            case 'connected':
                                updateConnectionStatus('🟢 Connected', 'green');
                                if (loadingOverlay) {
                                    loadingOverlay.style.display = 'none';
                                }
                                break;
                            case 'disconnected':
                                updateConnectionStatus('🔴 Disconnected', 'red');
                                if (loadingOverlay) {
                                    loadingOverlay.style.display = 'flex';
                                }
                                break;
                            case 'failed':
                                updateConnectionStatus('❌ Connection Failed', 'red');
                                updateDebugInfo('ICE Connection failed - possible network issues');
                                // Attempt ICE restart
                                if (isStreamer) {
                                    updateDebugInfo('Attempting ICE restart...');
                                    peerConnection.createOffer({ iceRestart: true })
                                        .then(offer => {
                                            return peerConnection.setLocalDescription(offer);
                                        })
                                        .then(() => {
                                            socket.emit('signal', {
                                                streamId,
                                                signal: peerConnection.localDescription
                                            });
                                            updateDebugInfo('Sent ICE restart offer');
                                        })
                                        .catch(error => {
                                            updateDebugInfo(`ICE restart failed: ${error.message}`);
                                        });
                                }
                                break;
                        }
                    };

                    peerConnection.onconnectionstatechange = () => {
                        const state = peerConnection.connectionState;
                        updateDebugInfo(`Connection State changed to: ${state}`);
                        
                        // Handle different connection states
                        switch(state) {
                            case 'failed':
                                updateDebugInfo('Connection failed - attempting to restart...');
                                break;
                            case 'connected':
                                updateDebugInfo('Connection established successfully');
                                break;
                        }
                    };

                    peerConnection.onsignalingstatechange = () => {
                        updateDebugInfo(`Signaling State changed to: ${peerConnection.signalingState}`);
                    };

                    // Only set up negotiation handling for the streamer
                    if (isStreamer) {
                        peerConnection.onnegotiationneeded = () => {
                            // Skip negotiation if we're already negotiating or if we don't have a stable signaling state
                            if (isSettingRemoteDescription || peerConnection.signalingState !== 'stable') {
                                updateDebugInfo('Skipping negotiation - already in progress or unstable state');
                                return;
                            }

                            updateDebugInfo('Negotiation needed - creating new offer');
                            peerConnection.createOffer()
                                .then(offer => {
                                    return peerConnection.setLocalDescription(offer);
                                })
                                .then(() => {
                                    socket.emit('signal', {
                                        streamId,
                                        signal: peerConnection.localDescription
                                    });
                                    updateDebugInfo('Sent renegotiation offer');
                                })
                                .catch(error => {
                                    updateDebugInfo(`Negotiation failed: ${error.message}`);
                                });
                        };
                    }

                    if (isStreamer) {
                        try {
                            updateDebugInfo('Requesting media devices...');
                            localStream = await navigator.mediaDevices.getUserMedia({ 
                                video: {
                                    width: { ideal: 1280 },
                                    height: { ideal: 720 }
                                }, 
                                audio: true 
                            });
                            
                            const localVideo = document.getElementById('local-video');
                            if (localVideo) {
                                localVideo.srcObject = localStream;
                                updateDebugInfo('Local stream acquired and displayed');
                            } else {
                                updateDebugInfo('Warning: Local video element not found');
                            }

                            // Add all tracks at once to avoid multiple negotiations
                            const tracks = localStream.getTracks();
                            tracks.forEach(track => {
                                try {
                                    peerConnection.addTrack(track, localStream);
                                    updateDebugInfo(`Added ${track.kind} track to peer connection`);
                                    
                                    // Monitor track status
                                    track.onended = () => {
                                        updateDebugInfo(`${track.kind} track ended`);
                                    };
                                    track.onmute = () => {
                                        updateDebugInfo(`${track.kind} track muted`);
                                    };
                                    track.onunmute = () => {
                                        updateDebugInfo(`${track.kind} track unmuted`);
                                    };
                                } catch (error) {
                                    updateDebugInfo(`Error adding ${track.kind} track: ${error.message}`);
                                }
                            });

                            // Create initial offer after adding tracks
                            if (!isSettingRemoteDescription) {
                                const offer = await peerConnection.createOffer();
                                await peerConnection.setLocalDescription(offer);
                                socket.emit('signal', {
                                    streamId,
                                    signal: offer
                                });
                                updateDebugInfo('Sent initial offer');
                            }
                        } catch (error) {
                            updateDebugInfo(`Error accessing media devices: ${error.message}`);
                            console.error('Error accessing media devices:', error);
                            alert('Failed to access camera/microphone. Please check permissions and ensure no other application is using them.');
                            throw error;
                        }
                    }

                    peerConnection.onicecandidate = (event) => {
                        if (event.candidate) {
                            updateDebugInfo(`Sending ICE candidate: ${event.candidate.candidate}`);
                            socket.emit('signal', {
                                streamId,
                                signal: event.candidate
                            });
                        } else {
                            updateDebugInfo('Finished collecting ICE candidates');
                        }
                    };

                    peerConnection.onicecandidateerror = (event) => {
                        updateDebugInfo(`ICE candidate error: ${event.errorText} (${event.errorCode})`);
                    };

                    // Set up track handling for viewers
                    if (!isStreamer) {
                        peerConnection.ontrack = (event) => {
                            try {
                                updateDebugInfo(`Received ${event.track.kind} track`);
                                const remoteVideo = document.getElementById('remote-video');
                                if (remoteVideo) {
                                    if (remoteVideo.srcObject !== event.streams[0]) {
                                        remoteVideo.srcObject = event.streams[0];
                                        updateDebugInfo('Set remote stream to video element');
                                        
                                        // Monitor remote stream status
                                        event.streams[0].onremovetrack = () => {
                                            updateDebugInfo('Remote track removed from stream');
                                        };
                                        event.streams[0].onaddtrack = () => {
                                            updateDebugInfo('New track added to remote stream');
                                        };

                                        // Monitor track status
                                        event.track.onmute = () => {
                                            updateDebugInfo(`Remote ${event.track.kind} track muted`);
                                        };
                                        event.track.onunmute = () => {
                                            updateDebugInfo(`Remote ${event.track.kind} track unmuted`);
                                        };
                                        event.track.onended = () => {
                                            updateDebugInfo(`Remote ${event.track.kind} track ended`);
                                        };

                                        if (loadingOverlay) {
                                            loadingOverlay.style.display = 'none';
                                        }
                                    }
                                } else {
                                    updateDebugInfo('Warning: Remote video element not found');
                                }
                            } catch (error) {
                                updateDebugInfo(`Error handling remote track: ${error.message}`);
                                console.error('Error handling remote track:', error);
                            }
                        };
                    }

                    updateDebugInfo('WebRTC initialization completed');
                } catch (error) {
                    updateDebugInfo(`Failed to initialize WebRTC: ${error.message}`);
                    console.error('WebRTC initialization error:', error);
                    updateConnectionStatus('❌ Setup Failed', 'red');
                    throw error;
                }
            }

            // Stream control functions
            if (isStreamer) {
                const startStreamButton = document.getElementById('start-stream');
                const stopStreamButton = document.getElementById('stop-stream');

                if (startStreamButton) {
                    startStreamButton.addEventListener('click', async () => {
                        try {
                            updateDebugInfo('Starting stream...');
                            const response = await fetch(`/streams/${streamId}/start`, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                                }
                            });

                            if (!response.ok) {
                                throw new Error(`HTTP error! status: ${response.status}`);
                            }
                            
                            updateDebugInfo('Stream started on server');
                            startStreamButton.disabled = true;
                            stopStreamButton.disabled = false;
                            
                            await initWebRTC();
                            updateDebugInfo('WebRTC initialized');

                            socket.emit('stream-start', streamId);
                            updateDebugInfo('Emitted stream-start event');

                            const offer = await peerConnection.createOffer();
                            await peerConnection.setLocalDescription(offer);
                            socket.emit('signal', {
                                streamId,
                                signal: offer
                            });
                            updateDebugInfo('Sent offer');
                        } catch (error) {
                            updateDebugInfo(`Error starting stream: ${error.message}`);
                            console.error('Error starting stream:', error);
                            alert('Failed to start stream: ' + error.message);
                            startStreamButton.disabled = false;
                            stopStreamButton.disabled = true;
                        }
                    });
                }

                if (stopStreamButton) {
                    stopStreamButton.addEventListener('click', async () => {
                        try {
                            updateDebugInfo('Stopping stream...');
                            await fetch(`/streams/${streamId}/stop`, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                                }
                            });
                            
                            startStreamButton.disabled = false;
                            stopStreamButton.disabled = true;
                            
                            socket.emit('stream-stop', streamId);
                            updateDebugInfo('Emitted stream-stop event');

                            if (localStream) {
                                localStream.getTracks().forEach(track => track.stop());
                                updateDebugInfo('Stopped local media tracks');
                            }
                            if (peerConnection) {
                                peerConnection.close();
                                updateDebugInfo('Closed peer connection');
                            }
                        } catch (error) {
                            updateDebugInfo(`Error stopping stream: ${error.message}`);
                            console.error('Error stopping stream:', error);
                            alert('Failed to stop stream: ' + error.message);
                        }
                    });
                }
            } else {
                // Viewer initialization
                updateDebugInfo('Initializing viewer connection...');
                initWebRTC().then(async () => {
                    try {
                        const offer = await peerConnection.createOffer();
                        await peerConnection.setLocalDescription(offer);
                        socket.emit('signal', {
                            streamId,
                            signal: offer
                        });
                        updateDebugInfo('Sent viewer offer');
                    } catch (error) {
                        updateDebugInfo(`Error creating offer: ${error.message}`);
                        console.error('Error creating offer:', error);
                    }
                }).catch(error => {
                    updateDebugInfo(`Error initializing viewer WebRTC: ${error.message}`);
                    console.error('Error initializing viewer WebRTC:', error);
                });
            }
        });
    </script>
    @endpush
</x-app-layout> 