<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Earning;
use App\Models\Withdrawal;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class RevenueController extends Controller
{
    /**
     * Get user's revenue summary and details
     */
    public function getRevenue(Request $request)
    {
        $user = Auth::user();
        
        // Get filter parameters
        $month = $request->input('month');
        $year = $request->input('year');
        $source = $request->input('source');
        
        // Build query for earnings
        $earningsQuery = Earning::where('creator_id', $user->id);
        
        // Apply filters if provided
        if ($month && $year) {
            $earningsQuery->whereMonth('earned_at', $month)
                         ->whereYear('earned_at', $year);
        }
        
        if ($source) {
            $earningsQuery->where('source', $source);
        }
        
        // Get earnings with pagination
        $earnings = $earningsQuery->with(['sourcePost', 'sourceSubscription.subscriber'])
                                 ->orderBy('earned_at', 'desc')
                                 ->paginate(15);
        
        // Calculate summary statistics
        $totalEarned = Earning::where('creator_id', $user->id)->sum('amount');
        $totalWithdrawn = Withdrawal::where('user_id', $user->id)
                                    ->whereIn('status', ['completed', 'processing'])
                                    ->sum('amount');
        $availableBalance = $user->creator_balance;
        
        // Get next payout date (1st of next month)
        $nextPayoutDate = now()->addMonthNoOverflow()->startOfMonth();
        
        // Get recent withdrawals
        $recentWithdrawals = Withdrawal::where('user_id', $user->id)
                                      ->orderBy('created_at', 'desc')
                                      ->limit(5)
                                      ->get();
        
        // Get monthly earnings breakdown
        $monthlyEarnings = Earning::where('creator_id', $user->id)
            ->select(DB::raw('YEAR(earned_at) as year, MONTH(earned_at) as month, SUM(amount) as total'))
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->limit(12)
            ->get()
            ->map(function($item) {
                $monthName = date('F', mktime(0, 0, 0, $item->month, 1));
                return [
                    'month' => $monthName . ' ' . $item->year,
                    'total' => $item->total
                ];
            });
        
        // Get current month's earnings
        $currentMonthEarnings = Earning::where('creator_id', $user->id)
                                      ->whereMonth('earned_at', now()->month)
                                      ->whereYear('earned_at', now()->year)
                                      ->sum('amount');
        
        return response()->json([
            'success' => true,
            'data' => [
                'summary' => [
                    'total_earned' => $totalEarned,
                    'total_withdrawn' => $totalWithdrawn,
                    'available_balance' => $availableBalance,
                    'next_payout_date' => $nextPayoutDate->format('F j, Y'),
                    'can_withdraw' => $user->canWithdraw(),
                    'current_month_earnings' => $currentMonthEarnings
                ],
                'earnings' => $earnings,
                'recent_withdrawals' => $recentWithdrawals,
                'monthly_breakdown' => $monthlyEarnings
            ]
        ]);
    }
    
    /**
     * Request a withdrawal
     */
    public function requestWithdrawal(Request $request)
    {
        $user = Auth::user();
        
        // Check if user can withdraw
        if (!$user->canWithdraw()) {
            return response()->json([
                'success' => false,
                'message' => 'You need at least $5.00 to request a withdrawal.'
            ], 400);
        }
        
        // Check if there's already a pending withdrawal
        $pendingWithdrawal = Withdrawal::where('user_id', $user->id)
                                      ->where('status', 'pending')
                                      ->exists();
        
        if ($pendingWithdrawal) {
            return response()->json([
                'success' => false,
                'message' => 'You already have a pending withdrawal request.'
            ], 400);
        }
        
        // Create withdrawal request
        $withdrawal = Withdrawal::create([
            'user_id' => $user->id,
            'amount' => $user->creator_balance,
            'status' => 'pending'
        ]);
        
        // Update user's balance
        $user->creator_balance = 0;
        $user->save();
        
        // TODO: Send email notification
        
        return response()->json([
            'success' => true,
            'message' => 'Withdrawal request submitted successfully.',
            'data' => $withdrawal
        ]);
    }
} 