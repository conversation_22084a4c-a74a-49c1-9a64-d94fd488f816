@extends('layouts.app')

@section('content')
<meta name="signaling-server" content="{{ env('SIGNALING_SERVER_URL', 'http://localhost:3000') }}">
<div class="container">
    <div class="connection-status" id="connection-status">
        <span class="status-indicator"></span>
        <span class="status-text">Connecting...</span>
    </div>

    @if($stream->is_live)
        <div class="live-container">
            <!-- Streamer View -->
            @if(Auth::id() === $stream->user_id)
                <div class="streamer-overlay">
                    <div class="live-badge">
                        <div class="pulsating-ring"></div>
                        LIVE
                    </div>
                    <div class="viewer-count">
                        <i class="fas fa-eye"></i>
                        <span id="viewerCount">{{ $stream->viewer_count }}</span>
                    </div>
                    <button class="btn btn-danger end-stream-btn" id="endStreamBtn">
                        <i class="fas fa-stop-circle"></i> End Stream
                    </button>
                </div>
                <video id="local-video" autoplay playsinline muted></video>
            @else
                <!-- Viewer View -->
                <video id="remote-video" autoplay playsinline></video>
            @endif

            <div class="viewer-ui">
                <div class="sidebar">
                    <div class="reaction-buttons">
                        <button class="heart-btn" id="send-heart"><i class="fas fa-heart"></i></button>
                        <button class="share-btn"><i class="fas fa-share"></i></button>
                    </div>
                </div>

                <div class="comment-section">
                    <div class="comments-container" id="chat-messages"></div>
                    <form id="chat-form" class="comment-input">
                        <input type="text" id="chat-message" placeholder="Add a comment..." maxlength="255">
                        <button type="submit" class="btn btn-primary">Send</button>
                    </form>
                </div>
            </div>
        </div>
    @else
        <div class="recording-player">
            <video src="{{ Storage::url($stream->recording_path) }}" controls class="w-100"></video>
        </div>
    @endif
</div>

@push('styles')
<style>
.container {
    padding-bottom: 0;
}

.connection-status {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 8px 15px;
    border-radius: 20px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    display: flex;
    align-items: center;
    gap: 8px;
    z-index: 1000;
}

.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: yellow;
}

.status-indicator.connected {
    background: #4CAF50;
}

.status-indicator.disconnected {
    background: #f44336;
}

.live-container {
    position: relative;
    background: #000;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    height: calc(100vh - 100px);
}

.streamer-overlay {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    position: absolute;
    top: 4px;
    left: 4px;
    z-index: 99;
}

.live-badge {
    margin: 10px 0 0 40px;
    color: white;
    display: flex;
    align-items: center;
    gap: 8px;
}

.pulsating-ring {
    width: 24px;
    height: 24px;
    border: 2px solid #e91e63;
    border-radius: 50%;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { transform: scale(0.9); opacity: 1; }
    100% { transform: scale(1.5); opacity: 0; }
}

#local-video, #remote-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.viewer-ui {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.sidebar {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: auto;
}

.comment-section {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,0.7);
    padding: 10px;
    pointer-events: auto;
}

.comments-container {
    max-height: 200px;
    overflow-y: auto;
    color: white;
    margin-bottom: 10px;
}

.chat-message {
    margin-bottom: 8px;
    padding: 4px 8px;
    border-radius: 4px;
    background: rgba(255,255,255,0.1);
}

#chat-form {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

#chat-message {
    flex: 1;
    background: rgba(255,255,255,0.1);
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
}

.reaction-buttons button {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    padding: 8px;
    cursor: pointer;
    transition: transform 0.2s;
}

.reaction-buttons button:hover {
    transform: scale(1.2);
}

.heart-btn {
    color: #e91e63;
}

.heart-animation {
    position: fixed;
    animation: float-up 2s ease-out forwards;
    color: #e91e63;
    font-size: 24px;
    pointer-events: none;
}

@keyframes float-up {
    0% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
    100% {
        transform: translateY(-100vh) scale(1.5);
        opacity: 0;
    }
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', async function() {
    const streamId = '{{ $stream->id }}';
    const isStreamer = {{ Auth::id() === $stream->user_id ? 'true' : 'false' }};
    const iceServers = {!! json_encode($stream->ice_servers) !!};
    
    // Connection status elements
    const statusIndicator = document.querySelector('.status-indicator');
    const statusText = document.querySelector('.status-text');
    
    // Update connection status
    function updateConnectionStatus(status, message) {
        statusIndicator.className = 'status-indicator ' + status;
        statusText.textContent = message;
        console.log('Connection status:', message);
    }

    // Initialize livestream with debug logging
    console.log('Initializing livestream:', {
        streamId,
        isStreamer,
        iceServers
    });

    const livestream = new LivestreamRealtime(streamId, isStreamer, iceServers);

    // Start streaming or join stream based on role
    try {
        if (isStreamer) {
            console.log('Starting stream...');
            await livestream.startStream();
            updateConnectionStatus('connected', 'Broadcasting');
        } else {
            console.log('Joining stream...');
            await livestream.joinStream();
            updateConnectionStatus('connected', 'Watching stream');
        }
    } catch (error) {
        console.error('Error initializing stream:', error);
        updateConnectionStatus('disconnected', 'Failed to initialize stream');
    }

    // Handle chat form submission
    const chatForm = document.getElementById('chat-form');
    const chatInput = document.getElementById('chat-message');
    const chatMessages = document.getElementById('chat-messages');

    if (chatForm) {
        chatForm.addEventListener('submit', (e) => {
            e.preventDefault();
            const message = chatInput.value.trim();
            if (!message) return;

            livestream.sendComment(message);
            chatInput.value = '';
        });
    }

    // Handle heart reactions
    const heartButton = document.getElementById('send-heart');
    if (heartButton) {
        heartButton.addEventListener('click', () => {
            livestream.sendHeart();
        });
    }

    // Handle end stream button
    const endStreamBtn = document.getElementById('endStreamBtn');
    if (endStreamBtn) {
        endStreamBtn.addEventListener('click', async () => {
            try {
                console.log('Ending stream... 1');
                await livestream.endStream();
                console.log('Ending stream... 2');
                const response = await fetch(`/live/${streamId}/end`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                });

                const result = await response.json();
                if (result.success) {
                    window.location.href = result.redirect_url;
                }
            } catch (error) {
                console.error('Error ending stream:', error);
            }
        });
    }

    // Listen for connection events
    livestream.socket.on('connect', () => {
        updateConnectionStatus('connected', 'Connected to server');
    });

    livestream.socket.on('disconnect', () => {
        updateConnectionStatus('disconnected', 'Disconnected from server');
    });

    livestream.socket.on('connection-success', (data) => {
        console.log('Connection success:', data);
        updateConnectionStatus('connected', 'Connected as ' + (isStreamer ? 'broadcaster' : 'viewer'));
    });

    livestream.socket.on('connect_error', (error) => {
        console.error('Connection error:', error);
        updateConnectionStatus('disconnected', 'Connection error');
    });

    // Add error handling for video elements
    const localVideo = document.getElementById('local-video');
    const remoteVideo = document.getElementById('remote-video');

    if (localVideo) {
        localVideo.onerror = (error) => {
            console.error('Local video error:', error);
            updateConnectionStatus('disconnected', 'Video error');
        };
    }

    if (remoteVideo) {
        remoteVideo.onerror = (error) => {
            console.error('Remote video error:', error);
            updateConnectionStatus('disconnected', 'Video error');
        };
    }
});
</script>
@endpush
@endsection
