# Laravel WebRTC Streaming Application

This is a backup of the WebRTC-based live streaming functionality implemented in Laravel. The application allows users to create, manage, and watch live video streams using WebRTC technology.

## Components

### Backend (Laravel)

- `app/Models/Stream.php`: Stream model defining the database structure and relationships
- `app/Http/Controllers/StreamController.php`: Controller handling stream creation, management, and viewing
- `database/migrations/*_create_streams_table.php`: Database migration for the streams table

### Frontend (Blade Views)

- `resources/views/streams/create.blade.php`: Stream creation form
- `resources/views/streams/index.blade.php`: List of available streams
- `resources/views/streams/show.blade.php`: Stream viewing/broadcasting interface

### WebRTC Signaling Server

- `signaling-server.js`: Node.js/Socket.IO server handling WebRTC signaling

## Features

1. Stream Management
   - Create new streams with title and description
   - Start/stop streaming
   - View active streams
   - Real-time viewer count

2. WebRTC Implementation
   - Peer-to-peer video streaming
   - ICE candidate negotiation
   - Connection state management
   - Automatic connection recovery

3. Security
   - Authentication required for streaming
   - Secure WebRTC connections
   - CSRF protection

## Technical Details

### Database Schema

The `streams` table includes:
- `id`: Stream identifier
- `user_id`: Broadcaster's user ID
- `title`: Stream title
- `description`: Stream description
- `stream_key`: Unique stream identifier
- `is_live`: Current streaming status
- `viewer_count`: Number of current viewers

### WebRTC Flow

1. Broadcaster initiates stream
2. Signaling server manages WebRTC handshake
3. Peer connections established between broadcaster and viewers
4. Video/audio tracks shared through peer connection
5. Connection monitoring and automatic recovery

### Dependencies

- Laravel (backend framework)
- Socket.IO (signaling server)
- WebRTC adapter (browser compatibility)

## Setup Instructions

1. Copy files to their respective locations in a Laravel project
2. Run database migrations
3. Install Node.js dependencies for signaling server
4. Start both Laravel and signaling servers:
   ```bash
   # Terminal 1 - Laravel server
   php artisan serve

   # Terminal 2 - Signaling server
   node signaling-server.js
   ```

## Notes

- Ensure proper CORS configuration for signaling server
- Configure STUN/TURN servers for production use
- Monitor WebRTC connection states for debugging
- Check browser console and debug panel for detailed logs 