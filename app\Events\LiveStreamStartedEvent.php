<?php

namespace App\Events;

use App\Models\LiveStream;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class LiveStreamStartedEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $stream;

    public function __construct(LiveStream $stream)
    {
        $this->stream = $stream;
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn()
    {
        return [
            new Channel('live-streams'), // Global channel for all users
            new Channel('user.' . $this->stream->user_id . '.followers') // Followers channel
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs()
    {
        return 'stream.started';
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith()
    {
        return [
            'id' => $this->stream->id,
            'title' => $this->stream->title,
            'user' => [
                'id' => $this->stream->user->id,
                'name' => $this->stream->user->name,
                'username' => $this->stream->user->username,
                'avatar' => $this->stream->user->avatar_url ?? asset('images/default.png')
            ],
            'started_at' => $this->stream->started_at,
            'stream_url' => route('livestream.show', $this->stream->id),
            'message' => $this->stream->user->name . ' started a live stream!'
        ];
    }
}
