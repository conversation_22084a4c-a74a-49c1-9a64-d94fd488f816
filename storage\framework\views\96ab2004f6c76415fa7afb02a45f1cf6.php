<?php $__env->startSection('content'); ?>
<style>
    .chat-container {
        height: 100vh;
        background: #000;
        color: white;
    }

    .chat-header {
        padding: 15px;
        background: #111;
        display: flex;
        align-items: center;
        gap: 15px;
        border-bottom: 1px solid #222;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .back-button {
        color: white;
        text-decoration: none;
        font-size: 20px;
    }

    .header-title {
        font-size: 18px;
        font-weight: 600;
    }

    .search-input {
        width: 100%;
        padding: 12px 40px;
        background: #222;
        border: none;
        border-radius: 25px;
        color: white;
        font-size: 16px;
    }

    .search-icon {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #666;
    }

    .chat-stories {
        padding: 10px;
        display: flex;
        gap: 15px;
        overflow-x: auto;
        border-bottom: 1px solid #222;
    }

    .story-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 5px;
    }

    .story-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid #FFD700;
        padding: 2px;
    }

    .story-username {
        font-size: 12px;
        color: #BBB;
        text-align: center;
    }

    .chat-list {
        padding: 10px;
    }

    .chat-item {
        display: flex;
        align-items: center;
        gap: 15px;
        padding: 10px;
        text-decoration: none;
        color: white;
    }

    .chat-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        object-fit: cover;
    }

    .chat-info {
        flex: 1;
    }

    .chat-name {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .chat-preview {
        color: #999;
        font-size: 14px;
    }

    .chat-meta {
        text-align: right;
    }

    .chat-time {
        color: #666;
        font-size: 12px;
        margin-bottom: 5px;
    }

    .unread-badge {
        background: #FFD700;
        color: black;
        padding: 2px 8px;
        border-radius: 10px;
        font-size: 12px;
        font-weight: bold;
    }

    .verified-badge {
        color: #FFD700;
        margin-left: 5px;
    }

    /* Search functionality */
    .search-bar {
        position: relative;
        padding-bottom: 10px;
        margin: 10px;
        border-bottom: 1px solid #57535333;
    }

    .search-results {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: #111;
        border: 1px solid #333;
        border-radius: 0 0 15px 15px;
        max-height: 300px;
        overflow-y: auto;
        z-index: 1000;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    }

    .search-result-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 15px;
        cursor: pointer;
        border-bottom: 1px solid #222;
        transition: background 0.2s ease;
        text-decoration: none;
        color: white;
    }

    .search-result-item:hover {
        background: #222;
    }

    .search-result-item:last-child {
        border-bottom: none;
    }

    .search-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
    }

    .search-live-avatar {
        border: 2px solid #FF3B30;
    }

    .search-info {
        flex: 1;
    }

    .search-username {
        font-weight: 600;
        margin-bottom: 2px;
        display: flex;
        align-items: center;
        gap: 6px;
    }

    .search-status {
        font-size: 12px;
        color: #999;
    }

    .search-live-badge {
        background: #FF3B30;
        color: white;
        padding: 2px 6px;
        border-radius: 8px;
        font-size: 10px;
        font-weight: bold;
    }

    .in-app-notification .notification-content {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .in-app-notification .notification-avatar img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
    }

    .in-app-notification .notification-text {
        flex: 1;
    }

    .in-app-notification .notification-title {
        font-weight: bold;
        font-size: 14px;
        margin-bottom: 2px;
    }

    .in-app-notification .notification-message {
        font-size: 12px;
        color: #ccc;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 200px;
    }

    .in-app-notification .notification-close {
        background: none;
        border: none;
        color: #999;
        font-size: 18px;
        cursor: pointer;
        padding: 0;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .in-app-notification .notification-close:hover {
        color: white;
    }

    .no-results {
        padding: 20px;
        text-align: center;
        color: #666;
    }

    /* Live Users Section */
    .live-users-section {
        padding: 15px;
        border-bottom: 1px solid #222;
        background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
    }

    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .section-header h3 {
        color: #FF3B30;
        margin: 0;
        font-size: 16px;
        font-weight: 600;
    }

    .section-header small {
        color: #999;
        font-size: 12px;
    }

    .live-users-list {
        display: flex;
        gap: 15px;
        overflow-x: auto;
        padding-bottom: 10px;
    }

    .live-user-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        min-width: 80px;
        text-decoration: none;
        color: white;
        transition: transform 0.2s ease;
    }

    .live-user-item:hover {
        transform: scale(1.05);
    }

    .live-user-avatar-container {
        position: relative;
        margin-bottom: 8px;
    }

    .live-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid #FF3B30;
        animation: pulse-live 2s infinite;
    }

    @keyframes pulse-live {
        0% { border-color: #FF3B30; }
        50% { border-color: #FF6B60; }
        100% { border-color: #FF3B30; }
    }

    .live-indicator {
        position: absolute;
        bottom: -5px;
        left: 50%;
        transform: translateX(-50%);
        background: #FF3B30;
        color: white;
        padding: 2px 6px;
        border-radius: 8px;
        font-size: 10px;
        font-weight: bold;
    }

    .live-user-info {
        text-align: center;
    }

    .live-username {
        font-size: 12px;
        font-weight: 600;
        margin-bottom: 2px;
    }

    .live-stream-title {
        font-size: 10px;
        color: #999;
        margin-bottom: 2px;
    }

    .live-viewer-count {
        font-size: 10px;
        color: #666;
    }

    /* Enhanced story avatars */
    .story-avatar-container {
        position: relative;
    }

    .story-live-badge {
        position: absolute;
        top: -2px;
        right: -2px;
        background: #FF3B30;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 8px;
        border: 2px solid #000;
    }

    /* Live status in chat list */
    .live-status {
        background: #FF3B30;
        color: white;
        padding: 2px 6px;
        border-radius: 10px;
        font-size: 10px;
        font-weight: bold;
        margin-left: 8px;
        animation: pulse-text 2s infinite;
    }

    .recent-status {
        background: #4CAF50;
        color: white;
        padding: 2px 6px;
        border-radius: 10px;
        font-size: 10px;
        font-weight: bold;
        margin-left: 8px;
    }

    @keyframes pulse-text {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
    }

    .story-online-badge {
        position: absolute;
        top: -2px;
        right: -2px;
        background: #4CAF50;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 8px;
        border: 2px solid #000;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .live-users-list {
            gap: 10px;
        }

        .live-user-item {
            min-width: 70px;
        }

        .live-avatar {
            width: 50px;
            height: 50px;
        }
    }
</style>

<div class="chat-container">

    <!-- Live Users Section -->
    <?php if(count($liveUsers) > 0): ?>
        <div class="live-users-section">
            <div class="section-header">
                <h3>🔴 Currently Live</h3>
                <small><?php echo e(count($liveUsers)); ?> users streaming</small>
            </div>
            <div class="live-users-list">
                <?php $__currentLoopData = $liveUsers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $liveUser): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <a href="<?php echo e(route('chat.show', $liveUser->id)); ?>" class="live-user-item">
                        <div class="live-user-avatar-container">
                            <?php if($liveUser->photo): ?>
                                <img src="<?php echo e(asset('storage/' . $liveUser->photo)); ?>" alt="<?php echo e($liveUser->username); ?>" class="live-avatar">
                            <?php else: ?>
                                <img src="/images/default-avatar.png" alt="<?php echo e($liveUser->username); ?>" class="live-avatar">
                            <?php endif; ?>
                            <div class="live-indicator">LIVE</div>
                        </div>
                        <div class="live-user-info">
                            <div class="live-username"><?php echo e($liveUser->username); ?></div>
                            <div class="live-stream-title"><?php echo e(Str::limit($liveUser->stream_title ?? 'Live Stream', 25)); ?></div>
                            <div class="live-viewer-count">👥 <?php echo e($liveUser->viewer_count ?? 0); ?></div>
                        </div>
                    </a>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    <?php endif; ?>

    <div class="search-bar">
        <i class="fas fa-search search-icon"></i>
        <input type="text" class="search-input" id="search-input" placeholder="Search for chats and users...">
        <div class="search-results" id="search-results" style="display: none;"></div>
    </div>

    <!-- Recent Chats Stories -->
    <!-- <div class="chat-stories">
        <?php $__currentLoopData = $recentChats->take(8); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $chat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="story-item">
                <div class="story-avatar-container">
                    <?php if($chat->photo): ?>
                        <img src="<?php echo e(asset('storage/' . $chat->photo)); ?>" alt="<?php echo e($chat->username); ?>" class="story-avatar">
                    <?php else: ?>
                        <img src="/images/default-avatar.png" alt="<?php echo e($chat->username); ?>" class="story-avatar">
                    <?php endif; ?>
                    <?php if($chat->initial_status === 'live'): ?>
                        <div class="story-live-badge">🔴</div>
                    <?php endif; ?>
                </div>
                <div class="story-username"><?php echo e($chat->username); ?></div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div> -->

    <div class="chat-list">
        <?php if($recentChats->isEmpty()): ?>
            <div style="padding: 20px; color: #666; text-align: center;">
                No recent chats found. Start a conversation!
            </div>
        <?php endif; ?>
        <?php $__currentLoopData = $recentChats; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $chat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <a href="<?php echo e(route('chat.show', $chat->id)); ?>" class="chat-item">
                <?php if($chat->photo): ?>
                    <img src="<?php echo e(asset('storage/' . $chat->photo)); ?>" alt="<?php echo e($chat->username); ?>" class="chat-avatar">
                <?php else: ?>
                    <img src="/images/default-avatar.png" alt="<?php echo e($chat->username); ?>" class="chat-avatar">
                <?php endif; ?>

                <div class="chat-info">
                    <div class="chat-name">
                        <?php echo e($chat->name ?? $chat->username); ?>

                        <?php if($chat->is_verified): ?>
                            <i class="fas fa-check-circle verified-badge"></i>
                        <?php endif; ?>
                        <?php if($chat->initial_status === 'live'): ?>
                            <span class="live-status">🔴 LIVE</span>
                        <?php endif; ?>
                        
                    </div>
                    <div class="chat-preview">
                        <?php if($chat->initial_status === 'live' && $chat->stream_title): ?>
                            📺 <?php echo e(Str::limit($chat->stream_title, 30)); ?>

                        <?php else: ?>
                            <?php echo e(Str::limit($chat->message ?? 'No messages yet', 30)); ?>

                        <?php endif; ?>
                    </div>
                </div>

                <div class="chat-meta">
                    <div class="chat-time"><?php echo e(\Carbon\Carbon::parse($chat->created_at)->diffForHumans(null, true)); ?></div>
                    <?php if($chat->unread_count > 0): ?>
                        <div class="unread-badge"><?php echo e($chat->unread_count); ?></div>
                    <?php endif; ?>
                </div>
            </a>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</div>

<script src="https://cdn.socket.io/4.5.0/socket.io.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Socket.IO for user status
    const signalingServerUrl = "<?php echo e(env('SIGNALING_SERVER_URL')); ?>";
    const currentUser = <?php echo json_encode([
        'id' => auth()->id(), 'username' => auth()->user()->username ?? 'Guest'
    ], 512) ?>;

    let socket;
    const onlineUsersData = new Map();

    try {
        socket = io(signalingServerUrl);
        console.log('Socket.IO initialized for chat status');
        console.log('Current user data:', currentUser);

        socket.on('connect', () => {
            console.log('Connected to status server');

            // Emit that user is online on chat index page
            if (currentUser && currentUser.id) {
                socket.emit('user-online', {
                    userId: currentUser.id.toString(),
                    username: currentUser.username || 'Guest',
                    currentPage: '/chats'
                });
            }
        });

        // Listen for user status updates
        socket.on('user-status-update', (data) => {
            console.log('User status update:', data);
            updateUserStatusInUI(data);
        });

        // Listen for new chat notifications
        socket.on('new-chat-notification', (data) => {
            console.log('New chat notification received:', data);
            showChatNotification(data);
            updateChatListWithNewMessage(data);
        });

        // Listen for online users list
        socket.on('online-users-list', (data) => {
            console.log('Online users received:', data.onlineUsers);
            data.onlineUsers.forEach(user => {
                onlineUsersData.set(user.userId, user);
                updateUserStatusInUI(user);
            });
        });

        // Handle page unload (user leaving)
        window.addEventListener('beforeunload', () => {
            if (currentUser && currentUser.id) {
                socket.emit('user-offline', {
                    userId: currentUser.id.toString()
                });
            }
        });

        // Handle visibility change (tab switching)
        document.addEventListener('visibilitychange', () => {
            if (currentUser && currentUser.id) {
                if (document.hidden) {
                    socket.emit('user-offline', {
                        userId: currentUser.id.toString()
                    });
                } else {
                    socket.emit('user-online', {
                        userId: currentUser.id.toString(),
                        username: currentUser.username || 'Guest',
                        currentPage: '/chats'
                    });
                }
            }
        });

    } catch (error) {
        console.error('Socket.IO initialization failed:', error);
    }

    // Function to update user status in UI
    function updateUserStatusInUI(userData) {
        const userId = userData.userId;
        const status = userData.status;

        // Update chat items
        const chatItems = document.querySelectorAll(`a[href="/chats/${userId}"]`);
        chatItems.forEach(chatItem => {
            const statusElement = chatItem.querySelector('.live-status, .recent-status');
            const nameElement = chatItem.querySelector('.chat-name');

            if (statusElement) {
                statusElement.remove();
            }

            if (status === 'online' && nameElement) {
                const onlineSpan = document.createElement('span');
                onlineSpan.className = 'recent-status';
                onlineSpan.innerHTML = '🟢 Online';
                nameElement.appendChild(onlineSpan);
            }
        });

                 // Update story avatars
        const storyItems = document.querySelectorAll('.story-item');
        storyItems.forEach(storyItem => {
            const avatarContainer = storyItem.querySelector('.story-avatar-container');
            const usernameElement = storyItem.querySelector('.story-username');

            if (usernameElement && userData.username === usernameElement.textContent.trim()) {
                const existingBadge = avatarContainer.querySelector('.story-online-badge');
                if (existingBadge) {
                    existingBadge.remove();
                }

                if (status === 'online') {
                    const onlineBadge = document.createElement('div');
                    onlineBadge.className = 'story-online-badge';
                    onlineBadge.innerHTML = '🟢';
                    avatarContainer.appendChild(onlineBadge);
                }
            }
        });

        // Update search results if visible
        const searchResultItems = document.querySelectorAll(`[data-user-id="${userId}"]`);
        searchResultItems.forEach(searchItem => {
            const statusElement = searchItem.querySelector('.search-live-badge[style*="background: #4CAF50"]');
            const usernameElement = searchItem.querySelector('.search-username');

            if (statusElement) {
                statusElement.remove();
            }

            if (status === 'online' && usernameElement) {
                const onlineSpan = document.createElement('span');
                onlineSpan.className = 'search-live-badge';
                onlineSpan.style.background = '#4CAF50';
                onlineSpan.innerHTML = '🟢 Online';
                usernameElement.appendChild(onlineSpan);
            }
        });
     }

    // Original search functionality
    const searchInput = document.getElementById('search-input');
    const searchResults = document.getElementById('search-results');
    let searchTimeout;

    searchInput.addEventListener('input', function() {
        const query = this.value.trim();

        // Clear previous timeout
        clearTimeout(searchTimeout);

        if (query.length < 2) {
            searchResults.style.display = 'none';
            return;
        }

        // Debounce search
        searchTimeout = setTimeout(() => {
            performSearch(query);
        }, 300);
    });

    searchInput.addEventListener('blur', function() {
        // Hide results after a short delay to allow clicking
        setTimeout(() => {
            searchResults.style.display = 'none';
        }, 200);
    });

    searchInput.addEventListener('focus', function() {
        if (this.value.trim().length >= 2) {
            searchResults.style.display = 'block';
        }
    });

    async function performSearch(query) {
        try {
            const response = await fetch(`/api/chat/search-users?q=${encodeURIComponent(query)}`, {
                headers: {
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            });

            const data = await response.json();

            if (data.success) {
                displaySearchResults(data.users);
            } else {
                showNoResults();
            }
        } catch (error) {
            console.error('Search error:', error);
            showNoResults();
        }
    }

    function displaySearchResults(users) {
        if (users.length === 0) {
            showNoResults();
            return;
        }

        const html = users.map(user => {
            const avatarSrc = user.photo || '/images/default-avatar.png';
            const liveClass = user.is_live ? 'search-live-avatar' : '';
            const liveBadge = user.is_live ? '<span class="search-live-badge">🔴 LIVE</span>' : '';
            const verifiedBadge = user.is_verified ? '<i class="fas fa-check-circle" style="color: #FFD700;"></i>' : '';

            // Check if user is online in our real-time data
            const onlineUser = onlineUsersData.get(user.id);
            const onlineBadge = onlineUser && onlineUser.status === 'online' ? '<span class="search-live-badge" style="background: #4CAF50;">🟢 Online</span>' : '';

            return `
                <a href="/chats/${user.id}" class="search-result-item" data-user-id="${user.id}">
                    <img src="${avatarSrc}" alt="${user.username}" class="search-avatar ${liveClass}">
                    <div class="search-info">
                        <div class="search-username">
                            ${user.name || user.username}
                            ${verifiedBadge}
                            ${liveBadge || onlineBadge}
                        </div>
                        <div class="search-status">@${user.username}</div>
                    </div>
                </a>
            `;
        }).join('');

        searchResults.innerHTML = html;
        searchResults.style.display = 'block';
    }

    function showNoResults() {
        searchResults.innerHTML = '<div class="no-results">No users found</div>';
        searchResults.style.display = 'block';
    }

    // Load unread count periodically
    function updateUnreadCount() {
        fetch('/api/chat/unread-count', {
            headers: {
                'Accept': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.unread_count > 0) {
                // Update UI with unread count if needed
                console.log('Unread messages:', data.unread_count);
            }
        })
        .catch(error => console.error('Error fetching unread count:', error));
    }

    // Update unread count every 30 seconds
    setInterval(updateUnreadCount, 30000);
    updateUnreadCount(); // Initial load

    // Function to show chat notification
    function showChatNotification(data) {
        // Request notification permission if not granted
        if ('Notification' in window) {
            if (Notification.permission === 'granted') {
                const notification = new Notification(`New message from ${data.sender_username}`, {
                    body: data.message,
                    icon: data.sender_avatar || '/images/default-avatar.png',
                    tag: `chat-${data.sender_id}`,
                    requireInteraction: false
                });

                notification.onclick = function() {
                    window.focus();
                    window.location.href = data.chat_url;
                    notification.close();
                };

                // Auto close after 5 seconds
                setTimeout(() => notification.close(), 5000);
            } else if (Notification.permission !== 'denied') {
                Notification.requestPermission().then(permission => {
                    if (permission === 'granted') {
                        showChatNotification(data);
                    }
                });
            }
        }

        // Show in-app notification as fallback
        showInAppNotification(data);
    }

    // Function to show in-app notification
    function showInAppNotification(data) {
        const notification = document.createElement('div');
        notification.className = 'in-app-notification';
        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-avatar">
                    <img src="${data.sender_avatar || '/images/default-avatar.png'}" alt="${data.sender_username}">
                </div>
                <div class="notification-text">
                    <div class="notification-title">${data.sender_username}</div>
                    <div class="notification-message">${data.message}</div>
                </div>
                <button class="notification-close">&times;</button>
            </div>
        `;

        // Add styles
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #222;
            color: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 10000;
            max-width: 300px;
            cursor: pointer;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Click to open chat
        notification.addEventListener('click', (e) => {
            if (!e.target.classList.contains('notification-close')) {
                window.location.href = data.chat_url;
            }
        });

        // Close button
        notification.querySelector('.notification-close').addEventListener('click', (e) => {
            e.stopPropagation();
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => notification.remove(), 300);
        });

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => notification.remove(), 300);
            }
        }, 5000);
    }

    // Function to update chat list with new message
    function updateChatListWithNewMessage(data) {
        const chatItems = document.querySelectorAll('.chat-item');
        let chatFound = false;

        chatItems.forEach(chatItem => {
            const href = chatItem.getAttribute('href');
            if (href && href.includes(`/chats/${data.sender_id}`)) {
                chatFound = true;

                // Update last message
                const messageElement = chatItem.querySelector('.chat-message');
                if (messageElement) {
                    messageElement.textContent = data.message;
                }

                // Update time
                const timeElement = chatItem.querySelector('.chat-time');
                if (timeElement) {
                    timeElement.textContent = 'now';
                }

                // Add or update unread badge
                let unreadBadge = chatItem.querySelector('.unread-badge');
                if (!unreadBadge) {
                    unreadBadge = document.createElement('div');
                    unreadBadge.className = 'unread-badge';
                    const chatMeta = chatItem.querySelector('.chat-meta');
                    if (chatMeta) {
                        chatMeta.appendChild(unreadBadge);
                    }
                }

                const currentCount = parseInt(unreadBadge.textContent) || 0;
                unreadBadge.textContent = currentCount + 1;

                // Move to top of list
                const chatList = chatItem.parentNode;
                chatList.insertBefore(chatItem, chatList.firstChild);
            }
        });

        // If chat not found in list, we could optionally add it
        // For now, we'll just log it
        if (!chatFound) {
            console.log('New chat from user not in current list:', data.sender_username);
        }
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\instapwa\resources\views/chat/index.blade.php ENDPATH**/ ?>