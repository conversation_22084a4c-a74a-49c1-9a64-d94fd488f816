@extends('layouts.app')

@section('content')
<div class="stream-ended-container">
    <!-- Header -->
    <div class="ended-header">
        <button class="back-btn" onclick="window.history.back()">
            <i class="fas fa-arrow-left"></i>
        </button>
        <h1 class="ended-title">Live Stream Ended</h1>
        <div class="share-btn">
            <i class="fas fa-share"></i>
        </div>
    </div>

    <!-- Video Player -->
    <div class="video-player-container">
        @if($stream->recording_path)
            <video id="streamVideo" controls poster="{{ asset('images/stream-poster.jpg') }}">
                <source src="{{ asset('storage/' . $stream->recording_path) }}" type="video/webm">
                <source src="{{ asset('storage/' . $stream->recording_path) }}" type="video/mp4">
                Your browser does not support the video tag.
            </video>
            <div class="video-overlay">
                <div class="play-btn" id="playBtn">
                    <i class="fas fa-play"></i>
                </div>
            </div>
        @else
            <div class="no-recording">
                <i class="fas fa-video-slash"></i>
                <h3>No Recording Available</h3>
                <p>This stream was not recorded or the recording failed.</p>
            </div>
        @endif
    </div>

    <!-- Stream Info -->
    <div class="stream-info-section">
        <div class="streamer-info">
            @if($stream->user->profile && $stream->user->profile->photo)
                <img src="{{ Storage::disk('public')->url($stream->user->profile->photo) }}" alt="{{ $stream->user->name }}" class="streamer-avatar">
            @else
                <img src="/images/default.png" alt="{{ $stream->user->name }}" class="streamer-avatar">
            @endif
            
            <div class="streamer-details">
                <h3 class="streamer-name">{{ $stream->user->name }}</h3>
                <p class="stream-title">{{ $stream->title }}</p>
                <p class="stream-description">{{ $stream->description }}</p>
            </div>
        </div>

        <!-- Stream Stats -->
        <div class="stream-stats">
            <div class="stat-item">
                <i class="fas fa-eye"></i>
                <span class="stat-label">Peak Viewers</span>
                <span class="stat-value">{{ $stream->viewer_count }}</span>
            </div>
            
            <div class="stat-item">
                <i class="fas fa-clock"></i>
                <span class="stat-label">Duration</span>
                <span class="stat-value">{{ $stream->formatted_duration }}</span>
            </div>
            
            <div class="stat-item">
                <i class="fas fa-calendar"></i>
                <span class="stat-label">Streamed</span>
                <span class="stat-value">{{ $stream->started_at->format('M j, Y') }}</span>
            </div>
            
            <div class="stat-item">
                <i class="fas fa-comments"></i>
                <span class="stat-label">Comments</span>
                <span class="stat-value">{{ $stream->comments->count() ?? 0 }}</span>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
        @if(Auth::id() === $stream->user_id)
            <button class="action-btn primary" onclick="shareStream()">
                <i class="fas fa-share"></i>
                Share Recording
            </button>
            
            @if($stream->recording_path)
                <a href="{{ asset('storage/' . $stream->recording_path) }}" download class="action-btn secondary">
                    <i class="fas fa-download"></i>
                    Download
                </a>
            @endif
            
            <button class="action-btn tertiary" onclick="deleteStream()">
                <i class="fas fa-trash"></i>
                Delete
            </button>
        @else
            <button class="action-btn primary" onclick="followStreamer()">
                <i class="fas fa-user-plus"></i>
                Follow {{ $stream->user->name }}
            </button>
            
            <button class="action-btn secondary" onclick="shareStream()">
                <i class="fas fa-share"></i>
                Share
            </button>
        @endif
    </div>

    <!-- Related Streams -->
    @if($stream->user->liveStreams()->where('id', '!=', $stream->id)->where('has_recording', true)->count() > 0)
    <div class="related-streams">
        <h3>More from {{ $stream->user->name }}</h3>
        <div class="related-grid">
            @foreach($stream->user->liveStreams()->where('id', '!=', $stream->id)->where('has_recording', true)->orderBy('started_at', 'desc')->take(4)->get() as $relatedStream)
                <div class="related-item" onclick="viewStream({{ $relatedStream->id }})">
                    <div class="related-thumbnail">
                        @if($relatedStream->recording_path)
                            <video preload="metadata">
                                <source src="{{ asset('storage/' . $relatedStream->recording_path) }}#t=1" type="video/webm">
                            </video>
                        @else
                            <div class="no-thumbnail">
                                <i class="fas fa-play"></i>
                            </div>
                        @endif
                        <div class="duration-badge">{{ $relatedStream->formatted_duration }}</div>
                    </div>
                    <div class="related-info">
                        <p class="related-title">{{ Str::limit($relatedStream->title, 40) }}</p>
                        <p class="related-date">{{ $relatedStream->started_at->diffForHumans() }}</p>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
    @endif
</div>

<style>
.stream-ended-container {
    max-width: 1200px;
    margin: 0 auto;
    background: #111;
    color: white;
    min-height: 100vh;
}

.ended-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    background: #000;
    border-bottom: 1px solid #333;
}

.back-btn, .share-btn {
    width: 40px;
    height: 40px;
    background: #333;
    border: none;
    border-radius: 50%;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background 0.3s;
}

.back-btn:hover, .share-btn:hover {
    background: #555;
}

.ended-title {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
}

.video-player-container {
    position: relative;
    aspect-ratio: 16/9;
    background: #000;
    margin: 20px;
    border-radius: 12px;
    overflow: hidden;
}

#streamVideo {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0,0,0,0.3);
    opacity: 1;
    transition: opacity 0.3s;
}

.video-overlay.hidden {
    opacity: 0;
    pointer-events: none;
}

.play-btn {
    width: 80px;
    height: 80px;
    background: rgba(255,255,255,0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    color: #000;
    cursor: pointer;
    transition: all 0.3s;
}

.play-btn:hover {
    transform: scale(1.1);
    background: white;
}

.no-recording {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #666;
}

.no-recording i {
    font-size: 4rem;
    margin-bottom: 20px;
}

.stream-info-section {
    padding: 20px;
}

.streamer-info {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 30px;
}

.streamer-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #333;
}

.streamer-name {
    font-size: 20px;
    font-weight: 600;
    margin: 0 0 5px 0;
}

.stream-title {
    font-size: 16px;
    color: #ccc;
    margin: 0 0 8px 0;
}

.stream-description {
    font-size: 14px;
    color: #999;
    margin: 0;
    line-height: 1.4;
}

.stream-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    background: #1a1a1a;
    padding: 20px;
    border-radius: 12px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 8px;
}

.stat-item i {
    font-size: 24px;
    color: #ff3040;
}

.stat-label {
    font-size: 12px;
    color: #999;
    text-transform: uppercase;
    font-weight: 500;
}

.stat-value {
    font-size: 18px;
    font-weight: 600;
    color: white;
}

.action-buttons {
    display: flex;
    gap: 15px;
    padding: 20px;
    flex-wrap: wrap;
    justify-content: center;
}

.action-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s;
}

.action-btn.primary {
    background: #ff3040;
    color: white;
}

.action-btn.secondary {
    background: #333;
    color: white;
}

.action-btn.tertiary {
    background: transparent;
    color: #999;
    border: 1px solid #333;
}

.action-btn:hover {
    transform: translateY(-2px);
}

.related-streams {
    padding: 20px;
    border-top: 1px solid #333;
}

.related-streams h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
}

.related-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
}

.related-item {
    cursor: pointer;
    transition: transform 0.3s;
}

.related-item:hover {
    transform: translateY(-5px);
}

.related-thumbnail {
    position: relative;
    aspect-ratio: 16/9;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 8px;
}

.related-thumbnail video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-thumbnail {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #333;
    color: #666;
    font-size: 2rem;
}

.duration-badge {
    position: absolute;
    bottom: 8px;
    right: 8px;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
}

.related-title {
    font-weight: 500;
    margin: 0 0 4px 0;
    line-height: 1.3;
}

.related-date {
    font-size: 12px;
    color: #999;
    margin: 0;
}

@media (max-width: 768px) {
    .stream-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .related-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const video = document.getElementById('streamVideo');
    const playBtn = document.getElementById('playBtn');
    const overlay = document.querySelector('.video-overlay');

    if (video && playBtn) {
        playBtn.addEventListener('click', function() {
            video.play();
            overlay.classList.add('hidden');
        });

        video.addEventListener('pause', function() {
            overlay.classList.remove('hidden');
        });

        video.addEventListener('ended', function() {
            overlay.classList.remove('hidden');
        });
    }
});

function shareStream() {
    if (navigator.share) {
        navigator.share({
            title: '{{ $stream->title }}',
            text: 'Check out this live stream recording!',
            url: window.location.href
        });
    } else {
        // Fallback - copy to clipboard
        navigator.clipboard.writeText(window.location.href);
        alert('Link copied to clipboard!');
    }
}

function followStreamer() {
    // Implement follow functionality
    alert('Follow feature coming soon!');
}

function deleteStream() {
    if (confirm('Are you sure you want to delete this stream recording? This action cannot be undone.')) {
        // Implement delete functionality
        alert('Delete feature coming soon!');
    }
}

function viewStream(streamId) {
    window.location.href = `/live/${streamId}/ended`;
}
</script>
@endsection 