@extends('layouts.app')

@section('content')
<div class="saved-streams-container">
    <div class="header">
        <h1>📺 Saved Live Streams</h1>
        <p>Watch previous live stream recordings</p>
    </div>

    @if($savedStreams->count() > 0)
        <div class="streams-grid">
            @foreach($savedStreams as $stream)
                <div class="stream-card" onclick="playStream({{ $stream->id }})">
                    <div class="stream-thumbnail">
                        @if($stream->has_recording && $stream->recording_path)
                            <video class="thumbnail-video" preload="metadata">
                                <source src="{{ asset('storage/' . $stream->recording_path) }}#t=1" type="video/mp4">
                                <source src="{{ asset('storage/' . $stream->recording_path) }}#t=1" type="video/webm">
                            </video>
                        @else
                            <div class="no-thumbnail">
                                <i class="fas fa-video"></i>
                                <span>No Preview</span>
                            </div>
                        @endif
                        
                        <div class="stream-overlay">
                            <div class="play-button">
                                <i class="fas fa-play"></i>
                            </div>
                            
                            <div class="stream-info">
                                <div class="stream-title">{{ $stream->title }}</div>
                                <div class="stream-meta">
                                    <span class="streamer">{{ $stream->user->name }}</span>
                                    <span class="date">{{ $stream->started_at->diffForHumans() }}</span>
                                </div>
                                
                                @if($stream->duration_seconds)
                                    <div class="duration">
                                        {{ gmdate('H:i:s', $stream->duration_seconds) }}
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Pagination -->
        <div class="pagination-wrapper">
            {{ $savedStreams->links() }}
        </div>
    @else
        <div class="no-streams">
            <div class="no-streams-icon">
                <i class="fas fa-video-slash"></i>
            </div>
            <h3>No Saved Streams</h3>
            <p>There are no recorded live streams available yet.</p>
            <a href="{{ route('livestream.create') }}" class="btn btn-primary">
                <i class="fas fa-broadcast-tower"></i>
                Start Your First Live Stream
            </a>
        </div>
    @endif
</div>

<!-- Stream Player Modal -->
<div id="streamModal" class="stream-modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="modalTitle">Stream Recording</h3>
            <button class="close-btn" onclick="closeStreamModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="modal-body">
            <video id="streamPlayer" controls class="stream-player">
                Your browser does not support the video tag.
            </video>
            
            <div class="stream-details">
                <div class="streamer-info">
                    <img id="streamerAvatar" src="" alt="Streamer" class="streamer-avatar">
                    <div class="streamer-text">
                        <h4 id="streamerName"></h4>
                        <p id="streamDate"></p>
                    </div>
                </div>
                
                <div class="stream-actions">
                    <button class="action-btn" onclick="shareStream()">
                        <i class="fas fa-share"></i>
                        Share
                    </button>
                    <button class="action-btn" onclick="downloadStream()">
                        <i class="fas fa-download"></i>
                        Download
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.saved-streams-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
    min-height: 100vh;
}

.header {
    text-align: center;
    margin-bottom: 40px;
    padding: 40px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 16px;
}

.header h1 {
    margin: 0 0 10px 0;
    font-size: 2.5rem;
    font-weight: 700;
}

.header p {
    margin: 0;
    font-size: 1.1rem;
    opacity: 0.9;
}

.streams-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 24px;
    margin-bottom: 40px;
}

.stream-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.stream-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.stream-thumbnail {
    position: relative;
    aspect-ratio: 16/9;
    background: #000;
    overflow: hidden;
}

.thumbnail-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-thumbnail {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #333;
    color: #999;
}

.no-thumbnail i {
    font-size: 3rem;
    margin-bottom: 12px;
}

.stream-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(0deg, rgba(0,0,0,0.8) 0%, transparent 50%);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stream-card:hover .stream-overlay {
    opacity: 1;
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #333;
    transition: all 0.3s ease;
}

.play-button:hover {
    background: white;
    transform: translate(-50%, -50%) scale(1.1);
}

.stream-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px;
    color: white;
}

.stream-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 8px;
    line-height: 1.3;
}

.stream-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    opacity: 0.9;
}

.duration {
    position: absolute;
    top: 12px;
    right: 12px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.no-streams {
    text-align: center;
    padding: 80px 20px;
    color: #666;
}

.no-streams-icon {
    font-size: 4rem;
    margin-bottom: 24px;
    opacity: 0.5;
}

.no-streams h3 {
    margin: 0 0 12px 0;
    font-size: 1.8rem;
}

.no-streams p {
    margin: 0 0 32px 0;
    font-size: 1.1rem;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    background: #0056b3;
    transform: translateY(-2px);
}

/* Modal Styles */
.stream-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 10000;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 16px;
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    position: relative;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #eee;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.3rem;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background 0.2s;
}

.close-btn:hover {
    background: #f5f5f5;
}

.stream-player {
    width: 100%;
    max-width: 800px;
    height: auto;
}

.stream-details {
    padding: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.streamer-info {
    display: flex;
    align-items: center;
    gap: 16px;
}

.streamer-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

.streamer-text h4 {
    margin: 0 0 4px 0;
    font-size: 1.1rem;
}

.streamer-text p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

.stream-actions {
    display: flex;
    gap: 12px;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
}

.action-btn:hover {
    background: #e9ecef;
}

.pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 40px;
}

@media (max-width: 768px) {
    .streams-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .header {
        padding: 24px 16px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .stream-details {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
}
</style>

<script>
const streamData = @json($savedStreams->toArray());

function playStream(streamId) {
    const stream = streamData.data.find(s => s.id === streamId);
    if (!stream || !stream.has_recording) {
        alert('Recording not available for this stream');
        return;
    }

    // Update modal content
    document.getElementById('modalTitle').textContent = stream.title;
    document.getElementById('streamerName').textContent = stream.user.name;
    document.getElementById('streamerAvatar').src = stream.user.avatar_url || '/images/default.png';
    document.getElementById('streamDate').textContent = new Date(stream.started_at).toLocaleDateString();
    
    // Set video source
    const player = document.getElementById('streamPlayer');
    player.src = `/storage/${stream.recording_path}`;
    
    // Show modal
    document.getElementById('streamModal').style.display = 'flex';
    
    // Play video
    player.play().catch(e => console.log('Video play failed:', e));
}

function closeStreamModal() {
    const modal = document.getElementById('streamModal');
    const player = document.getElementById('streamPlayer');
    
    player.pause();
    player.currentTime = 0;
    modal.style.display = 'none';
}

function shareStream() {
    const title = document.getElementById('modalTitle').textContent;
    const url = window.location.href;
    
    if (navigator.share) {
        navigator.share({
            title: title,
            url: url
        });
    } else {
        navigator.clipboard.writeText(url).then(() => {
            alert('Stream URL copied to clipboard!');
        });
    }
}

function downloadStream() {
    const player = document.getElementById('streamPlayer');
    const link = document.createElement('a');
    link.href = player.src;
    link.download = document.getElementById('modalTitle').textContent + '.mp4';
    link.click();
}

// Close modal when clicking outside
document.getElementById('streamModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeStreamModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeStreamModal();
    }
});
</script>
@endsection 