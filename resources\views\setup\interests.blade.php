<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Interests - InstaPWA</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            background-color: #d31414;
            color: white;
            min-height: 100vh;
        }

        .container {
            padding: 20px;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo {
            width: 120px;
            margin: 0 auto 30px;
        }

        .title {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 16px;
            opacity: 0.8;
            max-width: 300px;
            margin: 0 auto;
            line-height: 1.4;
        }

        .interests-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 40px;
        }

        .interest-item {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 15px;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .interest-item.selected {
            background: rgba(255, 215, 0, 0.2);
            border: 2px solid #FFD700;
        }

        .interest-icon {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .next-btn {
            width: 100%;
            padding: 15px;
            background: #FFD700;
            color: black;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin-top: auto;
        }

        .next-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .error-message {
            color: #FFD700;
            margin-bottom: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="/images/logo.png" alt="InstaPWA Logo" class="logo">
            <div class="title">Pick your interests</div>
            <div class="subtitle">Select at least 3 topics you're interested in to personalize your experience</div>
        </div>

        @if(session('error'))
            <div class="error-message">{{ session('error') }}</div>
        @endif

        @if($errors->any())
            <div class="error-message">{{ $errors->first() }}</div>
        @endif

        <form action="{{ route('setup.interests') }}" method="POST" id="interestsForm">
            @csrf
            <div id="interestsInputContainer"></div>
            
            <div class="interests-grid">
                @foreach($interests as $interest)
                    <div class="interest-item" onclick="toggleInterest(this, {{ $interest->id }})">
                        <div class="interest-icon">
                            {{ substr($interest->name, 0, 1) }}
                        </div>
                        <div class="interest-name">{{ $interest->name }}</div>
                    </div>
                @endforeach
            </div>

            <button type="submit" class="next-btn" id="nextBtn" disabled>Next</button>
        </form>
    </div>

    <script>
        let selectedInterests = [];
        
        function toggleInterest(element, id) {
            const index = selectedInterests.indexOf(id);
            
            if (index === -1) {
                // Select interest
                selectedInterests.push(id);
                element.classList.add('selected');
            } else {
                // Unselect interest
                selectedInterests.splice(index, 1);
                element.classList.remove('selected');
            }
            
            // Update hidden inputs - create a separate input for each interest
            updateInterestInputs();
            
            // Enable button if at least 3 interests are selected
            document.getElementById('nextBtn').disabled = selectedInterests.length < 3;
        }
        
        function updateInterestInputs() {
            // Clear existing inputs
            const container = document.getElementById('interestsInputContainer');
            container.innerHTML = '';
            
            // Create a new input for each selected interest
            selectedInterests.forEach(id => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'interests[]';
                input.value = id;
                container.appendChild(input);
            });
        }
    </script>
</body>
</html> 