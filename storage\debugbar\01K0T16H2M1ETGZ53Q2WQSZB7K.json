{"__meta": {"id": "01K0T16H2M1ETGZ53Q2WQSZB7K", "datetime": "2025-07-22 21:38:54", "utime": **********.67691, "method": "GET", "uri": "/notifications", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.484171, "end": **********.67692, "duration": 0.1927490234375, "duration_str": "193ms", "measures": [{"label": "Booting", "start": **********.484171, "relative_start": 0, "end": **********.601266, "relative_end": **********.601266, "duration": 0.****************, "duration_str": "117ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.601274, "relative_start": 0.*****************, "end": **********.676921, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "75.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.6147, "relative_start": 0.*****************, "end": **********.616525, "relative_end": **********.616525, "duration": 0.0018248558044433594, "duration_str": "1.82ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.663286, "relative_start": 0.*****************, "end": **********.675472, "relative_end": **********.675472, "duration": 0.012186050415039062, "duration_str": "12.19ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: notifications.index", "start": **********.664939, "relative_start": 0.*****************, "end": **********.664939, "relative_end": **********.664939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.app", "start": **********.665418, "relative_start": 0.*****************, "end": **********.665418, "relative_end": **********.665418, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 24820344, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 2, "nb_templates": 2, "templates": [{"name": "notifications.index", "param_count": null, "params": [], "start": **********.664897, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/notifications/index.blade.phpnotifications.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fnotifications%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "layouts.app", "param_count": null, "params": [], "start": **********.665382, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}]}, "queries": {"count": 5, "nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01893, "accumulated_duration_str": "18.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'ysQ2OZdBk91GzyxsvUNjStsqoUKCd3cWniDGhfUE' limit 1", "type": "query", "params": [], "bindings": ["ysQ2OZdBk91GzyxsvUNjStsqoUKCd3cWniDGhfUE"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.624543, "duration": 0.01701, "duration_str": "17.01ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "instapwa", "explain": null, "start_percent": 0, "width_percent": 89.857}, {"sql": "select * from `users` where `id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.652397, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "instapwa", "explain": null, "start_percent": 89.857, "width_percent": 2.694}, {"sql": "select * from `notifications` where `user_id` = 28 order by `created_at` desc", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/NotificationController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\NotificationController.php", "line": 16}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.656925, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "NotificationController.php:16", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/NotificationController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\NotificationController.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FNotificationController.php&line=16", "ajax": false, "filename": "NotificationController.php", "line": "16"}, "connection": "instapwa", "explain": null, "start_percent": 92.552, "width_percent": 2.747}, {"sql": "select count(*) as aggregate from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = 28 and `notifications`.`notifiable_id` is not null and `read_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\User", 28], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "layouts.app", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/layouts/app.blade.php", "line": 382}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.670016, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "layouts.app:382", "source": {"index": 19, "namespace": "view", "name": "layouts.app", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/layouts/app.blade.php", "line": 382}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=382", "ajax": false, "filename": "app.blade.php", "line": "382"}, "connection": "instapwa", "explain": null, "start_percent": 95.298, "width_percent": 2.853}, {"sql": "select * from `love_wallets` where `love_wallets`.`user_id` = 28 and `love_wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "layouts.app", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/layouts/app.blade.php", "line": 413}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.673197, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "layouts.app:413", "source": {"index": 21, "namespace": "view", "name": "layouts.app", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/layouts/app.blade.php", "line": 413}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=413", "ajax": false, "filename": "app.blade.php", "line": "413"}, "connection": "instapwa", "explain": null, "start_percent": 98.151, "width_percent": 1.849}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/notifications", "action_name": "notifications", "controller_action": "App\\Http\\Controllers\\NotificationController@index", "uri": "GET notifications", "controller": "App\\Http\\Controllers\\NotificationController@index<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FNotificationController.php&line=11\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FNotificationController.php&line=11\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/NotificationController.php:11-22</a>", "middleware": "web, auth", "duration": "194ms", "peak_memory": "26MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1444814268 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1444814268\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-97799400 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-97799400\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Android&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"131 characters\">Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Mobile Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkF6UmJzY3hSSkk1VzUxS21NYWhVSlE9PSIsInZhbHVlIjoiZ0xXTHJZUTcwWmRSSkRtQWk1N2NCa0dIN2tVRkpYc3djS2RYOS9MZGdxRGxxemVnM1d5Z21sa2ZTZnhucVBlaGU5MFlnNmx4eWt4TXJEZWo1cnZpbzhXQXhWT1J4YUZDT1BtS1FSZXhyRHRlcStBUE9vQWhUb2hQcEtSamFoUUkiLCJtYWMiOiJiMjM0ZGJmYzllZTg1NzMyMGQxZTM2NWMzYzczY2MzOGM4ZjBlMzU2M2I2N2VhODI3MzMyNGQ5NTY4NWYyODI4IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InBMWUNtTi9PekdXbkV6dnAvRTAxcVE9PSIsInZhbHVlIjoiMkt5MlVOSXRsVTRjcVlMSU5WSU9rL3UyMDBxN2dOTE10TXFMQTArdmVWVDJiL3ZWUjFFOElRdlZuMjlPRFdTbjJ4dkpMd3NKeHNidldUZEdDS01pODIwOERMTW5UKzhBd3hzNTNheGJOVVFhdm1JN2UwdnpnV0N0di8yWTFDZ1QiLCJtYWMiOiIyOTVhZDRkNzkzYjg1MWIwM2NlYThjMzVhYWE5ZmMyMzg2ZDRmYzlkODI0ODA5ZjYyNTY5NTExZDc0NGE5YzgyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1920536342 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HKkQb009ur9juY1euzSJdZerzJQUHXdiHWRWdESt</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ysQ2OZdBk91GzyxsvUNjStsqoUKCd3cWniDGhfUE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1920536342\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1782709872 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 22 Jul 2025 21:38:54 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1782709872\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-305219569 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HKkQb009ur9juY1euzSJdZerzJQUHXdiHWRWdESt</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>28</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-305219569\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/notifications", "action_name": "notifications", "controller_action": "App\\Http\\Controllers\\NotificationController@index"}, "badge": null}}