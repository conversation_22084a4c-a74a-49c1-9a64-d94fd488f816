<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Love;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LoveController extends Controller
{
    /**
     * Get all loves for the authenticated user
     */
    public function getUserLoves(Request $request)
    {
        $user = Auth::user();
        
        $query = Love::where('user_id', $user->id)
            ->with(['post.user', 'post'])
            ->latest();
            
        // Apply filters if provided
        if ($request->has('filter')) {
            if ($request->filter === 'paid') {
                $query->where('is_paid', true);
            } elseif ($request->filter === 'free') {
                $query->where('is_paid', false);
            }
        }
        
        $loves = $query->paginate(10);
        
        return response()->json([
            'success' => true,
            'data' => $loves
        ]);
    }
    
    /**
     * Get love summary for the authenticated user
     */
    public function getLoveSummary()
    {
        $user = Auth::user();
        
        $lovesGiven = Love::where('user_id', $user->id)->count();
        $totalSpent = Love::where('user_id', $user->id)
            ->where('is_paid', true)
            ->sum('amount');
            
        return response()->json([
            'success' => true,
            'data' => [
                'loves_given' => $lovesGiven,
                'total_spent' => $totalSpent
            ]
        ]);
    }
} 