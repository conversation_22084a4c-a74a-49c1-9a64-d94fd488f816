@extends('layouts.app')

@section('content')
<div class="edit-post-container">
    <div class="edit-header">
        <h1>Edit Post</h1>
    </div>
    
    @if ($errors->any())
        <div class="alert alert-danger">
            <ul>
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif
    
    <form id="editPostForm" action="{{ route('posts.update', $post->id) }}" method="POST">
        @csrf
        @method('PUT')
        
        <div class="media-preview">
            @if($post->media_type === 'image')
                <img src="{{ asset('storage/' . $post->media_url) }}" alt="Post image" class="post-media">
            @elseif($post->media_type === 'video')
                <video src="{{ asset('storage/' . $post->media_url) }}" controls class="post-media"></video>
            @endif
        </div>
        
        <div class="form-group">
            <label for="caption">Caption</label>
            <textarea name="caption" id="caption" rows="4">{{ $post->caption }}</textarea>
        </div>
        
        @if(Auth::user()->is_creator)
        <div class="form-group">
            <label>Visibility</label>
            <div class="radio-options">
                <label>
                    <input type="radio" name="visibility" value="public" {{ $post->visibility === 'public' ? 'checked' : '' }}>
                    <span>Public</span>
                </label>
                <label>
                    <input type="radio" name="visibility" value="subscribers" {{ $post->visibility === 'subscribers' ? 'checked' : '' }}>
                    <span>Subscribers Only</span>
                </label>
            </div>
        </div>
        
        <div class="form-group{{ $post->visibility !== 'subscribers' ? ' hidden' : '' }}" id="premiumOption">
            <label>
                <input type="checkbox" name="is_premium" value="1" {{ $post->is_premium ? 'checked' : '' }}>
                <span>Premium Content</span>
            </label>
        </div>
        @endif
        
        <div class="form-actions">
            <button type="button" id="deletePostBtn" class="btn-delete">Delete Post</button>
            <button type="submit" class="btn-save">Save Changes</button>
        </div>
    </form>
    
    <form id="deletePostForm" action="{{ route('posts.destroy', $post->id) }}" method="POST" style="display: none;">
        @csrf
        @method('DELETE')
    </form>
</div>

<style>
    .edit-post-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .edit-header {
        margin-bottom: 20px;
        text-align: center;
    }
    
    .media-preview {
        margin-bottom: 20px;
        text-align: center;
    }
    
    .post-media {
        max-width: 100%;
        max-height: 400px;
        border-radius: 8px;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
    }
    
    textarea {
        width: 100%;
        padding: 10px;
        border: 1px solid #333;
        border-radius: 4px;
        background: #222;
        color: white;
        resize: vertical;
    }
    
    .radio-options {
        display: flex;
        gap: 20px;
    }
    
    .radio-options label {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
    }
    
    .hidden {
        display: none;
    }
    
    .form-actions {
        display: flex;
        justify-content: space-between;
        margin-top: 30px;
    }
    
    .btn-save {
        background: #4CAF50;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
    }
    
    .btn-delete {
        background: #F44336;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle visibility change
        const visibilityRadios = document.getElementsByName('visibility');
        const premiumOption = document.getElementById('premiumOption');
        
        if (visibilityRadios && premiumOption) {
            visibilityRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    if (this.value === 'subscribers') {
                        premiumOption.classList.remove('hidden');
                    } else {
                        premiumOption.classList.add('hidden');
                    }
                });
            });
        }
        
        // Handle form submission
        const editPostForm = document.getElementById('editPostForm');
        
        editPostForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.href = data.redirect;
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating the post.');
            });
        });
        
        // Handle delete button
        const deleteBtn = document.getElementById('deletePostBtn');
        const deleteForm = document.getElementById('deletePostForm');
        
        deleteBtn.addEventListener('click', function() {
            if (confirm('Are you sure you want to delete this post? This action cannot be undone.')) {
                const formData = new FormData(deleteForm);
                
                fetch(deleteForm.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.href = data.redirect;
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while deleting the post.');
                });
            }
        });
    });
</script>
@endsection 