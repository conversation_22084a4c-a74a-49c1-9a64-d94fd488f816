@extends('layouts.app')

@section('content')
<style>
    .stories {
        display: flex;
        overflow-x: auto;
        padding: 10px;
        gap: 15px;
        background: #111;
        margin-bottom: 10px;
    }

    .story {
        display: flex;
        flex-direction: column;
        align-items: center;
        min-width: 70px;
    }

    .story-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        border: 2px solid #FFD700;
        margin-bottom: 5px;
        object-fit: cover;
    }

    .story-username {
        font-size: 12px;
        color: white;
        text-align: center;
        max-width: 70px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .create-story {
        position: relative;
    }

    .create-story::after {
        content: '+';
        position: absolute;
        bottom: 20px;
        right: 0;
        background: #FFD700;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: black;
        font-weight: bold;
    }

    .post {
        margin-bottom: 20px;
        background: #111;
    }

    .post-header {
        display: flex;
        align-items: center;
        padding: 10px;
        gap: 10px;
    }

    .post-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
    }

    .post-user-info {
        flex: 1;
    }

    .post-username {
        font-weight: bold;
        margin-bottom: 2px;
    }

    .post-time {
        font-size: 12px;
        color: #666;
    }

    .post-image {
        width: 100%;
        aspect-ratio: 1;
        object-fit: cover;
    }

    .post-actions {
        display: flex;
        padding: 10px;
        gap: 15px;
    }

    .action-icon {
        width: 24px;
        height: 24px;
        fill: white;
    }

    .post-likes {
        padding: 0 10px;
        font-size: 14px;
        margin-bottom: 5px;
    }

    .post-caption {
        padding: 0 10px 10px;
        font-size: 14px;
    }
    
    .no-posts {
        padding: 40px 20px;
        text-align: center;
        background: #111;
        color: #888;
        margin-top: 20px;
    }

    .modal {
        display: none;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        top: 0;
        z-index: 1000;
        background: rgba(0, 0, 0, 0.5);
        position: relative;
    }

    .modal-content {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        border-top-left-radius: 20px;
        border-top-right-radius: 20px;
        transform: translateY(100%);
        transition: transform 0.3s ease-out;
    }

    .modal-content.open {
        transform: translateY(0);
    }

    .modal-header {
        display: flex;
        align-items: center;
        padding: 15px;
        border-bottom: 1px solid #eee;
    }

    .back-btn {
        background: none;
        border: none;
        padding: 5px;
        margin-right: 15px;
        cursor: pointer;
    }

    .back-btn svg {
        width: 24px;
        height: 24px;
    }

    .comments-list {
        padding: 15px;
        max-height: 60vh;
        overflow-y: auto;
    }

    .comment-item {
        display: flex;
        margin-bottom: 15px;
        gap: 10px;
    }

    .comment-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
    }

    .comment-content {
        flex: 1;
        color:#000;
    }

    .comment-username {
        font-weight: bold;
        margin-right: 5px;
    }

    .comment-time {
        color: #888;
        font-size: 0.9em;
    }

    .comment-input-wrapper {
        padding: 15px;
        border-top: 1px solid #eee;
        background: white;
    }

    .comment-form {
        display: flex;
        gap: 10px;
        align-items: center;
    }

    #comment-input {
        flex: 1;
        border: 1px solid #ddd;
        border-radius: 20px;
        padding: 8px 15px;
        font-size: 16px;
    }

    .send-btn {
        background: none;
        border: none;
        color: #0095f6;
        font-weight: 600;
        cursor: pointer;
    }

    .emoji-picker {
        margin-bottom: 10px;
        position: relative;
    }

    .emoji-btn {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
    }

    .emoji-list {
        position: absolute;
        bottom: 100%;
        left: 0;
        background: white;
        border: 1px solid #ddd;
        border-radius: 10px;
        padding: 10px;
        display: grid;
        grid-template-columns: repeat(9, 1fr);
        gap: 5px;
    }

    .emoji-list span {
        cursor: pointer;
        font-size: 24px;
        padding: 5px;
    }
    .action-btn{
        background:#000;
        border:0;
    }

    /* Live Stream Styles */
    .live-stream {
        cursor: pointer;
        transition: transform 0.2s ease;
    }

    .live-stream:hover {
        transform: scale(1.05);
    }

    .live-indicator-ring {
        position: relative;
    }

    .live-indicator-ring .story-avatar {
        border: 3px solid #ff3040;
        animation: pulse-ring 2s infinite;
    }

    .live-badge {
        position: absolute;
        bottom: -2px;
        left: 50%;
        transform: translateX(-50%);
        background: #ff3040;
        color: white;
        font-size: 8px;
        font-weight: 600;
        padding: 2px 6px;
        border-radius: 10px;
        text-transform: uppercase;
    }

    .viewer-count {
        position: absolute;
        top: -8px;
        right: -8px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        font-size: 10px;
        padding: 2px 6px;
        border-radius: 10px;
        min-width: 20px;
        text-align: center;
    }

    @keyframes pulse-ring {
        0% {
            box-shadow: 0 0 0 0 rgba(255, 48, 64, 0.7);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(255, 48, 64, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(255, 48, 64, 0);
        }
    }

    /* Recent Streams Section */
    .recent-streams-section {
        margin: 20px 0;
        background: #111;
        padding: 15px;
        border-radius: 8px;
    }

    .section-title {
        color: white;
        margin: 0 0 15px 0;
        font-size: 16px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .section-title i {
        color: #ff3040;
    }

    .recent-streams-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        gap: 12px;
    }

    .recent-stream-card {
        background: #1a1a1a;
        border-radius: 8px;
        overflow: hidden;
        cursor: pointer;
        transition: transform 0.2s ease;
    }

    .recent-stream-card:hover {
        transform: translateY(-2px);
    }

    .stream-thumbnail {
        position: relative;
        aspect-ratio: 16/9;
        background: #000;
        overflow: hidden;
    }

    .thumbnail-video {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .no-thumbnail {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #333;
        color: #666;
        font-size: 2rem;
    }

    .stream-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(transparent, rgba(0,0,0,0.7));
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.2s ease;
    }

    .recent-stream-card:hover .stream-overlay {
        opacity: 1;
    }

    .stream-duration {
        position: absolute;
        top: 8px;
        right: 8px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        font-size: 12px;
        padding: 2px 6px;
        border-radius: 4px;
    }

    .play-icon {
        color: white;
        font-size: 2rem;
        opacity: 0.9;
    }

    .stream-info {
        padding: 12px;
    }

    .stream-user {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 6px;
    }

    .stream-user-avatar {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        object-fit: cover;
    }

    .stream-username {
        color: #ccc;
        font-size: 12px;
        font-weight: 500;
    }

    .stream-title {
        color: white;
        font-size: 13px;
        font-weight: 600;
        margin-bottom: 4px;
        line-height: 1.3;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .stream-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 11px;
        color: #999;
    }

    @media (max-width: 768px) {
        .recent-streams-grid {
            grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
            gap: 8px;
        }
        
        .stream-info {
            padding: 8px;
        }
    }
</style>

<div class="stories">
    <!-- Live Streams Section -->
    @if($liveStreams->count() > 0)
        @foreach($liveStreams as $stream)
            <div class="story live-stream" onclick="joinLiveStream({{ $stream->id }})">
                <div class="live-indicator-ring">
                    @if($stream->user->profile && $stream->user->profile->photo)
                        <img src="{{ Storage::disk('public')->url($stream->user->profile->photo) }}" alt="{{ $stream->user->name }}" class="story-avatar">
                    @else
                        <img src="/images/default.png" alt="{{ $stream->user->name }}" class="story-avatar">
                    @endif
                    <div class="live-badge">LIVE</div>
                    <div class="viewer-count">{{ $stream->viewer_count }}</div>
                </div>
                <span class="story-username">{{ $stream->user->username }}</span>
            </div>
        @endforeach
    @endif

    <!-- Create Your Story -->
    <div class="story create-story" onclick="createStory()">
        @if(auth()->user()->profile && auth()->user()->profile->photo)
            <img src="{{ Storage::disk('public')->url(auth()->user()->profile->photo) }}" alt="Create Story" class="story-avatar">
        @else
            <img src="/images/default.png" alt="Create Story" class="story-avatar">
        @endif
        <span class="story-username">Your story</span>
    </div>

    <!-- Suggested Users -->
    @foreach($suggestedUsers as $user)
        <div class="story">
            @if($user->profile && $user->profile->photo)
                <img src="{{ Storage::disk('public')->url($user->profile->photo) }}" alt="{{ $user->username }}" class="story-avatar">
            @else
                <img src="/images/default.png" alt="{{ $user->username }}" class="story-avatar">
            @endif
            <span class="story-username">{{ $user->username }}</span>
        </div>
    @endforeach
</div>

<!-- Recent Streams Section -->
@if($recentStreams->count() > 0)
<div class="recent-streams-section">
    <h3 class="section-title">
        <i class="fas fa-video"></i>
        Recent Live Streams
    </h3>
    <div class="recent-streams-grid">
        @foreach($recentStreams as $stream)
            <div class="recent-stream-card" onclick="viewRecording({{ $stream->id }})">
                <div class="stream-thumbnail">
                    @if($stream->thumbnail_path)
                        <img src="{{ asset('storage/' . $stream->thumbnail_path) }}" alt="{{ $stream->title }}" class="thumbnail-video">
                    @elseif($stream->recording_path)
                        <video class="thumbnail-video" preload="metadata" muted>
                            <source src="{{ asset('storage/' . $stream->recording_path) }}#t=2" type="video/webm">
                            <source src="{{ asset('storage/' . $stream->recording_path) }}#t=2" type="video/mp4">
                        </video>
                    @else
                        <div class="no-thumbnail">
                            <i class="fas fa-video"></i>
                        </div>
                    @endif
                    
                    <div class="stream-overlay">
                        <div class="play-icon">
                            <i class="fas fa-play"></i>
                        </div>
                    </div>
                    
                    @if($stream->duration_seconds)
                        <div class="stream-duration">{{ $stream->formatted_duration }}</div>
                    @endif
                </div>
                
                <div class="stream-info">
                    <div class="stream-user">
                        @if($stream->user->profile && $stream->user->profile->photo)
                            <img src="{{ Storage::disk('public')->url($stream->user->profile->photo) }}" alt="{{ $stream->user->name }}" class="stream-user-avatar">
                        @else
                            <img src="/images/default.png" alt="{{ $stream->user->name }}" class="stream-user-avatar">
                        @endif
                        <span class="stream-username">{{ $stream->user->username }}</span>
                    </div>
                    <div class="stream-title">{{ $stream->title }}</div>
                    <div class="stream-meta">
                        <span class="stream-time">{{ $stream->ended_at->diffForHumans() }}</span>
                        <span class="stream-duration-text">{{ $stream->formatted_duration }}</span>
                    </div>
                </div>
            </div>
        @endforeach
    </div>
</div>
@endif

@if($posts->count() > 0)
    <div class="posts">
        @foreach($posts as $post)
        <div class="post" id="post-{{ $post->id }}">
            <div class="post-header">

                @if($post->user->profile && $post->user->profile->photo)
                    <img src="{{ Storage::disk('public')->url($post->user->profile->photo) }}" alt="{{ $post->user->username }}" class="post-avatar">
                @else
                    <img src="/images/default-avatar.png" alt="{{ $post->user->username }}" class="post-avatar">
                @endif
                
                <div class="post-user-info">
                    <div class="post-username">{{ $post->user->username }}</div>
                    <div class="post-time">{{ $post->created_at->diffForHumans() }}</div>
                </div>
                <svg viewBox="0 0 24 24" width="24" height="24" fill="white">
                    <path d="M12,16A2,2 0 0,1 14,18A2,2 0 0,1 12,20A2,2 0 0,1 10,18A2,2 0 0,1 12,16M12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12A2,2 0 0,1 12,10M12,4A2,2 0 0,1 14,6A2,2 0 0,1 12,8A2,2 0 0,1 10,6A2,2 0 0,1 12,4Z"/>
                </svg>
            </div>

        
          @if($post->media_type === 'video')
            @if($post->media_url)
           <video src="{{ asset('storage/' . $post->media_url) }}" alt="Post" class="post-image"></video>
            @endif
          @else
            @if($post->media_url)
           <img src="{{ asset('storage/' . $post->media_url) }}" alt="Post" class="post-image">
            @endif
          @endif

            <div class="post-actions">
                <div class="action-group">
                    @include('components.love-button', ['post' => $post])
                    
                    <button  class="action-btn"  onclick="openCommentModal({{ $post->id }})">
                        <svg class="action-icon" viewBox="0 0 24 24">
                            <path d="M20,2H4A2,2 0 0,0 2,4V22L6,18H20A2,2 0 0,0 22,16V4A2,2 0 0,0 20,2M6,9H18V11H6M14,14H6V12H14M18,8H6V6H18"/>
                        </svg>
                        <span class="action-count">{{ $post->comments->count() }}</span>
                    </button>
                </div>
            </div>

            <div class="post-likes">
                Liked by {{ rand(1, 50) }} people
            </div>

            @if($post->caption)
                <div class="post-caption">
                    <strong>{{ $post->user->username }}</strong> {{ $post->caption }}
                </div>
            @endif
        </div>
        @endforeach
    </div>
@else
    <div class="no-posts">
        <p>No posts yet</p>
        <a href="{{ route('create.post') }}" style="color: #FFD700; text-decoration: none; display: block; margin-top: 10px;">Create your first post</a>
    </div>
@endif

<!-- Comment Modal -->
<div id="comment-modal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <button class="back-btn" onclick="closeCommentModal()">
                <svg viewBox="0 0 24 24">
                    <path d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z"/>
                </svg>
            </button>
            <h2>Comments</h2>
        </div>
        
        <div class="comments-list" id="comments-list"></div>
        
        <div class="comment-input-wrapper">
            <div class="emoji-picker">
                <button onclick="toggleEmojiPicker()" class="emoji-btn">
                    <i class="far fa-smile"></i>
                </button>
                <div class="emoji-list" style="display: none;">
                    <span onclick="addEmoji('😀')">😀</span>
                    <span onclick="addEmoji('😂')">😂</span>
                    <span onclick="addEmoji('😊')">😊</span>
                    <span onclick="addEmoji('😍')">😍</span>
                    <span onclick="addEmoji('😎')">😎</span>
                    <span onclick="addEmoji('😜')">😜</span>
                    <span onclick="addEmoji('🥰')">🥰</span>
                    <span onclick="addEmoji('👍')">👍</span>
                    <span onclick="addEmoji('❤️')">❤️</span>
                </div>
            </div>
            
            <form id="comment-form" class="comment-form">
                <input type="text" 
                       id="comment-input" 
                       placeholder="Add a comment as {{ Auth::user()->username }}..." 
                       autocomplete="off">
                <button type="submit" class="send-btn">Send</button>
            </form>
        </div>
    </div>
</div>

<script>
let currentPostId = null;

function openCommentModal(postId) {
    currentPostId = postId;
    const modal = document.getElementById('comment-modal');
    modal.style.display = 'flex';
    setTimeout(() => {
        modal.querySelector('.modal-content').classList.add('open');
    }, 10);
    loadComments(postId);
}

function closeCommentModal() {
    
    const modal = document.getElementById('comment-modal');
    modal.querySelector('.modal-content').classList.remove('open');
    setTimeout(() => {
        modal.style.display = 'none';
    }, 300);
}

function toggleEmojiPicker() {
    const emojiList = document.querySelector('.emoji-list');
    emojiList.style.display = emojiList.style.display === 'none' ? 'grid' : 'none';
}

function addEmoji(emoji) {
    const input = document.getElementById('comment-input');
    input.value += emoji;
    input.focus();
}

function loadComments(postId) {
    fetch(`/posts/${postId}/comments`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (!data.success) {
                throw new Error(data.message || 'Failed to load comments');
            }
            
            const commentsList = document.getElementById('comments-list');
            if (!data.comments || !Array.isArray(data.comments)) {
                commentsList.innerHTML = '<p>No comments yet</p>';
                return;
            }
            
            commentsList.innerHTML = data.comments.map(comment => `
                <div class="comment-item" id="comment-${comment.id}">
                    <img src="${comment.user.profile_photo}" class="comment-avatar" alt="${comment.user.username}">
                    <div class="comment-content">
                        <span class="comment-username">${comment.user.username}</span>
                        <span class="comment-text">${comment.content}</span>
                        <div class="comment-meta">
                            <span class="comment-time">${comment.created_at}</span>
                            ${comment.can_edit ? `
                                <button onclick="editComment(${comment.id})">Edit</button>
                                <button onclick="deleteComment(${comment.id})">Delete</button>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `).join('');
        })
        .catch(error => {
            console.error('Error loading comments:', error);
            const commentsList = document.getElementById('comments-list');
            commentsList.innerHTML = `<p class="error-message">Error loading comments: ${error.message}</p>`;
        });
}

document.getElementById('comment-form').addEventListener('submit', function(e) {
    e.preventDefault();
    const input = document.getElementById('comment-input');
    const content = input.value.trim();
    
    if (!content) return;
    
    fetch(`/posts/${currentPostId}/comments`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Accept': 'application/json'
        },
        body: JSON.stringify({ content })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            input.value = '';
            loadComments(currentPostId);
            // Update comment count
            const countElem = document.querySelector(`#post-${currentPostId} .action-count`);
            countElem.textContent = parseInt(countElem.textContent) + 1;
        }
    });
});

function deleteComment(commentId) {
    if (confirm('Are you sure you want to delete this comment?')) {
        fetch(`/comments/${commentId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Failed to delete comment');
            }
            // Remove the comment from the DOM or refresh the comments list
            document.getElementById(`comment-${commentId}`).remove();
        })
        .catch(error => {
            console.error('Error deleting comment:', error);
            alert('An error occurred while deleting the comment: ' + error.message);
        });
    }
}

function editComment(commentId) {
    const commentElement = document.getElementById(`comment-${commentId}`);
    const commentContent = commentElement.querySelector('.comment-text');
    const newContent = prompt('Edit your comment:', commentContent.textContent);
    
    if (newContent !== null) {
        fetch(`/comments/${commentId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            },
            body: JSON.stringify({ content: newContent })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Failed to update comment');
            }
            // Update the comment text in the DOM
            commentContent.textContent = newContent;
        })
        .catch(error => {
            console.error('Error editing comment:', error);
            alert('An error occurred while editing the comment: ' + error.message);
        });
    }
}
</script>

<script>
// Live Stream Functions
function joinLiveStream(streamId) {
    window.location.href = `/live/${streamId}`;
}

function viewRecording(streamId) {
    window.location.href = `/live/${streamId}/ended`;
}

function createStory() {
    // Show options for creating story or live stream
    const options = confirm('Create a Live Stream? (Cancel for regular story)');
    if (options) {
        window.location.href = '{{ route("livestream.create") }}';
    } else {
        window.location.href = '{{ route("create.story") }}';
    }
}

// Auto-refresh live streams every 30 seconds
setInterval(function() {
    const liveStreamElements = document.querySelectorAll('.live-stream');
    if (liveStreamElements.length > 0) {
        // Only refresh if there are live streams showing
        fetch('/api/live-streams-status')
            .then(response => response.json())
            .then(data => {
                // Update viewer counts
                data.streams.forEach(stream => {
                    const element = document.querySelector(`[onclick="joinLiveStream(${stream.id})"] .viewer-count`);
                    if (element) {
                        element.textContent = stream.viewer_count;
                    }
                });
            })
            .catch(error => console.log('Failed to update live stream status:', error));
    }
}, 30000);

// Notification for new live streams
if ('Notification' in window && Notification.permission === 'granted') {
    // Listen for new live stream notifications via WebSocket
    if (typeof window.Echo !== 'undefined') {
        window.Echo.channel('live-streams')
            .listen('stream.started', (data) => {
                if (data.user.id != {{ auth()->id() }}) {
                    new Notification(`${data.user.name} started a live stream!`, {
                        body: data.title,
                        icon: data.user.avatar,
                        tag: `stream-${data.id}`
                    });
                }
            });
    }
}

// Request notification permission if not granted
if ('Notification' in window && Notification.permission === 'default') {
    Notification.requestPermission();
}
</script>
@endsection 