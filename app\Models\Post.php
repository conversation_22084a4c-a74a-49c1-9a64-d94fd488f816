<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Post extends Model
{
    use HasFactory;

    protected $fillable = [
        'camp_id',
        'user_id',
        'content',
        'media_url',
        'media_type',
    ];

    public function camp()
    {
        return $this->belongsTo(Camp::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function comments()
    {
        return $this->hasMany(Comment::class)->whereNull('parent_id');
    }

    public function loves()
    {
        return $this->hasMany(Love::class);
    }

    /**
     * Check if a user has loved this post
     */
    public function isLovedByUser($userId)
    {
        return $this->loves()->where('user_id', $userId)->exists();
    }

    /**
     * Get the love count for this post
     */
    public function getLoveCountAttribute()
    {
        return $this->loves()->count();
    }

    /**
     * Get the love value for this post in USD
     */
    public function getLoveValueAttribute()
    {
        return $this->getLoveCountAttribute() * 0.01;
    }
} 