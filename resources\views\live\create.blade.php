@extends('layouts.app')

@section('content')
<div class="container live-create-container">
    <div class="camera-preview">
        <video id="preview" autoplay muted playsinline class="w-100"></video>
        <div class="preview-overlay">
            <div class="preview-text">Live Preview</div>
        </div>
    </div>

    <form id="startStreamForm" class="stream-form">
        <div class="mb-3">
            <input type="text" name="title" class="form-control form-control-lg"
                   placeholder="Stream Title" required>
        </div>

        <div class="mb-3">
            <select name="visibility" class="form-select form-select-lg">
                <option value="public">Public Stream</option>
                <option value="private">Private Stream</option>
            </select>
        </div>

        <button type="submit" class="btn btn-danger btn-lg w-100 go-live-btn">
            <i class="fas fa-broadcast-tower me-2"></i>Start Live Stream
        </button>
    </form>

    <div id="liveControls" class="d-none">
        <div class="live-indicator">
            <span class="live-dot"></span>
            LIVE
        </div>
        <button id="endStreamBtn" class="btn btn-dark btn-lg w-100 mt-3">
            <i class="fas fa-stop-circle me-2"></i>End Stream
        </button>
    </div>
</div>

<style>
.live-create-container {
    max-width: 800px;
    margin: 2rem auto;
    background: #1a1a1a;
    padding: 2rem;
    border-radius: 12px;
}

.camera-preview {
    position: relative;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
    aspect-ratio: 16/9;
}

.preview-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0,0,0,0.5);
}

.preview-text {
    color: #fff;
    font-size: 1.5rem;
    font-weight: 500;
}

.go-live-btn {
    transition: all 0.3s ease;
}

.live-indicator {
    color: #dc3545;
    font-weight: 600;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 1rem;
}

.live-dot {
    width: 12px;
    height: 12px;
    background: #dc3545;
    border-radius: 50%;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Add notification styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    max-width: 300px;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 1000;
    animation: slideIn 0.3s ease-out;
}

.notification-success {
    background: #10b981;
    color: white;
}

.notification-error {
    background: #ef4444;
    color: white;
}

.notification-info {
    background: #3b82f6;
    color: white;
}

.notification-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
}

.notification-content button {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', async () => {
    const preview = document.getElementById('preview');
    let mediaStream;

    try {
        mediaStream = await navigator.mediaDevices.getUserMedia({
            video: { width: 1280, height: 720 },
            audio: true
        });
        preview.srcObject = mediaStream;
    } catch (error) {
        alert('Camera/microphone access is required to go live!');
        window.location.href = "{{ route('home') }}";
    }

    let streamId;
    let mediaRecorder;

    document.getElementById('startStreamForm').addEventListener('submit', async (e) => {
        e.preventDefault();

        const form = e.target;
        const submitBtn = form.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Starting...';

        try {
            const response = await fetch("{{ route('livestream.start') }}", {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': "{{ csrf_token() }}"
                },
                body: JSON.stringify({
                    title: form.title.value,
                    visibility: form.visibility.value
                })
            });

            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);

            // Try to get response text first
            const responseText = await response.text();
            console.log('Response text:', responseText);

            // Try to parse as JSON
            let data;
            try {
                data = JSON.parse(responseText);
            } catch (parseError) {
                console.error('Failed to parse response as JSON:', parseError);
                throw new Error(`Server returned non-JSON response: ${responseText}`);
            }

            console.log('Parsed response data:', data);

            if (!response.ok) {
                if (data.errors) {
                    // Laravel validation errors
                    const errorMessages = Object.values(data.errors).flat().join(', ');
                    throw new Error(`Validation errors: ${errorMessages}`);
                } else if (data.message) {
                    throw new Error(data.message);
                } else {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
            }

            if (data.success && data.stream_id) {
                streamId = data.stream_id;

                // Start recording
                await startRecording();

                // Hide form and show controls
                document.getElementById('startStreamForm').classList.add('d-none');
                document.getElementById('liveControls').classList.remove('d-none');

                // Remove preview overlay
                document.querySelector('.preview-overlay').style.display = 'none';

                // Show success message
                console.log('Stream started successfully with ID:', streamId);

                // Optional: Show toast notification
                showNotification('🔴 You are now live!', 'success');

            } else {
                throw new Error(data.message || 'Failed to start stream');
            }
        } catch (error) {
            console.error('Error starting stream:', error);
            showNotification(`Error: ${error.message}`, 'error');
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-broadcast-tower me-2"></i>Start Live Stream';
        }
    });

    function startBroadcasting(streamUrl) {
        const config = {
            iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
        };

        const peerConnection = new RTCPeerConnection(config);
        mediaStream.getTracks().forEach(track => {
            peerConnection.addTrack(track, mediaStream);
        });

        // Add your signaling server logic here
        // This is where you'd connect to your WebRTC server
    }

    let recordedChunks = [];

    async function startRecording() {
        if (!mediaStream) {
            console.error('No media stream available for recording');
            return;
        }

        try {
            recordedChunks = [];
            mediaRecorder = new MediaRecorder(mediaStream);

            mediaRecorder.ondataavailable = event => {
                if (event.data.size > 0) {
                    recordedChunks.push(event.data);
                }
            };

            mediaRecorder.onerror = (event) => {
                console.error('MediaRecorder error:', event.error);
            };

            mediaRecorder.start(1000); // Collect data every 1 second
            console.log('Recording started successfully');
        } catch (error) {
            console.error('Error starting recording:', error);
            mediaRecorder = null;
        }
    }

    async function stopRecording() {
        return new Promise(resolve => {
            if (!mediaRecorder || mediaRecorder.state === 'inactive') {
                console.log('MediaRecorder not available or already stopped');
                resolve();
                return;
            }

            mediaRecorder.onstop = async () => {
                console.log('MediaRecorder stopped, chunks count:', recordedChunks.length);

                if (recordedChunks.length === 0) {
                    console.error('No recorded chunks available');
                    resolve();
                    return;
                }

                const blob = new Blob(recordedChunks, { type: 'video/webm' });
                const file = new File([blob], `stream-${streamId}.webm`, { type: 'video/webm' });
                console.log('Created blob, size:', blob.size, 'bytes');

                if (blob.size === 0) {
                    console.error('Blob is empty, no recording data');
                    resolve();
                    return;
                }

                const formData = new FormData();
                formData.append('recording', file);

                console.log('Uploading recording for stream ID:', streamId);
                console.log('Upload URL:', `{{ route('livestream.upload', ':streamId') }}`.replace(':streamId', streamId));

                try {
                    const uploadResponse = await fetch(`{{ route('livestream.upload', ':streamId') }}`.replace(':streamId', streamId), {
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': "{{ csrf_token() }}"
                        },
                        body: formData
                    });

                    console.log('Upload response status:', uploadResponse.status);

                    const uploadText = await uploadResponse.text();
                    console.log('Upload response text:', uploadText);

                    let uploadData;
                    try {
                        uploadData = JSON.parse(uploadText);
                    } catch (e) {
                        console.error('Failed to parse upload response:', e);
                        console.error('Response was:', uploadText);
                        resolve();
                        return;
                    }

                    if (uploadResponse.ok && uploadData.success) {
                        console.log('Recording uploaded successfully:', uploadData);
                        showNotification('📹 Recording saved successfully!', 'success');
                    } else {
                        console.error('Upload failed:', uploadData);
                        showNotification(`Upload failed: ${uploadData.message || 'Unknown error'}`, 'error');
                    }
                } catch (error) {
                    console.error('Error uploading recording:', error);
                    showNotification(`Upload error: ${error.message}`, 'error');
                }
                resolve();
            };

            try {
                mediaRecorder.stop();
            } catch (error) {
                console.error('Error stopping mediaRecorder:', error);
                resolve();
            }
        });
    }

    document.getElementById('endStreamBtn').addEventListener('click', async () => {
        const endBtn = document.getElementById('endStreamBtn');
        endBtn.disabled = true;
        endBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Ending Stream...';

        try {
            // Stop recording first
            await stopRecording();

            // Stop media tracks
            if (mediaStream) {
                mediaStream.getTracks().forEach(track => track.stop());
            }

            // End stream on server
            if (streamId) {
                const response = await fetch(`{{ route('livestream.end', ':streamId') }}`.replace(':streamId', streamId), {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': "{{ csrf_token() }}"
                    }
                });

                const data = await response.json();
                if (data.success) {
                    if (data.redirect_url) {
                        window.location.href = data.redirect_url;
                    } else {
                        window.location.href = "{{ route('home') }}";
                    }
                } else {
                    console.error('Failed to end stream:', data.message);
                    window.location.href = "{{ route('home') }}";
                }
            } else {
                window.location.href = "{{ route('home') }}";
            }
        } catch (error) {
            console.error('Error ending stream:', error);
            alert('Failed to end stream properly. Redirecting to home...');
            window.location.href = "{{ route('home') }}";
        }
    });
});

// Notification function
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()">×</button>
        </div>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}
</script>

@endsection
