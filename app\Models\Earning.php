<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Earning extends Model
{
    protected $fillable = [
        'creator_id',
        'amount',
        'source',
        'source_id',
        'earned_at',
        'paid_at',
        'status'
    ];

    protected $casts = [
        'amount' => 'float',
        'earned_at' => 'datetime',
        'paid_at' => 'datetime',
    ];

    public function creator()
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    public function sourcePost()
    {
        return $this->belongsTo(Post::class, 'source_id')
            ->where('source', 'loves');
    }

    public function sourceSubscription()
    {
        return $this->belongsTo(Subscription::class, 'source_id')
            ->where('source', 'subscription');
    }
} 