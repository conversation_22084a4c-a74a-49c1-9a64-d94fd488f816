<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('posts', function (Blueprint $table) {
            $table->string('visibility')->default('public')->after('type'); // 'public', 'subscribers'
            $table->boolean('is_premium')->default(false)->after('visibility');
        });
    }

    public function down()
    {
        Schema::table('posts', function (Blueprint $table) {
            $table->dropColumn(['visibility', 'is_premium']);
        });
    }
}; 