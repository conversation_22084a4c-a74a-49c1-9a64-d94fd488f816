const express = require('express');
const { createServer } = require('http');
const { Server } = require('socket.io');
const { config } = require('dotenv');
const path = require('path');

// Load environment variables from signaling.env
// __filename and __dirname are available in CommonJS by default
config({ path: path.join(__dirname, 'signaling.env') });

const app = express();
const http = createServer(app);

// Add basic health check endpoint
app.get('/health', (req, res) => {
    res.send('Signaling server is running');
});

const io = new Server(http, {
    cors: {
        origin: process.env.APP_URL || "http://localhost:8000",
        methods: ["GET", "POST"],
        credentials: true
    },
    transports: ['websocket', 'polling']
});

// Store active streams and their broadcasters
const streams = new Map(); // streamId -> { broadcasterId, viewers: Set }

// Store online users and their status
const onlineUsers = new Map(); // userId -> { socketId, status, lastSeen, currentPage }
const userSockets = new Map(); // socketId -> userId

io.on('connection', (socket) => {
    console.log('A user connected:', socket.id);

    // Send initial connection success event
    socket.emit('connection-success', {
        socketId: socket.id,
        message: 'Connected to signaling server'
    });

    // Join a stream room
    socket.on('join-stream', ({ streamId, isStreamer }) => {
        try {
            console.log(`Socket ${socket.id} joining stream ${streamId} as ${isStreamer ? 'broadcaster' : 'viewer'}`);
            const streamRoom = `stream-${streamId}`;
            
            if (isStreamer) {
                // Initialize stream data if not exists
                if (!streams.has(streamId)) {
                    streams.set(streamId, {
                        broadcasterId: socket.id,
                        viewers: new Set()
                    });
                }
                
                socket.join(streamRoom);
                console.log(`Broadcaster ${socket.id} joined stream ${streamId}`);
                
                // Notify room that stream is available
                io.to(streamRoom).emit('stream-started', {
                    streamId,
                    broadcasterId: socket.id
                });
            } else {
                // Viewer joining
                const streamData = streams.get(streamId);
                if (streamData) {
                    socket.join(streamRoom);
                    streamData.viewers.add(socket.id);
                    
                    // Update viewer count
                    const viewerCount = streamData.viewers.size;
                    io.to(streamRoom).emit('viewer-count', { count: viewerCount });
                    
                    console.log(`Viewer ${socket.id} joined stream ${streamId}. Total viewers: ${viewerCount}`);
                } else {
                    socket.emit('error', { message: 'Stream not found' });
                }
            }
        } catch (error) {
            console.error('Error in join-stream:', error);
            socket.emit('error', { message: 'Failed to join stream' });
        }
    });

    // Handle WebRTC signaling
    socket.on('signal', ({ streamId, signal, viewerId }) => {
        try {
            const streamRoom = `stream-${streamId}`;
            const streamData = streams.get(streamId);
            
            if (!streamData) {
                console.error(`No stream data found for stream ${streamId}`);
                return;
            }

            console.log(`Signal ${signal.type} for stream ${streamId} from ${socket.id}`);

            if (socket.id === streamData.broadcasterId) {
                // Broadcaster sending to specific viewer
                if (viewerId) {
                    console.log(`Broadcaster sending ${signal.type} to viewer ${viewerId}`);
                    io.to(viewerId).emit('signal', {
                        streamId,
                        signal,
                        senderId: socket.id
                    });
                }
            } else {
                // Viewer sending to broadcaster
                console.log(`Viewer ${socket.id} sending ${signal.type} to broadcaster`);
                io.to(streamData.broadcasterId).emit('signal', {
                    streamId,
                    signal,
                    senderId: socket.id
                });
            }
        } catch (error) {
            console.error('Error in signal handling:', error);
            socket.emit('error', { message: 'Failed to process signal' });
        }
    });

    // Handle chat messages
    socket.on('send-comment', (data) => {
        try {
            console.log('Received send-comment event with data:', data);
            
            const { streamId, comment, username, userId } = data;
            const streamRoom = `stream-${streamId}`;
            const streamData = streams.get(streamId);
            
            if (!streamData) {
                socket.emit('error', { message: 'Stream not found' });
                return;
            }

            console.log(`Chat message in stream ${streamId} from ${username || 'Anonymous'}: ${comment}`);

            // Ensure we have valid data
            const validUsername = username || 'Anonymous';
            const validUserId = userId || 0;

            console.log('Broadcasting comment to room:', streamRoom);

            // Broadcast comment to all users in the stream room
            io.to(streamRoom).emit('new-comment', {
                streamId,
                comment,
                username: validUsername,
                userId: validUserId,
                timestamp: new Date().toISOString()
            });

        } catch (error) {
            console.error('Error in send-comment:', error);
            socket.emit('error', { message: 'Failed to send comment' });
        }
    });

    // Handle heart reactions
    socket.on('send-heart', ({ streamId, username, userId }) => {
        try {
            const streamRoom = `stream-${streamId}`;
            const streamData = streams.get(streamId);
            
            if (!streamData) {
                socket.emit('error', { message: 'Stream not found' });
                return;
            }

            console.log(`Heart reaction in stream ${streamId} from ${username}`);

            // Broadcast heart to all users in the stream room
            io.to(streamRoom).emit('heart-reaction', {
                streamId,
                username,
                userId,
                timestamp: new Date().toISOString()
            });

        } catch (error) {
            console.error('Error in send-heart:', error);
            socket.emit('error', { message: 'Failed to send heart' });
        }
    });

    // Handle stream stop
    socket.on('stream-stop', (streamId) => {
        try {
            const streamRoom = `stream-${streamId}`;
            const streamData = streams.get(streamId);
            
            if (streamData && streamData.broadcasterId === socket.id) {
                console.log(`Stopping stream ${streamId}`);
                io.to(streamRoom).emit('stream-ended');
                streams.delete(streamId);
            }
        } catch (error) {
            console.error('Error in stream-stop:', error);
            socket.emit('error', { message: 'Failed to stop stream' });
        }
    });

    // Handle private messages between users
    socket.on('send-message', (data) => {
        try {
            console.log('Received send-message event with data:', data);
            
            const { sender_id, receiver_id, message, media_url, media_type, created_at, sender } = data;
            
            console.log(`Private message from user ${sender_id} to user ${receiver_id}: ${message || 'media message'}`);

            // Broadcast message to the receiver
            io.emit('new-message', {
                sender_id,
                receiver_id,
                message,
                media_url,
                media_type,
                created_at,
                sender
            });

            console.log('Message broadcasted successfully');

        } catch (error) {
            console.error('Error in send-message:', error);
            socket.emit('error', { message: 'Failed to send message' });
        }
    });

    // Handle user joining chat rooms (for targeted messaging)
    socket.on('join-chat', ({ userId }) => {
        try {
            const chatRoom = `user-${userId}`;
            socket.join(chatRoom);
            console.log(`User ${userId} joined their chat room: ${chatRoom}`);
            
            socket.emit('chat-joined', {
                userId,
                message: 'Joined chat successfully'
            });
        } catch (error) {
            console.error('Error in join-chat:', error);
            socket.emit('error', { message: 'Failed to join chat' });
        }
    });

    // Handle user going online (entering chat pages)
    socket.on('user-online', ({ userId, username, currentPage }) => {
        try {
            // Validate required data
            if (!userId || !username) {
                console.error('Invalid user-online data:', { userId, username, currentPage });
                return;
            }
            
            console.log(`User ${userId} (${username}) is now online on page: ${currentPage}`);
            
            // Store user as online
            onlineUsers.set(userId, {
                socketId: socket.id,
                status: 'online',
                lastSeen: new Date(),
                currentPage: currentPage || '/chats',
                username: username
            });
            
            // Map socket to user
            userSockets.set(socket.id, userId);
            
            // Join user's personal room for targeted messaging
            socket.join(`user-${userId}`);
            
            // Broadcast to all other users that this user is online
            socket.broadcast.emit('user-status-update', {
                userId: parseInt(userId),
                username: username,
                status: 'online',
                currentPage: currentPage,
                timestamp: new Date().toISOString()
            });
            
            // Send current online users to the newly connected user
            const currentOnlineUsers = Array.from(onlineUsers.entries()).map(([id, data]) => ({
                userId: parseInt(id),
                username: data.username,
                status: data.status,
                currentPage: data.currentPage,
                lastSeen: data.lastSeen
            })).filter(user => user.userId !== parseInt(userId));
            
            socket.emit('online-users-list', {
                onlineUsers: currentOnlineUsers
            });
            
        } catch (error) {
            console.error('Error in user-online:', error);
            socket.emit('error', { message: 'Failed to set user online' });
        }
    });

    // Handle user going offline (leaving chat pages)
    socket.on('user-offline', ({ userId }) => {
        try {
            if (!userId) {
                console.error('Invalid user-offline data: userId is missing');
                return;
            }
            
            console.log(`User ${userId} is going offline`);
            
            const userData = onlineUsers.get(userId);
            if (userData) {
                // Update status to offline
                onlineUsers.set(userId, {
                    ...userData,
                    status: 'offline',
                    lastSeen: new Date()
                });
                
                // Broadcast offline status
                socket.broadcast.emit('user-status-update', {
                    userId: parseInt(userId),
                    username: userData.username,
                    status: 'offline',
                    timestamp: new Date().toISOString()
                });
                
                // Remove from online list after a delay
                setTimeout(() => {
                    onlineUsers.delete(userId);
                    userSockets.delete(socket.id);
                }, 30000); // Keep offline status for 30 seconds
            }
        } catch (error) {
            console.error('Error in user-offline:', error);
        }
    });

    // Handle page change (user navigating between chat pages)
    socket.on('page-change', ({ userId, currentPage }) => {
        try {
            const userData = onlineUsers.get(userId);
            if (userData) {
                // Update current page
                onlineUsers.set(userId, {
                    ...userData,
                    currentPage: currentPage,
                    lastSeen: new Date()
                });
                
                console.log(`User ${userId} changed page to: ${currentPage}`);
            }
        } catch (error) {
            console.error('Error in page-change:', error);
        }
    });

    // Get online users list
    socket.on('get-online-users', () => {
        try {
            const currentOnlineUsers = Array.from(onlineUsers.entries()).map(([id, data]) => ({
                userId: parseInt(id),
                username: data.username,
                status: data.status,
                currentPage: data.currentPage,
                lastSeen: data.lastSeen
            }));
            
            socket.emit('online-users-list', {
                onlineUsers: currentOnlineUsers
            });
        } catch (error) {
            console.error('Error getting online users:', error);
        }
    });

    // Handle disconnection
    socket.on('disconnect', () => {
        try {
            console.log('User disconnected:', socket.id);
            
            // Handle user going offline when disconnecting
            const userId = userSockets.get(socket.id);
            if (userId) {
                const userData = onlineUsers.get(userId);
                if (userData) {
                    console.log(`User ${userId} (${userData.username}) went offline due to disconnect`);
                    
                    // Broadcast offline status
                    socket.broadcast.emit('user-status-update', {
                        userId: parseInt(userId),
                        username: userData.username,
                        status: 'offline',
                        timestamp: new Date().toISOString()
                    });
                    
                    // Remove from online lists
                    onlineUsers.delete(userId);
                    userSockets.delete(socket.id);
                }
            }
            
            // Check if disconnected user was a broadcaster
            for (const [streamId, streamData] of streams.entries()) {
                if (streamData.broadcasterId === socket.id) {
                    // Broadcaster disconnected
                    console.log(`Broadcaster ${socket.id} disconnected from stream ${streamId}`);
                    io.to(`stream-${streamId}`).emit('stream-ended');
                    streams.delete(streamId);
                } else if (streamData.viewers.has(socket.id)) {
                    // Viewer disconnected
                    streamData.viewers.delete(socket.id);
                    io.to(`stream-${streamId}`).emit('viewer-count', {
                        count: streamData.viewers.size
                    });
                    console.log(`Viewer ${socket.id} disconnected from stream ${streamId}`);
                }
            }
        } catch (error) {
            console.error('Error in disconnect handling:', error);
        }
    });
});

const PORT = process.env.SIGNALING_SERVER_PORT || 3000;
http.listen(PORT, () => {
    console.log(`Signaling server running on port ${PORT}`);
    console.log(`CORS origin: ${process.env.APP_URL || "http://localhost:8000"}`);
});