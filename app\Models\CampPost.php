<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CampPost extends Model
{
    protected $fillable = [
        'camp_id',
        'user_id',
        'content',
        'media_url',
        'media_type'
    ];

    public function camp()
    {
        return $this->belongsTo(Camp::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
} 