<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class HeartReaction implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function broadcastOn()
    {
        return new Channel('livestream.' . $this->data['stream_id']);
    }

    public function broadcastAs()
    {
        return 'heart.reaction';
    }

    public function broadcastWith()
    {
        return [
            'user_id' => $this->data['user_id'],
            'username' => $this->data['username'],
            'timestamp' => now()->timestamp
        ];
    }
} 