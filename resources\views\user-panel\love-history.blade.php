@extends('layouts.app')

@section('content')
<div class="user-panel-container">
    <div class="panel-header">
        <h1>My Loves</h1>
    </div>
    
    <div class="love-stats-summary">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-heart"></i>
            </div>
            <div class="stat-details">
                <div class="stat-value">{{ number_format($lovesGiven) }}</div>
                <div class="stat-label">Loves Given</div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-dollar-sign"></i>
            </div>
            <div class="stat-details">
                <div class="stat-value">${{ number_format($totalSpent, 2) }}</div>
                <div class="stat-label">Total Spent</div>
            </div>
        </div>
    </div>
    
    <div class="filter-tabs">
        <a href="{{ route('user-panel.love-history', ['filter' => 'all']) }}" 
           class="filter-tab {{ request()->filter == 'all' || !request()->has('filter') ? 'active' : '' }}">
            All
        </a>
        <a href="{{ route('user-panel.love-history', ['filter' => 'free']) }}" 
           class="filter-tab {{ request()->filter == 'free' ? 'active' : '' }}">
            Free
        </a>
        <a href="{{ route('user-panel.love-history', ['filter' => 'paid']) }}" 
           class="filter-tab {{ request()->filter == 'paid' ? 'active' : '' }}">
            Paid
        </a>
    </div>
    
    <div class="love-history-list">
        @forelse($loves as $love)
            <div class="love-item">
                <div class="love-post-preview">
                    @if($love->post->media_type == 'image')
                        <img src="{{ asset('storage/' . $love->post->media_url) }}" alt="Post thumbnail">
                    @else
                        <div class="video-thumbnail">
                            <i class="fas fa-play-circle"></i>
                        </div>
                    @endif
                </div>
                
                <div class="love-details">
                    <div class="love-creator">
                        <img src="{{ $love->post->user->profile && $love->post->user->profile->photo ? asset('storage/' . $love->post->user->profile->photo) : asset('images/default-avatar.png') }}" 
                             alt="{{ $love->post->user->username }}" class="creator-avatar">
                        <span class="creator-name">{{ $love->post->user->username }}</span>
                    </div>
                    
                    <div class="love-caption">
                        {{ \Illuminate\Support\Str::limit($love->post->caption, 50) }}
                    </div>
                    
                    <div class="love-meta">
                        <div class="love-time">
                            <i class="far fa-clock"></i>
                            {{ $love->created_at->diffForHumans() }}
                        </div>
                        
                        <div class="love-type {{ $love->is_paid ? 'paid' : 'free' }}">
                            {{ $love->is_paid ? 'Paid' : 'Free' }}
                        </div>
                        
                        @if($love->is_paid)
                            <div class="love-amount">
                                <i class="fas fa-heart"></i>
                                <span>${{ number_format($love->amount, 2) }}</span>
                            </div>
                        @endif
                    </div>
                </div>
                
                <a href="{{ route('posts.show', $love->post_id) }}" class="view-post-btn">
                    <i class="fas fa-external-link-alt"></i>
                </a>
            </div>
        @empty
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="far fa-heart"></i>
                </div>
                <p>You haven't loved any posts yet.</p>
                <a href="{{ route('home') }}" class="btn-primary">Explore Posts</a>
            </div>
        @endforelse
    </div>
    
    <div class="pagination-container">
        {{ $loves->appends(request()->query())->links() }}
    </div>
</div>

<style>
    .user-panel-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .panel-header {
        margin-bottom: 20px;
    }
    
    .panel-header h1 {
        font-size: 24px;
        font-weight: bold;
    }
    
    .love-stats-summary {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 15px;
        margin-bottom: 30px;
    }
    
    .stat-card {
        background: #222;
        border-radius: 10px;
        padding: 15px;
        display: flex;
        align-items: center;
        gap: 15px;
    }
    
    .stat-icon {
        width: 40px;
        height: 40px;
        background: rgba(255, 215, 0, 0.1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        color: #FFD700;
    }
    
    .stat-value {
        font-size: 20px;
        font-weight: bold;
    }
    
    .stat-label {
        font-size: 14px;
        color: #888;
    }
    
    .filter-tabs {
        display: flex;
        margin-bottom: 20px;
        background: #222;
        border-radius: 8px;
        overflow: hidden;
    }
    
    .filter-tab {
        flex: 1;
        padding: 10px;
        text-align: center;
        color: #888;
        text-decoration: none;
        transition: all 0.3s ease;
    }
    
    .filter-tab.active {
        background: #FFD700;
        color: black;
        font-weight: bold;
    }
    
    .love-history-list {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }
    
    .love-item {
        background: #222;
        border-radius: 10px;
        padding: 15px;
        display: flex;
        gap: 15px;
        position: relative;
    }
    
    .love-post-preview {
        width: 80px;
        height: 80px;
        border-radius: 8px;
        overflow: hidden;
        flex-shrink: 0;
    }
    
    .love-post-preview img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .video-thumbnail {
        width: 100%;
        height: 100%;
        background: #333;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
    }
    
    .love-details {
        flex: 1;
    }
    
    .love-creator {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
    }
    
    .creator-avatar {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        object-fit: cover;
    }
    
    .creator-name {
        font-weight: bold;
        font-size: 14px;
    }
    
    .love-caption {
        font-size: 14px;
        margin-bottom: 10px;
        color: #ccc;
    }
    
    .love-meta {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        color: #888;
    }
    
    .view-post-btn {
        position: absolute;
        top: 15px;
        right: 15px;
        color: #888;
        font-size: 14px;
    }
    
    .empty-state {
        text-align: center;
        padding: 40px 0;
    }
    
    .empty-icon {
        font-size: 48px;
        color: #444;
        margin-bottom: 15px;
    }
    
    .btn-primary {
        display: inline-block;
        background: #FFD700;
        color: black;
        padding: 10px 20px;
        border-radius: 5px;
        text-decoration: none;
        font-weight: bold;
        margin-top: 15px;
    }
    
    .pagination-container {
        margin-top: 30px;
    }
    
    .love-type {
        padding: 3px 8px;
        border-radius: 4px;
        font-size: 12px;
    }
    
    .love-type.free {
        background: #444;
        color: #ccc;
    }
    
    .love-type.paid {
        background: rgba(255, 215, 0, 0.2);
        color: #FFD700;
    }
    
    @media (max-width: 600px) {
        .love-stats-summary {
            grid-template-columns: 1fr;
        }
    }
</style>
@endsection 