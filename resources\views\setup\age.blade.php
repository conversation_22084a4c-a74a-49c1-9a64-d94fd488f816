<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Birth Date - InstaPWA</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            background-color: #d31414;
            color: white;
            min-height: 100vh;
        }

        .container {
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }

        .logo {
            width: 150px;
            margin: 40px 0;
        }

        .title {
            font-size: 28px;
            margin-bottom: 40px;
            text-align: center;
        }

        .date-picker {
            display: flex;
            gap: 15px;
            margin-bottom: 60px;
            width: 100%;
            max-width: 360px;
        }

        .date-select {
            flex: 1;
            padding: 15px;
            background: rgba(0, 0, 0, 0.2);
            border: none;
            border-radius: 10px;
            color: white;
            font-size: 16px;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3e%3cpath d='M7,10L12,15L17,10H7Z'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 20px;
        }

        .date-select:focus {
            outline: none;
            background-color: rgba(0, 0, 0, 0.3);
        }

        .next-btn {
            width: 100%;
            padding: 15px;
            background: #FFD700;
            color: black;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin-top: auto;
            max-width: 360px;
        }

        .next-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .error-message {
            color: #FFD700;
            margin-bottom: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <img src="/images/logo.png" alt="InstaPWA Logo" class="logo">
        
        <div class="title">When were you born?</div>

        @if(session('error'))
            <div class="error-message">{{ session('error') }}</div>
        @endif

        @if($errors->any())
            <div class="error-message">{{ $errors->first() }}</div>
        @endif

        <form action="{{ route('setup.age') }}" method="POST" id="ageForm">
            @csrf
            <input type="hidden" name="birth_date" id="birthDateInput">
            
            <div class="date-picker">
                <select id="month" class="date-select" required>
                    <option value="" disabled selected>Month</option>
                    @foreach(range(1, 12) as $month)
                        <option value="{{ $month }}">{{ date('F', mktime(0, 0, 0, $month, 1)) }}</option>
                    @endforeach
                </select>

                <select id="day" class="date-select" required>
                    <option value="" disabled selected>Day</option>
                    @foreach(range(1, 31) as $day)
                        <option value="{{ $day }}">{{ $day }}</option>
                    @endforeach
                </select>

                <select id="year" class="date-select" required>
                    <option value="" disabled selected>Year</option>
                    @foreach(range(date('Y')-100, date('Y')-13) as $year)
                        <option value="{{ $year }}">{{ $year }}</option>
                    @endforeach
                </select>
            </div>

            <button type="submit" class="next-btn" id="nextBtn">Next</button>
        </form>
    </div>

    <script>
        document.getElementById('ageForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const month = document.getElementById('month').value;
            const day = document.getElementById('day').value;
            const year = document.getElementById('year').value;
            
            if (!month || !day || !year) {
                alert('Please select a complete birth date');
                return false;
            }
            
            // Format date as YYYY-MM-DD for backend
            const formattedMonth = month.padStart(2, '0');
            const formattedDay = day.padStart(2, '0');
            document.getElementById('birthDateInput').value = `${year}-${formattedMonth}-${formattedDay}`;
            
            this.submit();
        });
    </script>
</body>
</html> 