<?php

namespace App\Http\Controllers;

use Illuminate\Routing\Controller;
use Auth;
use App\Models\LiveStream;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use App\Notifications\LiveStreamStarted;
use Illuminate\Support\Facades\Notification;
use App\Http\Controllers\Controller as BaseController;
use App\Notifications\PrivateStreamInvite;
use Illuminate\Database\QueryException;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Str;
use App\Events\LiveStreamStartedEvent;

class LiveController extends BaseController
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function create()
    {
        return view('live.create');
    }

    public function store(Request $request)
    {
        try {
            $request->validate([
                'title' => 'required|string|max:255',
                'visibility' => 'required|in:public,private',
            ]);

            // Ensure required fields exist
            $streamData = [
                'user_id' => Auth::id(),
                'title' => $request->title,
                'visibility' => $request->visibility,
                'start_time' => now(),
                'is_live' => true,
                'stream_key' => Str::random(16),
                'stream_url' => 'rtmp://your-server.com/live/' . Str::random(20)
            ];

            if (!array_key_exists('stream_key', $streamData) || !array_key_exists('stream_url', $streamData)) {
                throw new \Exception('Missing required stream configuration fields');
            }

            $stream = LiveStream::create($streamData);

            if ($stream->visibility === 'public') {
                $this->sendPublicNotification($stream);
            }
            
            return response()->json([
                'status' => 'success',
                'stream_id' => $stream->id,
                'stream_url' => $stream->stream_url
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->errors()
            ], 422);
        } catch (QueryException $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Database error: ' . $e->getMessage()
            ], 500);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Server error: ' . $e->getMessage()
            ], 500);
        }
    }

    private function sendPublicNotification(LiveStream $stream)
    {
        $notification = new LiveStreamStarted($stream);
        Notification::send($stream->user->followers, $notification);
        
        // Real-time broadcast
        broadcast(new LiveStreamStartedEvent($stream))->toOthers();
    }

    private function sendPrivateInvites(LiveStream $stream)
    {
        $stream->user->followers()->approved()->chunk(100, fn($followers) => 
            Notification::send($followers, new PrivateStreamInvite($stream))
        );
    }

    public function end(LiveStream $stream)
    {
        if ($stream->user_id !== Auth::id()) {
            abort(403);
        }

        $stream->update([
            'end_time' => now(),
            'is_live' => false
        ]);

        // Only create post if there's a recording
        if ($stream->recording_path) {
            $post = $stream->user->posts()->create([
                'caption' => "Live stream recording: {$stream->title}",
                'media_url' => $stream->recording_path,
                'media_type' => 'video',
                'visibility' => 'public'
            ]);
            
            return response()->json([
                'status' => 'success',
                'post_url' => route('posts.show', $post)
            ]);
        }
        
                 // If no recording, just redirect to home
        return response()->json([
            'status' => 'success',
            'post_url' => route('home')
        ]);
    }

    public function uploadRecording(Request $request, LiveStream $stream)
    {
        $request->validate([
            'recording' => 'required|file|mimetypes:video/webm,video/mp4|max:102400' // 100MB max
        ]);

        if ($stream->user_id === Auth::id()) {
            $path = $request->file('recording')->store('recordings', 'public');
            
            $stream->update([
                'stream_url' => Storage::url($path),
                'end_time' => now()
            ]);

            return response()->json(['status' => 'success']);
        }

        return response()->json(['status' => 'error'], 403);
    }

    public function index()
    {
        $streams = LiveStream::where('is_live', true)
                    ->where('visibility', 'public')
                    ->with('user')
                    ->latest()
                    ->paginate(10);

        $scheduledStreams = LiveStream::where('start_time', '>', now())
            ->where('visibility', 'public')
            ->with('user')
            ->orderBy('start_time')
            ->take(5)
            ->get();
       
        return view('live.index', [
            'streams' => $streams,
            'scheduledStreams' => $scheduledStreams
        ]);
    }

    public function show(LiveStream $stream)
    {
        // Enhanced privacy check
        if ($stream->visibility === 'private' && 
            !$stream->user->followers->contains(Auth::id()) &&
            $stream->user_id !== Auth::id()) {
            abort(403, 'This is a private stream for approved followers only');
        }

        return view('live.show', [
            'stream' => $stream,
            'streamUrl' => $stream->stream_url,
            'isFollowing' => Auth::user()->isFollowing($stream->user)
        ]);
    }
} 