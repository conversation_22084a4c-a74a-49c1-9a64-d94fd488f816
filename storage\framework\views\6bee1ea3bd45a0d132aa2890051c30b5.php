<?php $__env->startSection('content'); ?>
<meta name="signaling-server" content="<?php echo e(env('SIGNALING_SERVER_URL', 'http://localhost:3000')); ?>">

<style>
    * {
        box-sizing: border-box;
    }

    body {
        margin: 0;
        padding: 0;
        height: 100vh;
        overflow: hidden;
        background: #000;
        color: white;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    }

    .navbar {
        display: none !important;
    }

    main {
        padding: 0 !important;
        margin: 0 !important;
        height: 100vh;
    }

    .chat-container {
        height: 100vh;
        display: flex;
        flex-direction: column;
        background: #000;
    }

    /* Chat Header */
    .chat-header {
        padding: 15px 20px;
        background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
        display: flex;
        align-items: center;
        gap: 15px;
        border-bottom: 1px solid #333;
        position: sticky;
        top: 0;
        z-index: 100;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    }

    .back-button {
        color: white;
        text-decoration: none;
        font-size: 20px;
        padding: 8px;
        border-radius: 50%;
        transition: background 0.2s ease;
    }

    .back-button:hover {
        background: rgba(255, 255, 255, 0.1);
    }

    .user-avatar {
        width: 45px;
        height: 45px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid #333;
    }

    .live-avatar {
        border-color: #FF3B30 !important;
        animation: pulse-live 2s infinite;
    }

    @keyframes pulse-live {
        0% { border-color: #FF3B30; }
        50% { border-color: #FF6B60; }
        100% { border-color: #FF3B30; }
    }

    .user-info {
        flex: 1;
    }

    .user-name {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 2px;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .live-badge {
        background: #FF3B30;
        color: white;
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: bold;
        animation: pulse-text 2s infinite;
    }

    @keyframes pulse-text {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
    }

    .user-status {
        font-size: 13px;
        color: #888;
    }

    .header-actions {
        display: flex;
        gap: 15px;
    }

    .action-btn {
        background: rgba(255, 255, 255, 0.1);
        border: none;
        color: white;
        padding: 10px;
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 16px;
    }

    .action-btn:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: scale(1.05);
    }

    .live-btn {
        background: #FF3B30;
    }

    .live-btn:hover {
        background: #FF6B60;
    }

    /* Messages Container */
    .messages-container {
        flex: 1;
        overflow-y: auto;
        padding: 20px;
        background: #000;
        scrollbar-width: thin;
        scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
    }

    .messages-container::-webkit-scrollbar {
        width: 4px;
    }

    .messages-container::-webkit-scrollbar-track {
        background: transparent;
    }

    .messages-container::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3);
        border-radius: 2px;
    }

    /* Message Bubbles */
    .message {
        margin-bottom: 15px;
        display: flex;
        animation: slideInFromBottom 0.3s ease-out;
    }

    @keyframes slideInFromBottom {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .message.sent {
        justify-content: flex-end;
    }

    .message.received {
        justify-content: flex-start;
    }

    .message-bubble {
        max-width: 70%;
        padding: 12px 16px;
        border-radius: 20px;
        position: relative;
        word-wrap: break-word;
    }

    .message.sent .message-bubble {
        background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
        color: white;
        border-bottom-right-radius: 8px;
    }

    .message.received .message-bubble {
        background: rgba(255, 255, 255, 0.1);
        color: white;
        border-bottom-left-radius: 8px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .message-content {
        font-size: 16px;
        line-height: 1.4;
    }

    .message-time {
        font-size: 11px;
        color: rgba(255, 255, 255, 0.6);
        margin-top: 5px;
        text-align: right;
    }

    .message.received .message-time {
        text-align: left;
    }

    /* Media Messages */
    .message-media {
        border-radius: 12px;
        overflow: hidden;
        margin-bottom: 8px;
        max-width: 250px;
    }

    .message-media img,
    .message-media video {
        width: 100%;
        height: auto;
        display: block;
    }

    /* Message Input */
    .message-input-container {
        padding: 15px 20px;
        background: rgba(255, 255, 255, 0.05);
        border-top: 1px solid #333;
        backdrop-filter: blur(10px);
    }

    .message-form {
        display: flex;
        gap: 12px;
        align-items: center;
    }

    .input-wrapper {
        flex: 1;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 25px;
        padding: 8px 16px;
        display: flex;
        align-items: center;
        gap: 8px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.2s ease;
    }

    .input-wrapper:focus-within {
        background: rgba(255, 255, 255, 0.15);
        border-color: #007AFF;
        box-shadow: 0 0 15px rgba(0, 122, 255, 0.2);
    }

    .message-input {
        flex: 1;
        background: transparent;
        border: none;
        color: white;
        font-size: 16px;
        padding: 8px 0;
        outline: none;
        resize: none;
        max-height: 100px;
    }

    .message-input::placeholder {
        color: rgba(255, 255, 255, 0.5);
    }

    .media-btn {
        background: transparent;
        border: none;
        color: rgba(255, 255, 255, 0.6);
        cursor: pointer;
        padding: 4px;
        border-radius: 50%;
        transition: all 0.2s ease;
        font-size: 18px;
    }

    .media-btn:hover {
        color: #007AFF;
        background: rgba(0, 122, 255, 0.1);
    }

    .message-input-container {
        padding-bottom: 80px !important;
    }

    .send-btn {
        background: #007AFF;
        border: none;
        color: white;
        padding: 12px 16px;
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .send-btn:hover {
        background: #0056CC;
        transform: scale(1.05);
    }

    .send-btn:active {
        transform: scale(0.98);
    }

    .send-btn:disabled {
        background: rgba(255, 255, 255, 0.2);
        cursor: not-allowed;
        transform: none;
    }

    /* Connection Status */
    .connection-status {
        position: fixed;
        top: 70px;
        right: 20px;
        padding: 8px 12px;
        border-radius: 15px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        font-size: 12px;
        display: flex;
        align-items: center;
        gap: 6px;
        z-index: 200;
        backdrop-filter: blur(10px);
        transition: all 0.2s ease;
    }

    .status-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #ffc107;
    }

    .status-indicator.connected {
        background: #4CAF50;
    }

    .status-indicator.disconnected {
        background: #f44336;
    }

    /* File input hidden */
    #media-input {
        display: none;
    }

    /* Empty state */
    .empty-state {
        text-align: center;
        padding: 40px 20px;
        color: #666;
    }

    .empty-state-icon {
        font-size: 48px;
        margin-bottom: 16px;
    }

    .empty-state-text {
        font-size: 16px;
        line-height: 1.5;
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .message-bubble {
            max-width: 85%;
        }

        .chat-header {
            padding: 12px 16px;
        }

        .user-name {
            font-size: 16px;
        }

        .message-input-container {
            padding: 12px 16px;
        }

        .messages-container {
            padding: 16px;
        }
    }
</style>

<div class="chat-container">
    <!-- Chat Header -->
    <div class="chat-header">
        <a href="<?php echo e(route('chat.index')); ?>" class="back-button">
            <i class="fas fa-arrow-left"></i>
        </a>

        <div class="user-avatar-container">
            <?php if($user->profile && $user->profile->photo): ?>
                <img src="<?php echo e(asset('storage/' . $user->profile->photo)); ?>"
                     alt="<?php echo e($user->username); ?>"
                     class="user-avatar <?php echo e($liveStream ? 'live-avatar' : ''); ?>">
            <?php else: ?>
                <img src="/images/default-avatar.png"
                     alt="<?php echo e($user->username); ?>"
                     class="user-avatar <?php echo e($liveStream ? 'live-avatar' : ''); ?>">
            <?php endif; ?>
        </div>

        <div class="user-info">
            <div class="user-name">
                <?php echo e($user->name ?? $user->username); ?>

                <?php if($user->is_verified): ?>
                    <i class="fas fa-check-circle" style="color: #FFD700;"></i>
                <?php endif; ?>
                <?php if($liveStream): ?>
                    <span class="live-badge">🔴 LIVE</span>
                <?php endif; ?>
            </div>
            <div class="user-status">{{ $user->username }}</div>
        </div>

        <div class="header-actions">
            <?php if($liveStream): ?>
                <a href="<?php echo e(route('livestream.show', $liveStream->id)); ?>" class="action-btn live-btn" title="Watch Live Stream">
                    <i class="fas fa-video"></i>
                </a>
            <?php endif; ?>
            <!-- <button class="action-btn" title="Video Call">
                <i class="fas fa-video"></i>
            </button>
            <button class="action-btn" title="Voice Call">
                <i class="fas fa-phone"></i>
            </button> -->
        </div>
    </div>

    <!-- Connection Status -->
    <div class="connection-status" id="connection-status" style="display: none;">
        <span class="status-indicator" id="status-indicator"></span>
        <span id="status-text">Connecting...</span>
    </div>

    <!-- Messages Container -->
    <div class="messages-container" id="messages-container">
        <?php if($chats->isEmpty()): ?>
            <div class="empty-state">
                <div class="empty-state-icon">💬</div>
                <div class="empty-state-text">
                    Start a conversation with <?php echo e($user->name ?? $user->username); ?>!<br>
                    <?php if($liveStream): ?>
                        They're currently live streaming.
                    <?php else: ?>
                        Send them a message and they'll see it when they're online.
                    <?php endif; ?>
                </div>
            </div>
        <?php else: ?>
            <?php $__currentLoopData = $chats; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $chat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="message <?php echo e($chat->sender_id === auth()->id() ? 'sent' : 'received'); ?>">
                    <div class="message-bubble">
                        <?php if($chat->media_url): ?>
                            <div class="message-media">
                                <?php if($chat->media_type === 'image'): ?>
                                    <img src="<?php echo e(asset('storage/' . $chat->media_url)); ?>" alt="Image">
                                <?php elseif($chat->media_type === 'video'): ?>
                                    <video controls>
                                        <source src="<?php echo e(asset('storage/' . $chat->media_url)); ?>" type="video/mp4">
                                    </video>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>

                        <?php if($chat->message): ?>
                            <div class="message-content"><?php echo e($chat->message); ?></div>
                        <?php endif; ?>

                        <div class="message-time">
                            <?php echo e($chat->created_at->diffForHumans()); ?>

                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php endif; ?>
    </div>

    <!-- Message Input -->
    <div class="message-input-container">
        <form class="message-form" id="chat-form" enctype="multipart/form-data">
            <?php echo csrf_field(); ?>
            <div class="input-wrapper">
                <!-- <button type="button" class="media-btn" onclick="document.getElementById('media-input').click()">
                    <i class="fas fa-camera"></i>
                </button> -->
                <textarea
                    class="message-input"
                    id="message-input"
                    placeholder="Message <?php echo e($user->username); ?>..."
                    rows="1"
                    autocomplete="off"></textarea>
                <input type="file" id="media-input" name="media" accept="image/*,video/*" style="display: none;">
            </div>
            <button type="submit" class="send-btn" id="send-btn">
                <i class="fas fa-paper-plane"></i>
            </button>
        </form>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.socket.io/4.5.0/socket.io.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('=== CHAT SCRIPT STARTING ===');

    const userId = <?php echo e(auth()->id()); ?>;
    const targetUserId = <?php echo e($user->id); ?>;
    const signalingServerUrl = document.querySelector('meta[name="signaling-server"]')?.content || "<?php echo e(env('SIGNALING_SERVER_URL')); ?>";

    // Get current user data safely
    const currentUser = <?php echo json_encode([
        'id' => auth()->id(), 'username' => auth()->user()->username ?? 'Guest'
    ], 512) ?>;

    console.log('Chat initialized:', {
        userId: userId,
        targetUserId: targetUserId,
        signalingServerUrl: signalingServerUrl,
        currentUser: currentUser
    });

    // Store in window scope for chat access
    window.currentUser = currentUser;

    // DOM elements
    const messagesContainer = document.getElementById('messages-container');
    const chatForm = document.getElementById('chat-form');
    const messageInput = document.getElementById('message-input');
    const sendBtn = document.getElementById('send-btn');
    const mediaInput = document.getElementById('media-input');
    const connectionStatus = document.getElementById('connection-status');
    const statusIndicator = document.getElementById('status-indicator');
    const statusText = document.getElementById('status-text');

    // Initialize Socket.IO
    let socket;
    try {
        socket = io(signalingServerUrl);
        console.log('Socket.IO initialized');
        console.log('Current user data:', currentUser);

        // Show connection status
        connectionStatus.style.display = 'flex';

        socket.on('connect', () => {
            console.log('Connected to chat server');
            updateConnectionStatus('connected', 'Online');

            // Emit that user is online on individual chat page
            if (currentUser && currentUser.id) {
                socket.emit('user-online', {
                    userId: userId.toString(),
                    username: currentUser.username || 'Guest',
                    currentPage: `/chats/${targetUserId}`
                });
            }
        });

        socket.on('disconnect', () => {
            console.log('Disconnected from chat server');
            updateConnectionStatus('disconnected', 'Offline');
        });

        socket.on('connect_error', (error) => {
            console.error('Connection error:', error);
            updateConnectionStatus('disconnected', 'Connection error');
        });

        // Listen for new messages
        socket.on('new-message', (data) => {
            console.log('Received new message:', data);
            if (data.sender_id === targetUserId || data.receiver_id === targetUserId) {
                appendMessage(data);

                // Mark messages as read if user is viewing this chat
                if (data.sender_id === targetUserId && !document.hidden) {
                    markMessagesAsRead();
                }
            }
        });

        // Listen for new chat notifications (when user is in different chat)
        socket.on('new-chat-notification', (data) => {
            console.log('New chat notification received:', data);
            // Only show notification if it's not from the current chat user
            if (data.sender_id !== targetUserId) {
                showChatNotification(data);
            }
        });

        // Listen for user status updates
        socket.on('user-status-update', (data) => {
            try {
                console.log('User status update in chat:', data);
                if (data && data.userId === targetUserId) {
                    updateTargetUserStatus(data);
                }
            } catch (error) {
                console.error('Error handling user status update:', error, data);
            }
        });

        // Listen for online users list
        socket.on('online-users-list', (data) => {
            try {
                console.log('Online users received in chat:', data.onlineUsers);
                if (data && data.onlineUsers && Array.isArray(data.onlineUsers)) {
                    const targetUser = data.onlineUsers.find(user => user.userId === targetUserId);
                    if (targetUser) {
                        updateTargetUserStatus(targetUser);
                    }
                }
            } catch (error) {
                console.error('Error handling online users list:', error, data);
            }
        });

    } catch (error) {
        console.error('Socket.IO initialization failed:', error);
        // Hide connection status if socket fails
        connectionStatus.style.display = 'none';
    }

    // Handle page unload (user leaving)
    window.addEventListener('beforeunload', () => {
        if (socket && socket.connected && currentUser && currentUser.id) {
            socket.emit('user-offline', {
                userId: userId.toString()
            });
        }
    });

    // Handle visibility change (tab switching)
    document.addEventListener('visibilitychange', () => {
        if (socket && socket.connected && currentUser && currentUser.id) {
            if (document.hidden) {
                socket.emit('user-offline', {
                    userId: userId.toString()
                });
            } else {
                socket.emit('user-online', {
                    userId: userId.toString(),
                    username: currentUser.username || 'Guest',
                    currentPage: `/chats/${targetUserId}`
                });
            }
        }
    });

    // Function to update target user status in header
    function updateTargetUserStatus(userData) {
        const userStatus = document.querySelector('.user-status');
        const liveBadge = document.querySelector('.live-badge');
        const userAvatar = document.querySelector('.user-avatar');

        // Remove existing status indicators
        if (liveBadge) {
            liveBadge.remove();
        }

        // Update status based on user data - with null checks
        if (userStatus) {
            if (userData.status === 'online') {
                userStatus.innerHTML = '🟢 Online';
            } else {
                userStatus.innerHTML = `{{ $user->username }}`;
            }
        } else {
            console.warn('User status element not found');
        }

        // Update avatar border - with null checks
        if (userAvatar) {
            if (userData.status === 'online' && !userAvatar.classList.contains('live-avatar')) {
                userAvatar.style.border = '2px solid #4CAF50';
            } else if (!userAvatar.classList.contains('live-avatar')) {
                userAvatar.style.border = '2px solid #333';
            }
        } else {
            console.warn('User avatar element not found');
        }
    }

    // Update connection status
    function updateConnectionStatus(status, message) {
        statusIndicator.className = 'status-indicator ' + status;
        statusText.textContent = message;

        // Hide after 3 seconds if connected
        if (status === 'connected') {
            setTimeout(() => {
                connectionStatus.style.display = 'none';
            }, 3000);
        }
    }

    // Auto-resize textarea
    messageInput.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = Math.min(this.scrollHeight, 100) + 'px';

        // Enable/disable send button
        sendBtn.disabled = !this.value.trim() && !mediaInput.files.length;
    });

    // Handle form submission
    chatForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        const message = messageInput.value.trim();
        const mediaFile = mediaInput.files[0];

        if (!message && !mediaFile) return;

        // Disable form while sending
        sendBtn.disabled = true;
        messageInput.disabled = true;

        try {
            const formData = new FormData();
            if (message) formData.append('message', message);
            if (mediaFile) formData.append('media', mediaFile);

            const response = await fetch(`/chats/<?php echo e($user->id); ?>`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json',
                },
            });

            const result = await response.json();

            if (result.success) {
                // Append message immediately for sender
                appendMessage({
                    ...result.chat,
                    sender_id: userId,
                    receiver_id: targetUserId
                });

                // Emit via socket for real-time delivery
                if (socket && socket.connected) {
                    socket.emit('send-message', {
                        sender_id: userId,
                        receiver_id: targetUserId,
                        message: result.chat.message,
                        media_url: result.chat.media_url,
                        media_type: result.chat.media_type,
                        created_at: result.chat.created_at,
                        sender: result.chat.sender
                    });
                }

                // Clear form
                messageInput.value = '';
                messageInput.style.height = 'auto';
                mediaInput.value = '';
            } else {
                alert('Failed to send message: ' + result.message);
            }
        } catch (error) {
            console.error('Error sending message:', error);
            alert('Failed to send message. Please try again.');
        } finally {
            // Re-enable form
            sendBtn.disabled = false;
            messageInput.disabled = false;
            messageInput.focus();
        }
    });

    // Handle media file selection
    mediaInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            // Enable send button when file is selected
            sendBtn.disabled = false;

            // Show file preview (optional)
            console.log('File selected:', file.name, file.type);
        }
    });

    // Append message to chat
    function appendMessage(data) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${data.sender_id === userId ? 'sent' : 'received'}`;

        let mediaHtml = '';
        if (data.media_url) {
            const mediaUrl = data.media_url.startsWith('http') ? data.media_url : `/storage/${data.media_url}`;
            if (data.media_type === 'image') {
                mediaHtml = `<div class="message-media"><img src="${mediaUrl}" alt="Image"></div>`;
            } else if (data.media_type === 'video') {
                mediaHtml = `<div class="message-media"><video controls><source src="${mediaUrl}" type="video/mp4"></video></div>`;
            }
        }

        const messageContent = data.message ? `<div class="message-content">${escapeHtml(data.message)}</div>` : '';

        messageDiv.innerHTML = `
            <div class="message-bubble">
                ${mediaHtml}
                ${messageContent}
                <div class="message-time">${data.created_at}</div>
            </div>
        `;

        messagesContainer.appendChild(messageDiv);
        scrollToBottom();
    }

    // Scroll to bottom of messages
    function scrollToBottom() {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    // Escape HTML to prevent XSS
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Enter key to send (Shift+Enter for new line)
    messageInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            chatForm.dispatchEvent(new Event('submit'));
        }
    });

    // Initial scroll to bottom
    scrollToBottom();

    // Function to show chat notification
    function showChatNotification(data) {
        // Request notification permission if not granted
        if ('Notification' in window) {
            if (Notification.permission === 'granted') {
                const notification = new Notification(`New message from ${data.sender_username}`, {
                    body: data.message,
                    icon: data.sender_avatar || '/images/default-avatar.png',
                    tag: `chat-${data.sender_id}`,
                    requireInteraction: false
                });

                notification.onclick = function() {
                    window.focus();
                    window.location.href = data.chat_url;
                    notification.close();
                };

                // Auto close after 5 seconds
                setTimeout(() => notification.close(), 5000);
            } else if (Notification.permission !== 'denied') {
                Notification.requestPermission().then(permission => {
                    if (permission === 'granted') {
                        showChatNotification(data);
                    }
                });
            }
        }

        // Show in-app notification as fallback
        showInAppNotification(data);
    }

    // Function to show in-app notification
    function showInAppNotification(data) {
        const notification = document.createElement('div');
        notification.className = 'in-app-notification';
        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-avatar">
                    <img src="${data.sender_avatar || '/images/default-avatar.png'}" alt="${data.sender_username}">
                </div>
                <div class="notification-text">
                    <div class="notification-title">${data.sender_username}</div>
                    <div class="notification-message">${data.message}</div>
                </div>
                <button class="notification-close">&times;</button>
            </div>
        `;

        // Add styles
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #222;
            color: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 10000;
            max-width: 300px;
            cursor: pointer;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Click to open chat
        notification.addEventListener('click', (e) => {
            if (!e.target.classList.contains('notification-close')) {
                window.location.href = data.chat_url;
            }
        });

        // Close button
        notification.querySelector('.notification-close').addEventListener('click', (e) => {
            e.stopPropagation();
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => notification.remove(), 300);
        });

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => notification.remove(), 300);
            }
        }, 5000);
    }

    // Function to mark messages as read
    function markMessagesAsRead() {
        fetch(`/api/chat/mark-read/<?php echo e($user->id); ?>`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Accept': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('Messages marked as read');
            }
        })
        .catch(error => console.error('Error marking messages as read:', error));
    }

    console.log('=== CHAT SCRIPT INITIALIZATION COMPLETE ===');
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\instapwa\resources\views/chat/show.blade.php ENDPATH**/ ?>