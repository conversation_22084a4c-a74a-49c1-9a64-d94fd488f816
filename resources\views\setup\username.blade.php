<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Set Username - InstaPWA</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            background-color: #d31414;
            color: white;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .container {
            width: 90%;
            max-width: 400px;
            text-align: center;
        }
        h1 {
            margin-bottom: 30px;
        }
        form {
            display: flex;
            flex-direction: column;
        }
        input {
            padding: 15px;
            margin-bottom: 20px;
            border: none;
            border-radius: 50px;
            font-size: 16px;
        }
        button {
            padding: 15px;
            background-color: #FFD700;
            color: black;
            border: none;
            border-radius: 50px;
            font-weight: bold;
            font-size: 16px;
            cursor: pointer;
        }
        .error {
            color: #FFD700;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Choose Your Username</h1>
        
        @if ($errors->any())
            <div class="error">
                {{ $errors->first('username') }}
            </div>
        @endif
        
        <form action="{{ route('setup.username') }}" method="POST">
            @csrf
            <input type="text" name="username" placeholder="Username" value="{{ old('username') }}" required>
            <button type="submit">Continue</button>
        </form>
    </div>
</body>
</html> 