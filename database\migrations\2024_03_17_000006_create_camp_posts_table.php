<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('camp_posts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('camp_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->text('content');
            $table->string('media_url')->nullable();
            $table->string('media_type')->nullable(); // image, video, document
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('camp_posts');
    }
}; 