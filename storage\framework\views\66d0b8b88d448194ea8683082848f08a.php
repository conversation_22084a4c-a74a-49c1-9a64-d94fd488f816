

<?php $__env->startSection('content'); ?>
<style>
    .notifications-container {
        background: #111;
        min-height: calc(100vh - 120px);
    }

    .notifications-header {
        padding: 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #222;
    }

    .date-header {
        padding: 10px 15px;
        background: #222;
        font-size: 14px;
        color: #888;
    }

    .notification-item {
        display: flex;
        padding: 15px;
        gap: 15px;
        border-bottom: 1px solid #222;
        position: relative;
    }

    .notification-avatar {
        width: 44px;
        height: 44px;
        border-radius: 50%;
        object-fit: cover;
    }

    .notification-content {
        flex: 1;
        font-size: 14px;
        line-height: 1.4;
    }

    .notification-time {
        color: #666;
        font-size: 12px;
        margin-top: 5px;
    }

    .notification-media {
        width: 44px;
        height: 44px;
        object-fit: cover;
    }

    .unread-indicator {
        width: 8px;
        height: 8px;
        background: #FFD700;
        border-radius: 50%;
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
    }

    .mark-all-read {
        color: #FFD700;
        background: none;
        border: none;
        font-size: 14px;
        cursor: pointer;
    }
</style>

<div class="notifications-container">
    <div class="notifications-header">
        <h2>Notifications</h2>
        <button class="mark-all-read" onclick="markAllAsRead()">Mark all as read</button>
    </div>

    <?php $__empty_1 = true; $__currentLoopData = $notifications->groupBy(function($notification) {
        return $notification->created_at->format('Y-m-d');
    }); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $date => $groupedNotifications): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
        <div class="date-header">
            <?php echo e(\Carbon\Carbon::parse($date)->diffForHumans(['parts' => 1])); ?>

        </div>

        <?php $__currentLoopData = $groupedNotifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="notification-item <?php echo e($notification->read_at ? '' : 'unread'); ?>">
                <?php if($notification->type == 'love'): ?>
                    <?php
                        $data = json_decode($notification->data);
                        $amount = $data->amount ?? 0.01;
                    ?>
                    <div class="notification-icon love">
                        <i class="fas fa-heart"></i>
                    </div>
                    <div class="notification-content">
                        <p><strong><?php echo e('@' . $data->username); ?></strong> loved your post and you earned $<?php echo e(number_format($amount, 2)); ?></p>
                        <small><?php echo e($notification->created_at->diffForHumans()); ?></small>
                    </div>
                <?php endif; ?>
                
                <!-- Other notification types -->
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
        <div style="text-align: center; padding: 40px; color: #666;">
            No notifications yet
        </div>
    <?php endif; ?>
</div>

<script>
function markAsRead(id) {
    fetch(`/notifications/${id}/mark-as-read`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
            'Accept': 'application/json'
        }
    });
}

function markAllAsRead() {
    fetch('/notifications/mark-all-as-read', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
            'Accept': 'application/json'
        }
    }).then(() => {
        location.reload();
    });
}
</script>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\instapwa\resources\views/notifications/index.blade.php ENDPATH**/ ?>