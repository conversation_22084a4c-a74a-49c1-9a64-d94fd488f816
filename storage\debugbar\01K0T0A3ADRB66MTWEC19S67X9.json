{"__meta": {"id": "01K0T0A3ADRB66MTWEC19S67X9", "datetime": "2025-07-22 21:23:23", "utime": **********.086032, "method": "GET", "uri": "/home", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.739672, "end": **********.086049, "duration": 0.3463771343231201, "duration_str": "346ms", "measures": [{"label": "Booting", "start": **********.739672, "relative_start": 0, "end": **********.861695, "relative_end": **********.861695, "duration": 0.*****************, "duration_str": "122ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.861703, "relative_start": 0.*****************, "end": **********.086051, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "224ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.875297, "relative_start": 0.*****************, "end": **********.877339, "relative_end": **********.877339, "duration": 0.0020418167114257812, "duration_str": "2.04ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.9381, "relative_start": 0.*****************, "end": **********.084177, "relative_end": **********.084177, "duration": 0.*****************, "duration_str": "146ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: home", "start": **********.939866, "relative_start": 0.****************, "end": **********.939866, "relative_end": **********.939866, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.951942, "relative_start": 0.*****************, "end": **********.951942, "relative_end": **********.951942, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.958807, "relative_start": 0.21913504600524902, "end": **********.958807, "relative_end": **********.958807, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.965147, "relative_start": 0.22547507286071777, "end": **********.965147, "relative_end": **********.965147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.971827, "relative_start": 0.23215508460998535, "end": **********.971827, "relative_end": **********.971827, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.978225, "relative_start": 0.23855304718017578, "end": **********.978225, "relative_end": **********.978225, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.984465, "relative_start": 0.24479293823242188, "end": **********.984465, "relative_end": **********.984465, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.990372, "relative_start": 0.2506999969482422, "end": **********.990372, "relative_end": **********.990372, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.995896, "relative_start": 0.2562241554260254, "end": **********.995896, "relative_end": **********.995896, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.002229, "relative_start": 0.2625570297241211, "end": **********.002229, "relative_end": **********.002229, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.008564, "relative_start": 0.2688920497894287, "end": **********.008564, "relative_end": **********.008564, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.014762, "relative_start": 0.27508997917175293, "end": **********.014762, "relative_end": **********.014762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.02148, "relative_start": 0.28180813789367676, "end": **********.02148, "relative_end": **********.02148, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.027433, "relative_start": 0.28776097297668457, "end": **********.027433, "relative_end": **********.027433, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.033795, "relative_start": 0.29412317276000977, "end": **********.033795, "relative_end": **********.033795, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.040696, "relative_start": 0.3010239601135254, "end": **********.040696, "relative_end": **********.040696, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.046868, "relative_start": 0.30719614028930664, "end": **********.046868, "relative_end": **********.046868, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.052853, "relative_start": 0.31318116188049316, "end": **********.052853, "relative_end": **********.052853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.058607, "relative_start": 0.3189351558685303, "end": **********.058607, "relative_end": **********.058607, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.064215, "relative_start": 0.3245429992675781, "end": **********.064215, "relative_end": **********.064215, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.070756, "relative_start": 0.3310840129852295, "end": **********.070756, "relative_end": **********.070756, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.app", "start": **********.077216, "relative_start": 0.33754396438598633, "end": **********.077216, "relative_end": **********.077216, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 26989600, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 22, "nb_templates": 22, "templates": [{"name": "home", "param_count": null, "params": [], "start": **********.939818, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.phphome", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=1", "ajax": false, "filename": "home.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.951905, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.958769, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.965109, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.971787, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.978188, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.984428, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.990333, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.99586, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.002189, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.008526, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.014726, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.021441, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.027396, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.033757, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.040659, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.046831, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.052814, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.058571, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.064168, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.070718, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "layouts.app", "param_count": null, "params": [], "start": **********.07718, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}]}, "queries": {"count": 75, "nb_statements": 75, "nb_visible_statements": 75, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.042110000000000015, "accumulated_duration_str": "42.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'ysQ2OZdBk91GzyxsvUNjStsqoUKCd3cWniDGhfUE' limit 1", "type": "query", "params": [], "bindings": ["ysQ2OZdBk91GzyxsvUNjStsqoUKCd3cWniDGhfUE"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.8851151, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "instapwa", "explain": null, "start_percent": 0, "width_percent": 3.586}, {"sql": "select * from `users` where `id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.897584, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "instapwa", "explain": null, "start_percent": 3.586, "width_percent": 1.187}, {"sql": "select * from `live_streams` where `is_live` = 1 and `visibility` = 'public' order by `started_at` desc limit 10", "type": "query", "params": [], "bindings": [1, "public"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 21}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.9015388, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:21", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=21", "ajax": false, "filename": "HomeController.php", "line": "21"}, "connection": "instapwa", "explain": null, "start_percent": 4.773, "width_percent": 1.401}, {"sql": "select * from `posts` where `user_id` in (select `followed_id` from `followers` where `follower_id` = 28 and `approved` = 1) or `user_id` = 28 or `visibility` = 'public' order by `created_at` desc limit 20", "type": "query", "params": [], "bindings": [28, 1, 28, "public"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.904404, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:35", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=35", "ajax": false, "filename": "HomeController.php", "line": "35"}, "connection": "instapwa", "explain": null, "start_percent": 6.174, "width_percent": 1.615}, {"sql": "select * from `users` where `users`.`id` in (1, 3, 4, 6, 7, 8, 27)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.9078639, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:35", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=35", "ajax": false, "filename": "HomeController.php", "line": "35"}, "connection": "instapwa", "explain": null, "start_percent": 7.789, "width_percent": 1.401}, {"sql": "select * from `user_profiles` where `user_profiles`.`user_id` in (1, 3, 4, 6, 7, 8, 27)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.911658, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:35", "source": {"index": 25, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=35", "ajax": false, "filename": "HomeController.php", "line": "35"}, "connection": "instapwa", "explain": null, "start_percent": 9.19, "width_percent": 1.187}, {"sql": "select * from `love` where `love`.`post_id` in (28, 29, 30, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.914167, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:35", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=35", "ajax": false, "filename": "HomeController.php", "line": "35"}, "connection": "instapwa", "explain": null, "start_percent": 10.378, "width_percent": 0.95}, {"sql": "select * from `users` where `id` != 28 and `id` not in (select `followed_id` from `followers` where `follower_id` = 28) order by RAND() limit 5", "type": "query", "params": [], "bindings": [28, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 47}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.916097, "duration": 0.01002, "duration_str": "10.02ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "instapwa", "explain": null, "start_percent": 11.327, "width_percent": 23.795}, {"sql": "select * from `user_profiles` where `user_profiles`.`user_id` in (6, 10, 17, 19, 21)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.92781, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "instapwa", "explain": null, "start_percent": 35.122, "width_percent": 1.187}, {"sql": "select * from `live_streams` where `has_recording` = 1 and `recording_path` is not null and `visibility` = 'public' and `ended_at` > '2025-07-21 21:23:22' order by `ended_at` desc limit 8", "type": "query", "params": [], "bindings": [1, "public", "2025-07-21 21:23:22"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 57}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.9298391, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:57", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=57", "ajax": false, "filename": "HomeController.php", "line": "57"}, "connection": "instapwa", "explain": null, "start_percent": 36.31, "width_percent": 1.116}, {"sql": "select * from `users` where `users`.`id` in (27)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 57}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.931864, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:57", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=57", "ajax": false, "filename": "HomeController.php", "line": "57"}, "connection": "instapwa", "explain": null, "start_percent": 37.426, "width_percent": 0.902}, {"sql": "select * from `user_profiles` where `user_profiles`.`user_id` = 28 and `user_profiles`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 508}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.9405591, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "home:508", "source": {"index": 21, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 508}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=508", "ajax": false, "filename": "home.blade.php", "line": "508"}, "connection": "instapwa", "explain": null, "start_percent": 38.328, "width_percent": 1.187}, {"sql": "select * from `user_profiles` where `user_profiles`.`user_id` = 27 and `user_profiles`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 566}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.949182, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "home:566", "source": {"index": 21, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 566}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=566", "ajax": false, "filename": "home.blade.php", "line": "566"}, "connection": "instapwa", "explain": null, "start_percent": 39.516, "width_percent": 1.306}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 70 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [70, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.952333, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 40.822, "width_percent": 0.807}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 70 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [70, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.954447, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 41.629, "width_percent": 1.306}, {"sql": "select * from `comments` where `comments`.`post_id` = 70 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [70], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.9568002, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 42.935, "width_percent": 1.069}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 69 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [69, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.959122, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 44.004, "width_percent": 1.021}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 69 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [69, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.961214, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 45.025, "width_percent": 1.282}, {"sql": "select * from `comments` where `comments`.`post_id` = 69 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [69], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.963253, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 46.307, "width_percent": 0.736}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 68 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [68, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.9654672, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 47.043, "width_percent": 1.045}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 68 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [68, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.967332, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 48.088, "width_percent": 0.902}, {"sql": "select * from `comments` where `comments`.`post_id` = 68 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [68], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.969189, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 48.991, "width_percent": 1.33}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 67 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [67, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.972204, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 50.321, "width_percent": 1.164}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 67 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [67, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.974247, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 51.484, "width_percent": 0.95}, {"sql": "select * from `comments` where `comments`.`post_id` = 67 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [67], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.976224, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 52.434, "width_percent": 0.997}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 66 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [66, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.9785802, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 53.431, "width_percent": 1.045}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 66 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [66, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.980418, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 54.476, "width_percent": 1.021}, {"sql": "select * from `comments` where `comments`.`post_id` = 66 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [66], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.982469, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 55.498, "width_percent": 1.045}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 65 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [65, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.984793, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 56.542, "width_percent": 0.784}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 65 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [65, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.986635, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 57.326, "width_percent": 0.736}, {"sql": "select * from `comments` where `comments`.`post_id` = 65 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.988422, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 58.062, "width_percent": 0.807}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 64 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [64, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.990686, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 58.87, "width_percent": 0.76}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 64 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [64, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.99241, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 59.63, "width_percent": 0.546}, {"sql": "select * from `comments` where `comments`.`post_id` = 64 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [64], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.993994, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 60.176, "width_percent": 0.712}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 63 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [63, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.996234, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 60.888, "width_percent": 1.021}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 63 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [63, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.99811, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 61.909, "width_percent": 0.926}, {"sql": "select * from `comments` where `comments`.`post_id` = 63 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [63], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.999842, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 62.835, "width_percent": 0.974}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 62 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [62, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.002664, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 63.809, "width_percent": 1.14}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 62 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [62, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.004697, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 64.949, "width_percent": 1.306}, {"sql": "select * from `comments` where `comments`.`post_id` = 62 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [62], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.006766, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 66.255, "width_percent": 0.807}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 61 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [61, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.008945, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 67.062, "width_percent": 1.092}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 61 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [61, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.010965, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 68.155, "width_percent": 1.045}, {"sql": "select * from `comments` where `comments`.`post_id` = 61 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [61], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.0128412, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 69.2, "width_percent": 1.045}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 60 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [60, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.015085, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 70.245, "width_percent": 0.997}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 60 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [60, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.017323, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 71.242, "width_percent": 1.187}, {"sql": "select * from `comments` where `comments`.`post_id` = 60 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [60], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.019378, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 72.429, "width_percent": 0.974}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 59 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [59, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.021832, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 73.403, "width_percent": 1.259}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 59 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [59, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.0239441, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 74.662, "width_percent": 0.76}, {"sql": "select * from `comments` where `comments`.`post_id` = 59 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [59], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.0256631, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 75.422, "width_percent": 0.689}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 58 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [58, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.027738, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 76.11, "width_percent": 0.76}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 58 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [58, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.029451, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 76.87, "width_percent": 1.021}, {"sql": "select * from `comments` where `comments`.`post_id` = 58 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [58], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.03143, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 77.891, "width_percent": 0.831}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 57 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [57, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.034206, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 78.722, "width_percent": 1.662}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 57 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [57, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.0364182, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 80.385, "width_percent": 0.665}, {"sql": "select * from `comments` where `comments`.`post_id` = 57 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [57], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.038738, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 81.05, "width_percent": 0.831}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 56 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [56, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.04105, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 81.881, "width_percent": 0.736}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 56 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [56, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.042805, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 82.617, "width_percent": 0.926}, {"sql": "select * from `comments` where `comments`.`post_id` = 56 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [56], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.0448072, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 83.543, "width_percent": 1.069}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 55 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [55, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.04718, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 84.612, "width_percent": 0.76}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 55 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [55, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.04904, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 85.372, "width_percent": 0.855}, {"sql": "select * from `comments` where `comments`.`post_id` = 55 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [55], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.0508509, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 86.227, "width_percent": 0.926}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 54 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [54, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.0531752, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 87.153, "width_percent": 1.069}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 54 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [54, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.0551622, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 88.221, "width_percent": 0.641}, {"sql": "select * from `comments` where `comments`.`post_id` = 54 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [54], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.0568001, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 88.863, "width_percent": 0.712}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 28 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [28, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.058913, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 89.575, "width_percent": 0.902}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 28 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [28, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.060656, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 90.477, "width_percent": 0.712}, {"sql": "select * from `comments` where `comments`.`post_id` = 28 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.062251, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 91.19, "width_percent": 0.736}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 29 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [29, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.0646892, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 91.926, "width_percent": 1.472}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 29 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [29, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.0670521, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 93.398, "width_percent": 0.807}, {"sql": "select * from `comments` where `comments`.`post_id` = 29 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.0688572, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 94.206, "width_percent": 1.045}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 30 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [30, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.071061, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 95.251, "width_percent": 0.902}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 30 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [30, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.072995, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 96.153, "width_percent": 0.974}, {"sql": "select * from `comments` where `comments`.`post_id` = 30 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.07488, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 97.127, "width_percent": 0.879}, {"sql": "select count(*) as aggregate from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = 28 and `notifications`.`notifiable_id` is not null and `read_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\User", 28], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "layouts.app", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/layouts/app.blade.php", "line": 382}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.0798998, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "layouts.app:382", "source": {"index": 19, "namespace": "view", "name": "layouts.app", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/layouts/app.blade.php", "line": 382}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=382", "ajax": false, "filename": "app.blade.php", "line": "382"}, "connection": "instapwa", "explain": null, "start_percent": 98.005, "width_percent": 1.021}, {"sql": "select * from `love_wallets` where `love_wallets`.`user_id` = 28 and `love_wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "layouts.app", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/layouts/app.blade.php", "line": 413}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.0819368, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "layouts.app:413", "source": {"index": 21, "namespace": "view", "name": "layouts.app", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/layouts/app.blade.php", "line": 413}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=413", "ajax": false, "filename": "app.blade.php", "line": "413"}, "connection": "instapwa", "explain": null, "start_percent": 99.026, "width_percent": 0.974}]}, "models": {"data": {"App\\Models\\Post": {"value": 20, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=1", "ajax": false, "filename": "Post.php", "line": "?"}}, "App\\Models\\User": {"value": 14, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\UserProfile": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FUserProfile.php&line=1", "ajax": false, "filename": "UserProfile.php", "line": "?"}}, "App\\Models\\LiveStream": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FLiveStream.php&line=1", "ajax": false, "filename": "LiveStream.php", "line": "?"}}}, "count": 43, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/home", "action_name": "home", "controller_action": "App\\Http\\Controllers\\HomeController@index", "uri": "GET home", "controller": "App\\Http\\Controllers\\HomeController@index<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=13\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=13\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/HomeController.php:13-60</a>", "middleware": "web, auth", "duration": "355ms", "peak_memory": "28MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-854670190 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-854670190\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1048488987 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1048488987\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2134900010 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Android&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"131 characters\">Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Mobile Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://localhost:8000/notifications</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImErb2lqelhGM2dsb2JKZEZ4WnZCUkE9PSIsInZhbHVlIjoicXVTNVpZdEZCQktTaFZYME92eXo4Qm9tSldQd2xYRU1TYjNXQ3FZS21IdWREd1JCWmlkR3RKV0JPYmMrYmNpaFpubVpjODlYcFZZbER2U1VQK1lpbEFnVkVxUmhtelJGVnJRVlZ2TytNVkl3OVU1d0h2TGNoRjRENG5pUFRpWloiLCJtYWMiOiI3MTVhYzk5ZTYyM2Q5NGU0N2I3ODE3MTcyNmUwOTBhNGYxY2MwZjk0YWI3MzI5ZmI4ZDQxNWU3NjY3NTk1MmNmIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IklHbUdJcHNzMHc1NUJIVTdFMjhzWWc9PSIsInZhbHVlIjoiZm9sN3RrZlRWWjFDbytaSFdMN2JsVkRVQU9RalpwS3RpWFJWcGpFVUR3RTRxRW5wbU5CblRYaFAybjhJNGcybGFmcTIyQ2w0NUtFVXh3U1FKM283MEJ2ZXhrKzBCQm9KS1h1WEYzRFZFeHFGWS9Lc0RMOGV5amhvQTFjckN2dE0iLCJtYWMiOiIxYzcwZDhlNjg1MTUxYWI4ODcxNzdkNWQ2MjA3MjI5MjZmODg1M2QwMDMzNmYxOGZiYjBjZGUwYTQxZWE5OTZhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2134900010\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2088957168 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HKkQb009ur9juY1euzSJdZerzJQUHXdiHWRWdESt</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ysQ2OZdBk91GzyxsvUNjStsqoUKCd3cWniDGhfUE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2088957168\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1976295222 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 22 Jul 2025 21:23:22 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1976295222\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-867069665 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HKkQb009ur9juY1euzSJdZerzJQUHXdiHWRWdESt</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://localhost:8000/notifications</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>28</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-867069665\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/home", "action_name": "home", "controller_action": "App\\Http\\Controllers\\HomeController@index"}, "badge": null}}