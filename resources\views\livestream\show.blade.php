@extends('layouts.app')

@section('content')
<div class="livestream-container">
    <!-- Header with user info and controls -->
    <div class="stream-header">
        <div class="user-info">
            <img src="{{ $liveStream->user->profile_photo ?? asset('images/default-avatar.png') }}" class="user-avatar">
            <span class="username">{{ '@' . $liveStream->user->username }}</span>
        </div>
        
        <div class="stream-stats">
            <div class="love-count">
                <i class="fas fa-heart"></i> <span id="loveCount">0</span>
            </div>
            <div class="viewer-count">
                <i class="fas fa-eye"></i> <span id="viewerCount">0</span>
            </div>
            <div class="live-badge">LIVE</div>
            
            @if($liveStream->user_id == auth()->id())
                <button id="endStreamBtn" class="end-stream-btn">End</button>
            @endif
        </div>
    </div>

    <!-- Video Stream -->
    <div class="video-container">
        <video id="streamVideo" autoplay playsinline></video>
    </div>

    <!-- Comment Section -->
    <div class="comment-section">
        <div class="comments-container" id="commentsContainer">
            <!-- Comments will be loaded here -->
        </div>
        
        <div class="comment-input">
            <input type="text" id="commentInput" placeholder="Write a comment...">
            <button id="sendCommentBtn">
                <i class="fas fa-paper-plane"></i>
            </button>
            <button id="toggleReactionsBtn">
                <i class="fas fa-smile"></i>
            </button>
        </div>
    </div>
</div>

<style>
.livestream-container {
    position: relative;
    height: 100vh;
    background: #000;
    color: white;
    display: flex;
    flex-direction: column;
}

.stream-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: rgba(0,0,0,0.5);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.stream-stats {
    display: flex;
    align-items: center;
    gap: 15px;
}

.live-badge {
    background: #FF1493;
    color: white;
    padding: 3px 8px;
    border-radius: 4px;
    font-weight: bold;
    font-size: 12px;
}

.end-stream-btn {
    background: rgba(255,255,255,0.2);
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
}

.video-container {
    flex: 1;
    width: 100%;
    height: calc(100vh - 150px);
    overflow: hidden;
}

#streamVideo {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.comment-section {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,0.7);
    padding: 10px;
}

.comments-container {
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 10px;
}

.comment-input {
    display: flex;
    gap: 10px;
}

#commentInput {
    flex: 1;
    background: rgba(255,255,255,0.2);
    border: none;
    border-radius: 20px;
    padding: 10px 15px;
    color: white;
}

#commentInput::placeholder {
    color: rgba(255,255,255,0.7);
}

#sendCommentBtn, #toggleReactionsBtn {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
}
</style>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const streamId = {{ $liveStream->id }};
    const isStreamer = {{ $liveStream->user_id == auth()->id() ? 'true' : 'false' }};
    let stream;

    // Initialize camera if user is the streamer
    async function initializeStream() {
        if (isStreamer) {
            try {
                stream = await navigator.mediaDevices.getUserMedia({
                    video: true,
                    audio: true
                });
                document.getElementById('streamVideo').srcObject = stream;
            } catch (err) {
                console.error('Error accessing camera:', err);
                alert('Unable to access camera. Please check permissions.');
            }
        } else {
            // For viewers, we would connect to the streamer's feed
            // This would typically involve WebRTC or a streaming service
            console.log('Viewer mode - would connect to stream');
            
            // For demo purposes, we'll just show a placeholder or the camera
            try {
                stream = await navigator.mediaDevices.getUserMedia({
                    video: true,
                    audio: false
                });
                document.getElementById('streamVideo').srcObject = stream;
            } catch (err) {
                console.error('Error accessing camera for demo:', err);
            }
        }
    }

    // End stream handler
    if (isStreamer) {
        document.getElementById('endStreamBtn').addEventListener('click', async () => {
            if (confirm('Are you sure you want to end this live stream?')) {
                try {
                    const response = await fetch(`/live/${streamId}/end`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                        }
                    });
                    
                    const data = await response.json();
                    if (data.success) {
                        // Stop all tracks
                        if (stream) {
                            stream.getTracks().forEach(track => track.stop());
                        }
                        
                        // Redirect to the ended page
                        window.location.href = `/live/${streamId}/ended`;
                    }
                } catch (err) {
                    console.error('Error ending stream:', err);
                    alert('Failed to end stream. Please try again.');
                }
            }
        });
    }

    // Comment input handler
    document.getElementById('sendCommentBtn').addEventListener('click', () => {
        const comment = document.getElementById('commentInput').value.trim();
        if (comment) {
            // Add comment to UI
            addComment({
                username: '{{ auth()->user()->username }}',
                text: comment
            });
            
            // Clear input
            document.getElementById('commentInput').value = '';
            
            // In a real app, you would send this to the server
            // sendCommentToServer(comment);
        }
    });

    // Helper to add a comment to the UI
    function addComment(comment) {
        const commentsContainer = document.getElementById('commentsContainer');
        const commentElement = document.createElement('div');
        commentElement.classList.add('comment');
        commentElement.innerHTML = `
            <strong>${comment.username}:</strong> ${comment.text}
        `;
        commentsContainer.appendChild(commentElement);
        commentsContainer.scrollTop = commentsContainer.scrollHeight;
    }

    // Initialize the stream
    initializeStream();
});
</script>
@endpush
@endsection
