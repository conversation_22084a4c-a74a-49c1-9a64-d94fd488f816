<?php

namespace App\Http\Controllers;

use App\Models\Love;
use App\Models\Post;
use App\Models\LoveWallet;
use App\Models\Earning;
use App\Models\Notification;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class LoveController extends Controller
{
    // Love value in dollars
    const LOVE_VALUE = 0.01;

    /**
     * Toggle love status for a post
     */
    public function toggle($postId)
    {
        $post = Post::findOrFail($postId);
        $user = Auth::user();
        
        // Check if user already loved this post
        $existingLove = Love::where('user_id', $user->id)
            ->where('post_id', $postId)
            ->first();
            
        DB::beginTransaction();
        
        try {
            if ($existingLove) {
                // User already loved this post, so unlike it
                $existingLove->delete();
                
                return response()->json([
                    'success' => true,
                    'loved' => false,
                    'loveCount' => $post->loves()->count()
                ]);
            } else {
                // User hasn't loved this post yet
                
                // Check if user has enough loves in wallet
                if (!$user->hasEnoughLoves()) {
                    return response()->json([
                        'success' => false,
                        'needsLoves' => true,
                        'message' => 'You need more loves to continue'
                    ]);
                }
                
                // Deduct love from user's wallet
                $wallet = $user->loveWallet;
                $wallet->deductLoves();
                
                // Create love record
                $love = Love::create([
                    'user_id' => $user->id,
                    'post_id' => $postId,
                    'is_paid' => true,
                    'amount' => 0.01 // $0.01 per love
                ]);
                
                // Add to creator's balance
                $creator = $post->user;
                $creator->increment('creator_balance', 0.01);
                
                // Create earning record
                Earning::create([
                    'creator_id' => $creator->id,
                    'amount' => 0.01,
                    'source' => 'loves',
                    'source_id' => $post->id,
                    'earned_at' => now(),
                    'status' => 'pending'
                ]);
                
                // Create notification for creator
                Notification::create([
                    'user_id' => $creator->id,
                    'type' => 'love',
                    'data' => json_encode([
                        'username' => $user->username,
                        'post_id' => $post->id,
                        'amount' => 0.01
                    ]),
                    'notifiable_type' => 'App\Models\Post',
                    'notifiable_id' => $post->id
                ]);
                
                DB::commit();
                
                return response()->json([
                    'success' => true,
                    'loved' => true,
                    'loveCount' => $post->loves()->count()
                ]);
            }
        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to process love: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show the buy loves page
     */
    public function showBuyLoves()
    {
        $lovePacks = [
            ['amount' => 100, 'price' => 80.00],
            ['amount' => 500, 'price' => 420.00],
            ['amount' => 1000, 'price' => 850.00],
            ['amount' => 2500, 'price' => 2100.00],
            ['amount' => 10000, 'price' => 7800.00],
            ['amount' => 25000, 'price' => 19000.00]
        ];
        
        return view('loves.buy', compact('lovePacks'));
    }

    /**
     * Process love purchase (simplified version without payment for testing)
     */
    public function buyLoves(Request $request)
    {
        $request->validate([
            'pack_id' => 'required|integer|min:0|max:5'
        ]);
        
        $lovePacks = [
            ['amount' => 100, 'price' => 80.00],
            ['amount' => 500, 'price' => 420.00],
            ['amount' => 1000, 'price' => 850.00],
            ['amount' => 2500, 'price' => 2100.00],
            ['amount' => 10000, 'price' => 7800.00],
            ['amount' => 25000, 'price' => 19000.00]
        ];
        
        $selectedPack = $lovePacks[$request->pack_id];
        
        $user = Auth::user();
        $wallet = $user->loveWallet ?? LoveWallet::create(['user_id' => $user->id]);
        $wallet->addLoves($selectedPack['amount']);
        
        return redirect()->route('loves.buy')
            ->with('success', 'Test mode: ' . number_format($selectedPack['amount']) . ' loves have been added to your wallet.');
    }
    
    /**
     * Create a payment intent (simplified mock version)
     */
    public function createPaymentIntent(Request $request)
    {
        $request->validate([
            'amount' => 'required|numeric|min:1'
        ]);
        
        // This is a mock implementation since Stripe is not installed
        return response()->json([
            'clientSecret' => 'mock_client_secret_' . time(),
            'message' => 'This is a mock implementation. Install Stripe PHP package for real payments.'
        ]);
    }
    
    /**
     * Handle successful payment (simplified mock version)
     */
    public function handlePaymentSuccess(Request $request)
    {
        $request->validate([
            'pack_id' => 'required|integer|min:0|max:5'
        ]);
        
        $lovePacks = [
            ['amount' => 100, 'price' => 80.00],
            ['amount' => 500, 'price' => 420.00],
            ['amount' => 1000, 'price' => 850.00],
            ['amount' => 2500, 'price' => 2100.00],
            ['amount' => 10000, 'price' => 7800.00],
            ['amount' => 25000, 'price' => 19000.00]
        ];
        
        $selectedPack = $lovePacks[$request->pack_id];
        $loveAmount = $selectedPack['amount'];
        
        return view('loves.payment-success', compact('loveAmount'));
    }
} 