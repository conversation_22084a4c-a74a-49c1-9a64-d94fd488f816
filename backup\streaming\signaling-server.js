import express from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';

const app = express();
const http = createServer(app);
const io = new Server(http, {
    cors: {
        origin: "http://localhost:8000",
        methods: ["GET", "POST"]
    }
});

const streams = new Map(); // Store active streams and their broadcasters

// Debug middleware for all socket events
const debugSocket = (socket) => {
    const originalEmit = socket.emit.bind(socket);
    socket.emit = (...args) => {
        console.log('Socket Emitting:', args[0], 'Data:', args.slice(1));
        originalEmit(...args);
    };
};

io.on('connection', (socket) => {
    debugSocket(socket);
    console.log('A user connected:', socket.id);

    // Join a stream room
    socket.on('join-stream', (streamId) => {
        try {
            console.log(`Socket ${socket.id} joining stream ${streamId}...`);
            const streamRoom = `stream-${streamId}`;
            socket.join(streamRoom);
            
            // If this is the broadcaster reconnecting, restore their role
            if (streams.has(streamId) && streams.get(streamId).broadcasterId === socket.id) {
                console.log(`Broadcaster ${socket.id} reconnected to stream ${streamId}`);
            }
            
            // Update viewer count (excluding broadcaster)
            const viewers = io.sockets.adapter.rooms.get(streamRoom)?.size || 0;
            const broadcasterCount = streams.has(streamId) ? 1 : 0;
            const viewerCount = Math.max(0, viewers - broadcasterCount);
            
            console.log(`Current viewers for stream ${streamId}: ${viewerCount}`);
            io.to(streamRoom).emit('viewer-count', { count: viewerCount });
        } catch (error) {
            console.error('Error in join-stream:', error);
            socket.emit('error', { message: 'Failed to join stream' });
        }
    });

    // Handle WebRTC signaling
    socket.on('signal', ({ streamId, signal }) => {
        try {
            const streamRoom = `stream-${streamId}`;
            console.log(`Received signal for stream ${streamId}:`, signal.type || 'ICE candidate');
            
            // For offers, store the broadcaster's socket ID
            if (signal.type === 'offer') {
                streams.set(streamId, { 
                    broadcasterId: socket.id,
                    lastActive: Date.now()
                });
                console.log(`Registered broadcaster ${socket.id} for stream ${streamId}`);
            }

            // Forward the signal to everyone in the room except the sender
            socket.to(streamRoom).emit('signal', {
                signal,
                senderId: socket.id
            });
            
            console.log(`Signal forwarded to stream ${streamId} from ${socket.id}`);
        } catch (error) {
            console.error('Error in signal handling:', error);
            socket.emit('error', { message: 'Failed to process signal' });
        }
    });

    // Handle stream start
    socket.on('stream-start', (streamId) => {
        try {
            console.log(`Starting stream ${streamId} from broadcaster ${socket.id}...`);
            streams.set(streamId, { 
                broadcasterId: socket.id,
                lastActive: Date.now()
            });
            
            const streamRoom = `stream-${streamId}`;
            socket.join(streamRoom);
            
            // Notify room that stream has started
            io.to(streamRoom).emit('stream-started', { 
                streamId,
                broadcasterId: socket.id
            });
            
            console.log(`Stream ${streamId} started by ${socket.id}`);
        } catch (error) {
            console.error('Error in stream-start:', error);
            socket.emit('error', { message: 'Failed to start stream' });
        }
    });

    // Handle stream stop
    socket.on('stream-stop', (streamId) => {
        try {
            console.log(`Stopping stream ${streamId}...`);
            streams.delete(streamId);
            io.to(`stream-${streamId}`).emit('stream-ended');
            console.log(`Stream ${streamId} stopped`);
        } catch (error) {
            console.error('Error in stream-stop:', error);
            socket.emit('error', { message: 'Failed to stop stream' });
        }
    });

    // Handle errors
    socket.on('error', (error) => {
        console.error('Socket error:', error);
    });

    // Handle disconnection
    socket.on('disconnect', () => {
        try {
            console.log('User disconnected:', socket.id);
            
            // Clean up streams if broadcaster disconnects
            for (const [streamId, stream] of streams.entries()) {
                if (stream.broadcasterId === socket.id) {
                    console.log(`Cleaning up stream ${streamId} after broadcaster ${socket.id} disconnect`);
                    streams.delete(streamId);
                    io.to(`stream-${streamId}`).emit('stream-ended');
                    console.log(`Stream ${streamId} ended due to broadcaster disconnect`);
                }
            }
        } catch (error) {
            console.error('Error in disconnect handling:', error);
        }
    });
});

// Periodic cleanup of inactive streams
setInterval(() => {
    const now = Date.now();
    for (const [streamId, stream] of streams.entries()) {
        if (now - stream.lastActive > 30000) { // 30 seconds
            console.log(`Cleaning up inactive stream ${streamId}`);
            streams.delete(streamId);
            io.to(`stream-${streamId}`).emit('stream-ended');
        }
    }
}, 10000); // Check every 10 seconds

const PORT = process.env.PORT || 3000;
http.listen(PORT, () => {
    console.log(`Signaling server running on port ${PORT}`);
}); 