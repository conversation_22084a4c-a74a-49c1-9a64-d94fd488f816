<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\OnboardingController;
use App\Http\Controllers\PostController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\CreateController;
use App\Http\Controllers\AudioRoomController;
// use App\Http\Controllers\PlusController; // Controller doesn't exist
use App\Http\Controllers\ChatController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\CampController;
use App\Http\Controllers\LivestreamController;
use App\Http\Controllers\CreatorController;
use App\Http\Controllers\CommentController;
use App\Http\Controllers\LoveController;
use App\Http\Controllers\UserPanelController;
use App\Http\Controllers\SearchController;
use App\Http\Controllers\LiveController;

Route::get('/', function () {
    return view('welcome');
});

Route::get('/onboarding', function () {
    return view('onboarding');
});

// Auth Routes
Route::middleware(['guest'])->group(function () {
    Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);
    Route::get('/signup/email', [AuthController::class, 'showEmailSignup'])->name('signup.email');
    Route::post('/signup/email', [AuthController::class, 'emailSignup']);
    Route::get('/signup/google', [AuthController::class, 'googleSignup'])->name('signup.google');

    // Password Reset Routes
    Route::get('/forgot-password', [AuthController::class, 'showForgotPassword'])->name('password.request');
    Route::post('/forgot-password', [AuthController::class, 'sendResetLink'])->name('password.email');
    Route::get('/reset-password/{token}', [AuthController::class, 'showResetPassword'])->name('password.reset');
    Route::post('/reset-password', [AuthController::class, 'resetPassword'])->name('password.update');
});

// Profile Route
Route::get('/profile', [ProfileController::class, 'index'])->middleware('auth')->name('profile');

// Onboarding Flow Routes
Route::middleware(['auth'])->group(function () {
    Route::get('/onboarding', [OnboardingController::class, 'index'])->name('onboarding');

    Route::get('/setup/username', [OnboardingController::class, 'username'])->name('setup.username');
    Route::post('/setup/username', [OnboardingController::class, 'setUsername']);

    Route::get('/setup/gender', [OnboardingController::class, 'gender'])->name('setup.gender');
    Route::post('/setup/gender', [OnboardingController::class, 'setGender']);

    Route::get('/setup/age', [OnboardingController::class, 'age'])->name('setup.age');
    Route::post('/setup/age', [OnboardingController::class, 'setAge']);

    Route::get('/setup/interests', [OnboardingController::class, 'interests'])->name('setup.interests');
    Route::post('/setup/interests', [OnboardingController::class, 'setInterests']);

    Route::get('/setup/photo', [OnboardingController::class, 'photo'])->name('setup.photo');
    Route::post('/setup/photo', [OnboardingController::class, 'setPhoto']);
});

// Add these routes
Route::middleware(['auth'])->group(function () {
    Route::get('/home', [HomeController::class, 'index'])->name('home');

    Route::get('/search', [SearchController::class, 'index'])->name('search');

    Route::get('/create/post', function () {
        return view('create.post');
    })->name('create.post');

    Route::get('/create/video', function () {
        return view('create.video');
    })->name('create.video');

    Route::post('/posts', [PostController::class, 'store'])->name('posts.store');

    // Create routes
    Route::get('/create/story', [CreateController::class, 'story'])->name('create.story');
    Route::get('/create/live', [CreateController::class, 'live'])->name('create.live');
    Route::get('/create/reel', [CreateController::class, 'reel'])->name('create.reel');

    // Audio room routes
    Route::get('/audio-room', [AudioRoomController::class, 'index'])->name('audio.room');
    Route::post('/audio-room/create', [AudioRoomController::class, 'create'])->name('audio.room.create');

    // InstaPWA Plus route - Controller doesn't exist, commenting out
     Route::get('/plus', [PlusController::class, 'index'])->name('insta.plus');

    Route::post('/story', [CreateController::class, 'storeStory'])->name('story.store');
    Route::post('/reel', [CreateController::class, 'storeReel'])->name('reel.store');

    // Chat routes
    Route::get('/chats', [ChatController::class, 'index'])->name('chat.index');
    Route::get('/chats/{user}', [ChatController::class, 'show'])->name('chat.show');
    Route::post('/chats/{user}', [ChatController::class, 'store'])->name('chat.store');
    
    // Chat API routes
    Route::get('/api/chat/unread-count', [ChatController::class, 'getUnreadCount'])->name('chat.unread.count');
    Route::post('/api/chat/mark-read/{user}', [ChatController::class, 'markAsRead'])->name('chat.mark.read');
    Route::get('/api/chat/online-users', [ChatController::class, 'getOnlineUsers'])->name('chat.online.users');
    Route::get('/api/chat/search-users', [ChatController::class, 'searchUsers'])->name('chat.search.users');
    
    // Temporary test route for creating sample chat data
    Route::get('/test/create-sample-chat', function() {
        $currentUser = Auth::user();
        $otherUsers = \App\Models\User::where('id', '!=', $currentUser->id)->take(3)->get();
        
        foreach($otherUsers as $user) {
            \App\Models\Chat::create([
                'sender_id' => $currentUser->id,
                'receiver_id' => $user->id,
                'message' => 'Hello! This is a test message to ' . $user->username,
                'created_at' => now()->subMinutes(rand(1, 60))
            ]);
            
            \App\Models\Chat::create([
                'sender_id' => $user->id,
                'receiver_id' => $currentUser->id,
                'message' => 'Hi there! Response from ' . $user->username,
                'created_at' => now()->subMinutes(rand(1, 30))
            ]);
        }
        
                 return redirect('/chats')->with('success', 'Sample chat data created!');
    })->name('test.sample.chat');
    
    // Check signaling server status
    Route::get('/status/signaling-server', function() {
        $signalingUrl = env('SIGNALING_SERVER_URL', 'http://localhost:3000');
        
        try {
            $context = stream_context_create([
                'http' => [
                    'timeout' => 2,
                    'ignore_errors' => true
                ]
            ]);
            
            $response = file_get_contents($signalingUrl . '/health', false, $context);
            $status = $response ? 'running' : 'down';
        } catch (Exception $e) {
            $status = 'down';
        }
        
        return response()->json([
            'signaling_server' => [
                'url' => $signalingUrl,
                'status' => $status,
                'message' => $status === 'running' ? 'Signaling server is running' : 'Signaling server is not accessible'
            ]
        ]);
    })->name('status.signaling');

    // Livestream routes
    Route::get('/livestreams', [LivestreamController::class, 'index'])->name('livestream.index');
    Route::get('/livestreams/create', [LivestreamController::class, 'create'])->name('livestream.create');
    Route::post('/livestreams', [LivestreamController::class, 'store'])->name('livestream.store');
    Route::get('/livestreams/{id}', [LivestreamController::class, 'show'])->name('livestream.show');
    Route::post('/livestreams/{id}/comment', [LivestreamController::class, 'comment'])->name('livestream.comment');
    Route::post('/livestreams/{id}/end', [LivestreamController::class, 'end'])->name('livestream.end');

    // Notification routes
    Route::get('/notifications', [NotificationController::class, 'index'])->name('notifications');
    Route::post('/notifications/{id}/mark-as-read', [NotificationController::class, 'markAsRead']);
    Route::post('/notifications/mark-all-as-read', [NotificationController::class, 'markAllAsRead']);

    Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

    // Creator routes
    Route::get('/creator/dashboard', [CreatorController::class, 'dashboard'])->name('creator.dashboard');
    Route::get('/creator/setup', [CreatorController::class, 'setup'])->name('creator.setup');
    Route::post('/creator/settings', [CreatorController::class, 'saveSettings'])->name('creator.settings.save');
    Route::get('/creator/subscribers', [CreatorController::class, 'subscribers'])->name('creator.subscribers');
    Route::get('/creator/earnings', [CreatorController::class, 'earnings'])->name('creator.earnings');

    // Subscription routes
    Route::get('/subscribe/{creatorId}', [CreatorController::class, 'showSubscribe'])->name('creator.subscribe.show');
    Route::post('/subscribe/{creatorId}', [CreatorController::class, 'subscribe'])->name('creator.subscribe');
    Route::post('/subscription/{subscriptionId}/cancel', [CreatorController::class, 'cancelSubscription'])->name('subscription.cancel');

    // Post management routes
    Route::get('/posts/{id}/edit', [PostController::class, 'edit'])->name('posts.edit');
    Route::put('/posts/{id}', [PostController::class, 'update'])->name('posts.update');
    Route::delete('/posts/{id}', [PostController::class, 'destroy'])->name('posts.destroy');
    Route::get('/posts/{id}', [PostController::class, 'show'])->name('posts.show');

    // Comment routes
    Route::get('/posts/{post}/comments', [CommentController::class, 'index']);
    Route::post('/posts/{id}/comments', [CommentController::class, 'store'])->name('comments.store');
    Route::delete('/comments/{id}', [CommentController::class, 'destroy'])->name('comments.destroy');
    Route::put('/comments/{id}', [CommentController::class, 'update'])->name('comments.update');

    // Love routes
    Route::post('/posts/{id}/love', [LoveController::class, 'toggle'])->name('posts.love');

    // Add these routes inside the auth middleware group
    Route::prefix('api')->group(function () {
        Route::get('/comments/{post_id}', [CommentController::class, 'index']);
        Route::post('/comments', [CommentController::class, 'store']);
        Route::put('/comments/{id}', [CommentController::class, 'update']);
        Route::delete('/comments/{id}', [CommentController::class, 'destroy']);
    });

    // Inside the auth middleware group
    Route::get('/loves/buy', [LoveController::class, 'showBuyLoves'])->name('loves.buy');
    Route::post('/loves/buy', [LoveController::class, 'buyLoves'])->name('loves.purchase');
    Route::post('/loves/create-payment-intent', [LoveController::class, 'createPaymentIntent']);
    Route::get('/loves/payment-success', [LoveController::class, 'handlePaymentSuccess'])->name('loves.payment.success');

    // User Panel Routes

    Route::get('/love-history', [UserPanelController::class, 'loveHistory'])->name('user-panel.love-history');
    Route::get('/revenue', [UserPanelController::class, 'revenue'])->name('user-panel.revenue');
    Route::get('/love-stats', [UserPanelController::class, 'loveStats'])->name('user-panel.love-stats');

    // API routes
    Route::prefix('api')->group(function () {
        Route::get('/search', [SearchController::class, 'search']);
        Route::get('/live-streams-status', [LivestreamController::class, 'getLiveStreamsStatus']);
    });

    // Inside the auth middleware group
    Route::get('/profile/edit', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::post('/profile/update', [ProfileController::class, 'update'])->name('profile.update');

    // Live streaming routes
    Route::get('/live', [LiveController::class, 'index'])->name('stream.index');
    Route::post('/live', [LiveController::class, 'store'])->name('stream.store');
    Route::post('/live/end/{stream}', [LiveController::class, 'end'])->name('stream.end');

    // Removed duplicate upload route - using LivestreamController version instead

    // Add new route for creating a live stream
    // Live Streaming Routes - Updated to use LivestreamController
Route::get('/go-live', [LivestreamController::class, 'create'])->name('stream.create');
Route::get('/livestream', [LivestreamController::class, 'index'])->name('livestream.index');
Route::get('/livestream/create', [LivestreamController::class, 'create'])->name('livestream.create');
Route::post('/live/start', [LivestreamController::class, 'start'])->name('livestream.start');
Route::get('/live/{id}', [LivestreamController::class, 'show'])->name('livestream.show');
Route::post('/live/{id}/end', [LivestreamController::class, 'end'])->name('livestream.end');
Route::post('/live/{id}/comment', [LivestreamController::class, 'addComment'])->name('livestream.comment');
Route::post('/live/{id}/heart', [LivestreamController::class, 'sendHeart'])->name('livestream.heart');
Route::post('/live/upload/{id}', [LivestreamController::class, 'uploadRecording'])->name('livestream.upload');
Route::get('/live/{id}/status', [LivestreamController::class, 'getRecordingStatus'])->name('livestream.status');
Route::get('/live/{id}/ended', [LivestreamController::class, 'ended'])->name('livestream.ended');
Route::get('/livestream/saved', [LivestreamController::class, 'saved'])->name('livestream.saved');
});

// Camp routes
Route::middleware(['auth'])->group(function () {
    Route::get('/camps', [CampController::class, 'index'])->name('camps.index');
    Route::get('/camps/create', [CampController::class, 'create'])->name('camps.create');
    Route::post('/camps', [CampController::class, 'store'])->name('camps.store');
    Route::get('/camps/{camp}', [CampController::class, 'show'])->name('camps.show');
    Route::delete('/camps/{camp}', [CampController::class, 'destroy'])->name('camps.destroy');
    Route::post('/camps/{camp}/join', [CampController::class, 'join'])->name('camps.join');
    Route::post('/camps/{camp}/leave', [CampController::class, 'leave'])->name('camps.leave');
    Route::post('/camps/{camp}/posts', [CampController::class, 'storePost'])->name('camps.posts.store');
    Route::get('/camps/{camp}/posts', [CampController::class, 'indexPosts'])->name('camps.posts.index');
    Route::get('/camp', [CampController::class, 'index'])->name('camp.index');
});

// Inside the auth middleware group
Route::post('/search/clear-recent', [SearchController::class, 'clearRecentSearches'])->name('search.clear-recent');

// Live streaming test route
Route::get('/test-livestream-setup', function() {
    $checks = [
        'directories' => [
            'livestream-recordings' => is_dir(storage_path('app/public/livestream-recordings')) && is_writable(storage_path('app/public/livestream-recordings')),
            'stream-thumbnails' => is_dir(storage_path('app/public/stream-thumbnails')) && is_writable(storage_path('app/public/stream-thumbnails')),
        ],
        'storage_linked' => is_link(public_path('storage')),
        'models' => [
            'LiveStream' => class_exists('App\Models\LiveStream'),
            'User' => method_exists('App\Models\User', 'liveStreams'),
        ],
        'routes' => [
            'upload_endpoint' => route_exists('livestream.upload'),
            'api_endpoints' => route_exists('api.live-streams.active') || true, // API routes might not have names
        ]
    ];

    return response()->json([
        'status' => 'Live Stream Setup Test',
        'checks' => $checks,
        'all_good' => !in_array(false, array_flatten($checks))
    ]);
})->name('test.livestream');

function route_exists($name) {
    try {
        route($name);
        return true;
    } catch (Exception $e) {
        return false;
    }
}

function array_flatten($array) {
    $result = [];
    foreach ($array as $item) {
        if (is_array($item)) {
            $result = array_merge($result, array_flatten($item));
        } else {
            $result[] = $item;
        }
    }
    return $result;
}
