<html>
<head>
    <title>Live Streams</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        body {
            background-color: #1a1a1a;  
            color: #fff;
            font-family: 'Roboto', sans-serif;
        }

        .container {
            margin-top: 20px;   
        }

        .card {
            background-color: #2d2d2d;
            border: none;
            margin-bottom: 20px;
        }

        .card-header {
            background-color: #3d3d3d;
            border-bottom: 1px solid #4d4d4d;
        }

        .card-body {    
            padding: 1rem;
        }

        .card-footer {
            background-color: #3d3d3d;
            border-top: 1px solid #4d4d4d;
        }

        .stream-card {
            border-bottom: 1px solid #2d2d2d;
            transition: transform 0.2s;
        }

        .stream-card:hover {
            transform: translateY(-2px);
        }

        .stream-thumbnail {
            position: relative;
            aspect-ratio: 16/9;
            background: #000;
        }

        .stream-overlay {           
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(transparent 60%, rgba(0,0,0,0.8));
                padding: 1rem;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
        }

        .live-badge {
            position: absolute;
            top: 1rem;
            left: 1rem;
            background: #dc3545;
            padding: 0.25rem 0.75rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .stream-preview {
            object-fit: cover;
            height: 100%;   
        }

        .stream-info h5 {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .viewers {
            background: rgba(0,0,0,0.7);
            padding: 0.25rem 0.75rem;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .stream-preview {
            object-fit: cover;
            height: 100%;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header bg-dark text-white">
                    <h4 class="mb-0">Live Streams</h4>
                </div>
                <div class="card-body p-0">
                    @forelse($streams as $stream)
                        <div class="stream-card position-relative">
                            <a href="{{ route('stream.show', $stream) }}" class="text-decoration-none text-white">
                                <div class="stream-thumbnail">
                                    <img src="{{ $stream->user->avatar_url }}" 
                                         alt="{{ $stream->user->name }}" 
                                         class="w-100 stream-preview">
                                    <div class="stream-overlay">
                                        <div class="live-badge">LIVE</div>
                                        <div class="stream-info">
                                            <h5 class="mb-1">{{ $stream->title }}</h5>
                                            <div class="d-flex align-items-center">
                                                <img src="{{ $stream->user->avatar_url }}" 
                                                     alt="{{ $stream->user->name }}"
                                                     class="rounded-circle me-2" 
                                                     style="width: 30px; height: 30px;">
                                                <span>{{ $stream->user->name }}</span>
                                            </div>
                                            <div class="mt-2">
                                                <span class="viewers">
                                                    <i class="fas fa-eye"></i> 
                                                    {{ $stream->viewer_count }} viewers
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    @empty
                        <div class="text-center p-4">
                            <i class="fas fa-broadcast-tower fa-3x text-muted mb-3"></i>
                            <h5>No live streams available</h5>
                            <p class="text-muted">Check back later or start your own stream!</p>
                        </div>
                    @endforelse
                </div>
                @if($streams->hasPages())
                    <div class="card-footer">
                        {{ $streams->links() }}
                    </div>
                @endif
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0">Start Your Stream</h5>
                </div>
                <div class="card-body text-center">
                    <p class="text-muted mb-3">Go live and connect with your audience</p>
                    <a href="{{ route('stream.create') }}" class="btn btn-danger btn-lg">
                        <i class="fas fa-broadcast-tower me-2"></i>Go Live
                    </a>
                </div>
            </div>

            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0">Upcoming Streams</h5>
                </div>
                <div class="card-body">
                    <div class="upcoming-streams">
                        @foreach($scheduledStreams as $stream)
                            <div class="upcoming-stream mb-3">
                                <div class="d-flex align-items-center">
                                    <img src="{{ $stream->user->avatar_url }}" 
                                         alt="{{ $stream->user->name }}"
                                         class="rounded-circle me-2" 
                                         style="width: 40px; height: 40px;">
                                    <div>
                                        <h6 class="mb-0">{{ $stream->title }}</h6>
                                        <small class="text-muted">
                                            {{ $stream->user->name }} • 
                                            {{ $stream->start_time->diffForHumans() }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stream-card {
    border-bottom: 1px solid #2d2d2d;
    transition: transform 0.2s;
}

.stream-card:hover {
    transform: translateY(-2px);
}

.stream-thumbnail {
    position: relative;
    aspect-ratio: 16/9;
    background: #000;
}

.stream-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(transparent 60%, rgba(0,0,0,0.8));
    padding: 1rem;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
}

.live-badge {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: #dc3545;
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    font-weight: 600;
    font-size: 0.9rem;
}

.stream-info h5 {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.viewers {
    background: rgba(0,0,0,0.7);
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    font-size: 0.9rem;
}

.stream-preview {
    object-fit: cover;
    height: 100%;
}
</style>
</body>
</html> 