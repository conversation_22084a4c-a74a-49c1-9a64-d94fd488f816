<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to InstaPWA</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            background-color: #d31414;
            color: white;
            height: 100vh;
            overflow: hidden;
        }

        .onboarding-container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }

        .splash-screen {
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #d31414;
            position: absolute;
            width: 100%;
            top: 0;
            left: 0;
            z-index: 10;
            transition: opacity 0.5s ease;
        }

        .splash-screen.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .insta-logo {
            width: 200px;
            height: auto;
        }

        .slides-container {
            height: 100vh;
            width: 100vw;
            overflow: hidden;
            position: relative;
        }

        .slide {
            position: absolute;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            transition: transform 0.5s ease;
            padding: 20px;
            box-sizing: border-box;
            text-align: center;
        }

        .slide-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .slide-description {
            font-size: 16px;
            max-width: 300px;
            line-height: 1.5;
        }

        .slide-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }

        .dots-container {
            position: absolute;
            bottom: 100px;
            left: 0;
            right: 0;
            display: flex;
            justify-content: center;
            gap: 10px;
        }

        .dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.3);
        }

        .dot.active {
            background-color: white;
        }

        .next-button {
            position: absolute;
            bottom: 40px;
            right: 20px;
            background-color: #FFD700;
            color: black;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .next-button svg {
            width: 20px;
            height: 20px;
        }
    </style>
</head>
<body>
    <div class="onboarding-container">
        <!-- Splash Screen -->
        <div class="splash-screen" id="splashScreen">
            <img src="/images/logo.png" alt="InstaPWA Logo" class="insta-logo">
        </div>

        <!-- Slides Container -->
        <div class="slides-container">
            <!-- Slide 1: Connecting People -->
            <div class="slide" id="slide1" style="transform: translateX(0);">
                <img src="/images/logo.png" alt="InstaPWA Logo" class="insta-logo" style="margin-bottom: 40px;">
                <div class="slide-title">Connecting People</div>
                <div class="slide-description">Join a global community and stay connected with friends, family, and like-minded creators.</div>
            </div>

            <!-- Slide 2: Sharing Moments -->
            <div class="slide" id="slide2" style="transform: translateX(100%);">
                <img src="/images/logo.png" alt="InstaPWA Logo" class="insta-logo" style="margin-bottom: 40px;">
                <div class="slide-title">Sharing Moments</div>
                <div class="slide-description">Capture and share your favorite moments with the world.</div>
            </div>

            <!-- Slide 3: Privacy and Security -->
            <div class="slide" id="slide3" style="transform: translateX(200%);">
                <img src="/images/logo.png" alt="InstaPWA Logo" class="insta-logo" style="margin-bottom: 40px;">
                <div class="slide-title">Privacy and Security</div>
                <div class="slide-description">We're committed to safeguarding your personal data and ensuring that your experience is safe and secure.</div>
            </div>
        </div>

        <!-- Navigation Dots -->
        <div class="dots-container">
            <div class="dot active" data-slide="0"></div>
            <div class="dot" data-slide="1"></div>
            <div class="dot" data-slide="2"></div>
        </div>

        <!-- Next Button -->
        <button class="next-button" id="nextButton">
            Next
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"/>
            </svg>
        </button>
    </div>

    <script>
        // Show splash screen for 2 seconds then start onboarding
        setTimeout(() => {
            document.getElementById('splashScreen').classList.add('hidden');
        }, 2000);

        const slides = document.querySelectorAll('.slide');
        const dots = document.querySelectorAll('.dot');
        const nextButton = document.getElementById('nextButton');
        let currentSlide = 0;

        // Update the active dot
        function updateDots() {
            dots.forEach((dot, index) => {
                if (index === currentSlide) {
                    dot.classList.add('active');
                } else {
                    dot.classList.remove('active');
                }
            });
        }

        // Handle next button click
        nextButton.addEventListener('click', () => {
            if (currentSlide < slides.length - 1) {
                currentSlide++;
                updateSlides();
            } else {
                // Redirect to username setup when finished
                window.location.href = "{{ route('setup.username') }}";
            }
        });

        // Update slides position
        function updateSlides() {
            slides.forEach((slide, index) => {
                slide.style.transform = `translateX(${(index - currentSlide) * 100}%)`;
            });
            
            // Update dots
            updateDots();
            
            // Change button text on last slide
            if (currentSlide === slides.length - 1) {
                nextButton.textContent = "Get Started";
                nextButton.appendChild(document.createElement('span'));
                nextButton.lastElementChild.innerHTML = `<svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"/>
                </svg>`;
            }
        }

        // Allow dot navigation
        dots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                currentSlide = index;
                updateSlides();
            });
        });
    </script>
</body>
</html> 