<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Profile Photo - InstaPWA</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            background-color: #d31414;
            color: white;
            min-height: 100vh;
        }

        .container {
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }

        .logo {
            width: 150px;
            margin: 40px 0 20px;
        }

        .title {
            font-size: 28px;
            margin-bottom: 10px;
            text-align: center;
        }

        .subtitle {
            font-size: 16px;
            opacity: 0.8;
            max-width: 300px;
            margin: 0 auto 40px;
            text-align: center;
            line-height: 1.4;
        }

        .photo-container {
            width: 180px;
            height: 180px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 50%;
            margin-bottom: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            position: relative;
        }

        .photo-placeholder {
            font-size: 40px;
            color: rgba(255, 255, 255, 0.5);
        }

        .photo-preview {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: none;
        }
        
        .photo-actions {
            display: flex;
            gap: 20px;
            margin-bottom: 60px;
        }
        
        .photo-action {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            cursor: pointer;
        }
        
        .action-icon {
            width: 60px;
            height: 60px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .action-icon svg {
            width: 30px;
            height: 30px;
            fill: white;
        }
        
        .action-label {
            font-size: 14px;
        }

        .next-btn {
            width: 100%;
            padding: 15px;
            background: #FFD700;
            color: black;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin-top: auto;
            max-width: 360px;
        }

        .next-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .error-message {
            color: #FFD700;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .camera-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #000;
            display: none;
            flex-direction: column;
            z-index: 100;
        }
        
        .camera-preview {
            flex: 1;
            background: #000;
            position: relative;
        }
        
        #video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .camera-controls {
            height: 100px;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding: 0 20px;
        }
        
        .camera-control {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        
        .capture-btn {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: white;
            border: 4px solid rgba(255, 255, 255, 0.3);
        }
        
        #fileInput {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <img src="/images/logo.png" alt="InstaPWA Logo" class="logo">
        
        <div class="title">Add Profile Photo</div>
        <div class="subtitle">Choose a photo to help friends recognize you</div>

        @if(session('error'))
            <div class="error-message">{{ session('error') }}</div>
        @endif

        @if($errors->any())
            <div class="error-message">{{ $errors->first() }}</div>
        @endif

        <div class="photo-container">
            <div class="photo-placeholder">
                <svg viewBox="0 0 24 24" width="80" height="80" fill="rgba(255, 255, 255, 0.5)">
                    <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z" />
                </svg>
            </div>
            <img src="" alt="Preview" class="photo-preview" id="photoPreview">
        </div>
        
        <div class="photo-actions">
            <div class="photo-action" onclick="document.getElementById('fileInput').click()">
                <div class="action-icon">
                    <svg viewBox="0 0 24 24">
                        <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z" />
                    </svg>
                </div>
                <div class="action-label">Upload</div>
            </div>
            
            <div class="photo-action" onclick="openCamera()">
                <div class="action-icon">
                    <svg viewBox="0 0 24 24">
                        <path d="M4,4H7L9,2H15L17,4H20A2,2 0 0,1 22,6V18A2,2 0 0,1 20,20H4A2,2 0 0,1 2,18V6A2,2 0 0,1 4,4M12,7A5,5 0 0,0 7,12A5,5 0 0,0 12,17A5,5 0 0,0 17,12A5,5 0 0,0 12,7M12,9A3,3 0 0,1 15,12A3,3 0 0,1 12,15A3,3 0 0,1 9,12A3,3 0 0,1 12,9Z" />
                    </svg>
                </div>
                <div class="action-label">Camera</div>
            </div>
        </div>

        <form action="{{ route('setup.photo') }}" method="POST" enctype="multipart/form-data" id="photoForm">
            @csrf
            <input type="file" id="fileInput" name="photo" accept="image/*" onchange="handleFileSelect(this)">
            <button type="submit" class="next-btn" id="nextBtn" disabled>Finish</button>
        </form>
    </div>
    
    <div class="camera-container" id="cameraContainer">
        <div class="camera-preview">
            <video id="video" autoplay playsinline></video>
        </div>
        <div class="camera-controls">
            <div class="camera-control" onclick="closeCamera()">
                <svg viewBox="0 0 24 24" width="30" height="30" fill="white">
                    <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
                </svg>
            </div>
            <div class="camera-control capture-btn" onclick="capturePhoto()"></div>
            <div class="camera-control" onclick="switchCamera()">
                <svg viewBox="0 0 24 24" width="30" height="30" fill="white">
                    <path d="M7.11,8.53L5.7,7.11C4.8,8.27 4.24,9.61 4.07,11H6.09C6.23,10.13 6.58,9.33 7.11,8.53M13,4.07V6.09C16.39,6.57 19,9.47 19,13C19,16.53 16.39,19.43 13,19.91V21.93C17.45,21.43 21,17.6 21,13C21,8.4 17.45,4.57 13,4.07M11,4.07C7.82,4.44 5.27,6.39 4.42,9H6.55C7.27,7.38 8.93,6.23 11,6.09V4.07M16.89,8.53C17.42,9.33 17.77,10.13 17.91,11H19.93C19.76,9.61 19.2,8.27 18.3,7.11L16.89,8.53M6.09,15C6.23,15.87 6.58,16.67 7.11,17.47L5.7,18.89C4.8,17.73 4.24,16.39 4.07,15H6.09M14,16.62C13.4,16.85 12.72,17 12,17C9.79,17 8,15.21 8,13C8,10.79 9.79,9 12,9C13.74,9 15.21,10.04 15.75,11.53L14,11.53C12.81,11.53 11.72,12.36 11.28,13.53V13.53C10.98,14.35 11.11,15.27 11.56,16H14V16.62Z" />
                </svg>
            </div>
        </div>
    </div>

    <script>
        let stream = null;
        let facingMode = "user"; // front camera by default
        
        function handleFileSelect(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    document.getElementById('photoPreview').src = e.target.result;
                    document.getElementById('photoPreview').style.display = 'block';
                    document.querySelector('.photo-placeholder').style.display = 'none';
                    document.getElementById('nextBtn').disabled = false;
                }
                
                reader.readAsDataURL(input.files[0]);
            }
        }
        
        async function openCamera() {
            try {
                const constraints = {
                    video: { facingMode: facingMode }
                };
                
                stream = await navigator.mediaDevices.getUserMedia(constraints);
                const video = document.getElementById('video');
                video.srcObject = stream;
                
                document.getElementById('cameraContainer').style.display = 'flex';
            } catch (err) {
                console.error('Error accessing camera:', err);
                alert('Could not access camera. Please try uploading a photo instead.');
            }
        }
        
        function closeCamera() {
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
            }
            document.getElementById('cameraContainer').style.display = 'none';
        }
        
        function switchCamera() {
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
            }
            
            facingMode = facingMode === "user" ? "environment" : "user";
            openCamera();
        }
        
        function capturePhoto() {
            const video = document.getElementById('video');
            const canvas = document.createElement('canvas');
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            const ctx = canvas.getContext('2d');
            
            // Flip horizontally if using front camera
            if (facingMode === "user") {
                ctx.translate(canvas.width, 0);
                ctx.scale(-1, 1);
            }
            
            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
            
            // Convert to blob
            canvas.toBlob(function(blob) {
                // Create a file from the blob
                const file = new File([blob], "camera_photo.jpg", {type: "image/jpeg"});
                
                // Create a FileList-like object
                const dataTransfer = new DataTransfer();
                dataTransfer.items.add(file);
                
                // Set the file input's files
                document.getElementById('fileInput').files = dataTransfer.files;
                
                // Update preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('photoPreview').src = e.target.result;
                    document.getElementById('photoPreview').style.display = 'block';
                    document.querySelector('.photo-placeholder').style.display = 'none';
                    document.getElementById('nextBtn').disabled = false;
                }
                reader.readAsDataURL(file);
                
                // Close camera
                closeCamera();
            }, 'image/jpeg', 0.8);
        }
    </script>
</body>
</html> 