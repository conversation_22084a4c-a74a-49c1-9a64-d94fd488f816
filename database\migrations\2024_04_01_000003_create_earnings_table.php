<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('earnings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('creator_id')->constrained('users')->onDelete('cascade');
            $table->decimal('amount', 10, 2);
            $table->string('source'); // 'subscription', 'tips', 'loves'
            $table->foreignId('source_id')->nullable(); // ID of subscription, post (for tips/loves)
            $table->dateTime('earned_at');
            $table->dateTime('paid_at')->nullable();
            $table->string('status'); // 'pending', 'paid', 'failed'
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('earnings');
    }
}; 