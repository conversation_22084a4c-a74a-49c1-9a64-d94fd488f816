@extends('layouts.app')

@section('content')
<div class="user-panel-container">
    <div class="panel-header">
        <h1>Love Statistics</h1>
    </div>
    
    <div class="love-stats-summary">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-heart"></i>
            </div>
            <div class="stat-details">
                <div class="stat-value">{{ number_format($lovesGiven) }}</div>
                <div class="stat-label">Loves Given</div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-heart"></i>
            </div>
            <div class="stat-details">
                <div class="stat-value">{{ number_format($lovesReceived) }}</div>
                <div class="stat-label">Loves Received</div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-dollar-sign"></i>
            </div>
            <div class="stat-details">
                <div class="stat-value">${{ number_format($lovesReceived * 0.01, 2) }}</div>
                <div class="stat-label">Revenue from Loves</div>
            </div>
        </div>
    </div>
    
    <div class="panel-tabs">
        <a href="{{ route('user-panel.love-history') }}" class="tab">Love History</a>
        <a href="{{ route('user-panel.love-stats') }}" class="tab active">Love Stats</a>
        <a href="{{ route('user-panel.revenue') }}" class="tab">Revenue</a>
    </div>
    
    <div class="stats-grid">
        <div class="stats-card">
            <h3>Monthly Love Trends</h3>
            <div class="chart-container">
                <canvas id="monthlyLovesChart"></canvas>
            </div>
        </div>
        
        <div class="stats-card">
            <h3>Top Loved Posts</h3>
            
            @if($topPosts->count() > 0)
                <div class="top-posts-list">
                    @foreach($topPosts as $post)
                        <div class="top-post-item">
                            <div class="post-preview">
                                @if($post->media_type == 'image')
                                    <img src="{{ asset('storage/' . $post->media_url) }}" alt="Post thumbnail">
                                @else
                                    <div class="video-thumbnail">
                                        <i class="fas fa-play-circle"></i>
                                    </div>
                                @endif
                            </div>
                            
                            <div class="post-details">
                                <div class="post-caption">
                                    {{ \Illuminate\Support\Str::limit($post->caption, 30) }}
                                </div>
                                
                                <div class="post-loves">
                                    <i class="fas fa-heart"></i>
                                    <span>{{ $post->loves_count }} loves</span>
                                </div>
                            </div>
                            
                            <a href="{{ route('posts.show', $post->id) }}" class="view-post-btn">
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="empty-state">
                    <p>You don't have any loved posts yet.</p>
                </div>
            @endif
        </div>
    </div>
</div>

<style>
    .user-panel-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .panel-header {
        margin-bottom: 20px;
    }
    
    .panel-header h1 {
        font-size: 24px;
        font-weight: bold;
    }
    
    .love-stats-summary {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 15px;
        margin-bottom: 30px;
    }
    
    .stat-card {
        background: #222;
        border-radius: 10px;
        padding: 15px;
        display: flex;
        align-items: center;
        gap: 15px;
    }
    
    .stat-icon {
        width: 40px;
        height: 40px;
        background: rgba(255, 215, 0, 0.1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        color: #FFD700;
    }
    
    .stat-value {
        font-size: 20px;
        font-weight: bold;
    }
    
    .stat-label {
        font-size: 14px;
        color: #888;
    }
    
    .panel-tabs {
        display: flex;
        border-bottom: 1px solid #333;
        margin-bottom: 20px;
    }
    
    .tab {
        padding: 10px 20px;
        color: #888;
        text-decoration: none;
        border-bottom: 2px solid transparent;
    }
    
    .tab.active {
        color: #FFD700;
        border-bottom: 2px solid #FFD700;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .stats-card {
        background: #222;
        border-radius: 10px;
        padding: 20px;
    }
    
    .stats-card h3 {
        margin-top: 0;
        margin-bottom: 15px;
        font-size: 18px;
    }
    
    .chart-container {
        height: 250px;
    }
    
    .top-posts-list {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }
    
    .top-post-item {
        display: flex;
        gap: 15px;
        position: relative;
    }
    
    .post-preview {
        width: 60px;
        height: 60px;
        border-radius: 8px;
        overflow: hidden;
        flex-shrink: 0;
    }
    
    .post-preview img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .video-thumbnail {
        width: 100%;
        height: 100%;
        background: #333;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
    }
    
    .post-details {
        flex: 1;
    }
    
    .post-caption {
        font-size: 14px;
        margin-bottom: 8px;
    }
    
    .post-loves {
        font-size: 14px;
        color: #ff0066;
    }
    
    .view-post-btn {
        position: absolute;
        top: 0;
        right: 0;
        color: #888;
        font-size: 14px;
    }
    
    .empty-state {
        text-align: center;
        padding: 20px 0;
        color: #888;
    }
    
    @media (max-width: 600px) {
        .love-stats-summary {
            grid-template-columns: 1fr;
        }
    }
</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Monthly loves chart
        const monthlyLovesCtx = document.getElementById('monthlyLovesChart').getContext('2d');
        
        const monthlyLovesData = @json($monthlyLoves);
        
        new Chart(monthlyLovesCtx, {
            type: 'line',
            data: {
                labels: monthlyLovesData.map(item => item.month),
                datasets: [{
                    label: 'Loves Received',
                    data: monthlyLovesData.map(item => item.count),
                    borderColor: '#ff0066',
                    backgroundColor: 'rgba(255, 0, 102, 0.1)',
                    tension: 0.3,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            color: '#888'
                        },
                        grid: {
                            color: '#333'
                        }
                    },
                    x: {
                        ticks: {
                            color: '#888'
                        },
                        grid: {
                            color: '#333'
                        }
                    }
                },
                plugins: {
                    legend: {
                        labels: {
                            color: '#fff'
                        }
                    }
                }
            }
        });
    });
</script>
@endsection 