@extends('layouts.app')

@section('content')
<div class="live-container">
    <!-- Close Button -->
    <button class="close-btn" onclick="window.history.back()">×</button>

    <!-- Title Input -->
    <input type="text"
           class="title-input"
           placeholder="Add a Title..."
           id="streamTitle"
           autocomplete="off">
    <!-- Camera Preview -->

    <div class="camera-preview">


        <video id="cameraPreview" autoplay playsinline muted> </video>

        <!-- Privacy Selector -->
        <div class="privacy-selector">
            <button class="privacy-btn">
                <span class="privacy-icon">👥</span>
                Public
                <span class="dropdown-arrow">▼</span>
            </button>

            <!-- Camera Flip Button -->
            <button class="camera-flip-btn" id="flipCameraBtn">
                <i class="fas fa-camera-rotate"></i>
            </button>
        </div>
    </div>

    <!-- Go Live Button -->
    <div class="go-live-wrapper">
        <button type="submit" class="go-live-btn" id="goLiveBtn">
            GO LIVE <span class="broadcast-icon">((•))</span>
        </button>
    </div>

</div>

<style>
.live-container {
    background: #000;
    min-height: 87dvh;
    position: relative;
    color: white;
    overflow: hidden;
}

.close-btn {
    position: absolute;
    top: 20px;
    left: 20px;
    font-size: 24px;
    color: white;
    background: none;
    border: none;
    z-index: 10;
}

.title-input {
    position: absolute;
    top: 20px;
    left: 60px;
    right: 20px;
    background: transparent;
    border: none;
    color: white;
    font-size: 18px;
    padding: 10px;
    z-index: 100;
    width: calc(100% - 80px);
}

.title-input::placeholder {
    color: rgba(255, 255, 255, 0.8);
}

.title-input:focus {
    outline: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}

.camera-preview {
    width: 100%;
    height: 85dvh;
    position: relative;
}

#cameraPreview {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.privacy-selector {
    position: absolute;
    bottom: 8px;
    left: 20px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 90%;
}

.privacy-btn {
    background: rgba(0,0,0,0.5);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.go-live-wrapper {
    position: fixed;
    bottom: 20dvh;
    left: 50%;
    transform: translateX(-50%);
}

.go-live-btn {
    background: linear-gradient(to right, #FF1493, #FF4500);
    color: white;
    border: none;
    padding: 12px 40px;
    border-radius: 30px;
    font-weight: bold;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.camera-flip-btn {
    position: relative;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255,255,255,0.2);
    border: none;
    color: white;
}

.pre-live-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.pre-live-message {
    background: rgba(0,0,0,0.8);
    padding: 20px 30px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 18px;
}

.pulse-dot {
    width: 15px;
    height: 15px;
    background: #FF1493;
    border-radius: 50%;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(0.8);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.2);
        opacity: 1;
    }
    100% {
        transform: scale(0.8);
        opacity: 0.8;
    }
}
</style>


<script>

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOMContentLoaded');
    let stream;
    let facingMode = 'user';
    let cameraInitialized = false;

    // Initialize camera on page load
    async function initializeCamera() {
        if (cameraInitialized) return;

        try {
            // Request both video and audio permissions explicitly
            stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    facingMode,
                    width: { ideal: 1280 },
                    height: { ideal: 720 }
                },
                audio: true
            });

            const videoElement = document.getElementById('cameraPreview');
            if (videoElement) {
                videoElement.srcObject = stream;

                // Make sure we wait for the video to be ready
                videoElement.onloadedmetadata = function() {
                    videoElement.play().catch(e => console.error('Error playing video:', e));
                    cameraInitialized = true;
                };
            }
        } catch (err) {
            console.error('Camera access error:', err);

            if (err.name === 'NotAllowedError') {
                alert('Camera access denied. Please allow camera access to use this feature.');
            } else if (err.name === 'NotFoundError') {
                alert('No camera found. Please connect a camera to use this feature.');
            } else {
                alert('Unable to access camera. Error: ' + err.message);
            }
        }
    }

    // Try to initialize camera immediately
    initializeCamera();

    // Also try again after a short delay (sometimes helps with certain browsers)
    setTimeout(initializeCamera, 500);

    // Flip camera handler
    document.getElementById('flipCameraBtn').addEventListener('click', async () => {
        if (stream) {
            // Stop all tracks
            stream.getTracks().forEach(track => track.stop());

            // Toggle facing mode
            facingMode = facingMode === 'user' ? 'environment' : 'user';
            cameraInitialized = false;

            // Reinitialize with new facing mode
            await initializeCamera();
        }
    });

    // Go Live button handler
    document.getElementById('goLiveBtn').addEventListener('click', async () => {
        const title = document.getElementById('streamTitle').value.trim();
        if (!title) {
            alert('Please add a title for your stream');
            return;
        }

        const btn = document.getElementById('goLiveBtn');
        btn.disabled = true;

        // Show pre-live state
        const preLiveOverlay = document.createElement('div');
        preLiveOverlay.className = 'pre-live-overlay';
        preLiveOverlay.innerHTML = `
            <div class="pre-live-message">
                <div class="pulse-dot"></div>
                You're about to go live... please wait
            </div>
        `;
        document.querySelector('.live-container').appendChild(preLiveOverlay);

        btn.innerHTML = 'Starting Stream...';

        // Add 2-second delay before going live
        setTimeout(async () => {
            try {
                const response = await fetch('/live/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify({ title })
                });

                const data = await response.json();

                if (data.success) {
                    // Redirect to the live stream page
                    window.location.href = `/live/${data.stream_id}`;
                } else {
                    throw new Error(data.message || 'Failed to start stream');
                }
            } catch (err) {
                console.error('Error starting stream:', err);
                btn.disabled = false;
                btn.innerHTML = 'GO LIVE <span class="broadcast-icon">((•))</span>';
                alert('Failed to start stream. Please try again.');
                // Remove pre-live overlay if there's an error
                document.querySelector('.pre-live-overlay')?.remove();
            }
        }, 2000);
    });
});
</script>

@endsection
