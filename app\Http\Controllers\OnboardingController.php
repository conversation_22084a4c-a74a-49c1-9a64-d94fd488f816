<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\UserProfile;
use App\Models\Interest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class OnboardingController extends Controller
{
    public function index()
    {
        // Show the main onboarding view
        return view('onboarding');
    }

    public function username()
    {
        // If user already has a username, skip to next step
        if (Auth::user()->username) {
            return redirect()->route('setup.gender');
        }
        
        return view('setup.username');
    }

    public function setUsername(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'username' => 'required|alpha_dash|min:3|max:30|unique:users,username'
        ], [
            'username.alpha_dash' => 'Username can only contain letters, numbers, dashes, and underscores.',
            'username.unique' => 'This username is already taken.',
            'username.min' => 'Username must be at least 3 characters long.',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        Auth::user()->update(['username' => $request->username]);
        
        return redirect()->route('setup.gender');
    }

    public function gender()
    {
        // If user already set gender, go to next step
        if (Auth::user()->profile && Auth::user()->profile->gender) {
            return redirect()->route('setup.age');
        }
        
        return view('setup.gender');
    }

    public function setGender(Request $request)
    {
        $request->validate([
            'gender' => 'required|in:male,female,other'
        ]);

        UserProfile::updateOrCreate(
            ['user_id' => Auth::id()],
            ['gender' => $request->gender]
        );
        
        return redirect()->route('setup.age');
    }

    public function age()
    {
        // If user already set birth date, go to next step
        if (Auth::user()->profile && Auth::user()->profile->birth_date) {
            return redirect()->route('setup.interests');
        }
        
        return view('setup.age');
    }

    public function setAge(Request $request)
    {
        $request->validate([
            'birth_date' => 'required|date|before:-13 years'
        ], [
            'birth_date.before' => 'You must be at least 13 years old to use this platform.'
        ]);

        UserProfile::updateOrCreate(
            ['user_id' => Auth::id()],
            ['birth_date' => $request->birth_date]
        );
        
        return redirect()->route('setup.interests');
    }

    public function interests()
    {
        // Get all available interests
        $interests = Interest::all();
        
        // If user already set interests, go to next step
        if (Auth::user()->interests()->count() > 0) {
            return redirect()->route('setup.photo');
        }
        
        return view('setup.interests', compact('interests'));
    }

    public function setInterests(Request $request)
    {
        $request->validate([
            'interests' => 'required|array|min:3',
            'interests.*' => 'exists:interests,id'
        ], [
            'interests.required' => 'Please select at least one interest.',
            'interests.min' => 'Please select at least 3 interests.'
        ]);

        // Clear previous interests if any
        Auth::user()->interests()->detach();
        
        // Attach new interests
        foreach ($request->interests as $interestId) {
            Auth::user()->interests()->attach($interestId);
        }
        
        return redirect()->route('setup.photo');
    }

    public function photo()
    {
        // If user already uploaded photo, go to home
        if (Auth::user()->profile && Auth::user()->profile->photo) {
            Auth::user()->update(['onboarding_completed' => true]);
            return redirect()->route('home');
        }
        
        return view('setup.photo');
    }

    public function setPhoto(Request $request)
    {
        $request->validate([
            'photo' => 'required|image|max:5120' // 5MB max
        ]);

        if ($request->hasFile('photo')) {
            $file = $request->file('photo');
            $filename = time() . '_' . $file->getClientOriginalName();
            $path = $file->storeAs('profile-photos', $filename, 'public');
            
            UserProfile::updateOrCreate(
                ['user_id' => Auth::id()],
                ['photo' => $path]
            );
            
            // Mark onboarding as completed
            Auth::user()->update(['onboarding_completed' => true]);
            
            return redirect()->route('home');
        }
        
        return back()->with('error', 'Please upload a profile photo to continue.');
    }
} 