@extends('layouts.app')

@section('content')
<div class="search-container">
    <div class="search-header">
        <div class="search-input-container">
            <i class="fas fa-search search-icon"></i>
            <input type="text" id="searchInput" class="search-input" placeholder="Search" autocomplete="off">
            <i class="fas fa-times-circle clear-icon" id="clearSearchBtn"></i>
        </div>
    </div>
    
    <div class="search-content">
        <div id="initialContent">
            @if(count($recentSearches) > 0)
            <div class="search-section">
                <div class="section-header">
                    <h3>Recent Searches</h3>
                    <button id="clearRecentBtn" class="clear-btn">Clear All</button>
                </div>
                <div class="recent-searches">
                    @foreach($recentSearches as $search)
                    <div class="recent-search-item" data-query="{{ $search }}">
                        <i class="fas fa-history"></i>
                        <span>{{ $search }}</span>
                        <i class="fas fa-arrow-right"></i>
                    </div>
                    @endforeach
                </div>
            </div>
            @endif
            
            @if(count($trendingSearches) > 0)
            <div class="search-section">
                <div class="section-header">
                    <h3>Trending</h3>
                </div>
                <div class="trending-searches">
                    @foreach($trendingSearches as $trending)
                    <div class="trending-search-item" data-query="{{ $trending->query }}">
                        <i class="fas fa-fire"></i>
                        <span>{{ $trending->query }}</span>
                        <i class="fas fa-arrow-right"></i>
                    </div>
                    @endforeach
                </div>
            </div>
            @endif
        </div>
        
        <div id="searchResults" class="search-results" style="display: none;">
            <div id="usersSection" class="search-section" style="display: none;">
                <div class="section-header">
                    <h3>Users</h3>
                </div>
                <div id="usersResults" class="users-results"></div>
            </div>
            
            <div id="postsSection" class="search-section" style="display: none;">
                <div class="section-header">
                    <h3>Posts</h3>
                </div>
                <div id="postsResults" class="posts-results"></div>
            </div>
            
            <div id="campsSection" class="search-section" style="display: none;">
                <div class="section-header">
                    <h3>Camps</h3>
                </div>
                <div id="campsResults" class="camps-results"></div>
            </div>
            
            <div id="noResults" class="no-results" style="display: none;">
                <div class="no-results-icon">
                    <i class="fas fa-search"></i>
                </div>
                <h3>No results found</h3>
                <p>Try searching for something else</p>
            </div>
        </div>
    </div>
</div>

<style>
    .search-container {
        display: flex;
        flex-direction: column;
        height: calc(100vh - 60px);
        background-color: #121212;
    }
    
    .search-header {
        padding: 15px;
        background-color: #1a1a1a;
        position: sticky;
        top: 0;
        z-index: 10;
    }
    
    .search-input-container {
        position: relative;
        display: flex;
        align-items: center;
    }
    
    .search-icon {
        position: absolute;
        left: 15px;
        color: #888;
    }
    
    .search-input {
        width: 100%;
        padding: 12px 40px;
        background-color: #2a2a2a;
        border: none;
        border-radius: 8px;
        color: #fff;
        font-size: 16px;
    }
    
    .search-input:focus {
        outline: none;
        background-color: #333;
    }
    
    .clear-icon {
        position: absolute;
        right: 15px;
        color: #888;
        cursor: pointer;
        display: none;
    }
    
    .search-content {
        flex: 1;
        overflow-y: auto;
        padding: 15px;
    }
    
    .search-section {
        margin-bottom: 25px;
    }
    
    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }
    
    .section-header h3 {
        font-size: 18px;
        font-weight: 600;
        color: #fff;
    }
    
    .clear-btn {
        background: none;
        border: none;
        color: #FFD700;
        font-size: 14px;
        cursor: pointer;
    }
    
    .recent-search-item, .trending-search-item {
        display: flex;
        align-items: center;
        padding: 12px;
        background-color: #1a1a1a;
        border-radius: 8px;
        margin-bottom: 8px;
        cursor: pointer;
    }
    
    .recent-search-item i, .trending-search-item i {
        margin-right: 12px;
        color: #888;
    }
    
    .recent-search-item i:last-child, .trending-search-item i:last-child {
        margin-left: auto;
        margin-right: 0;
    }
    
    .recent-search-item:hover, .trending-search-item:hover {
        background-color: #222;
    }
    
    .user-result {
        display: flex;
        align-items: center;
        padding: 12px;
        background-color: #1a1a1a;
        border-radius: 8px;
        margin-bottom: 8px;
        cursor: pointer;
    }
    
    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
        margin-right: 12px;
    }
    
    .user-info {
        flex: 1;
    }
    
    .user-name {
        font-weight: 600;
        color: #fff;
        display: flex;
        align-items: center;
    }
    
    .verified-badge {
        color: #FFD700;
        margin-left: 5px;
        font-size: 12px;
    }
    
    .user-username {
        color: #888;
        font-size: 14px;
    }
    
    .post-result {
        display: flex;
        padding: 12px;
        background-color: #1a1a1a;
        border-radius: 8px;
        margin-bottom: 8px;
        cursor: pointer;
    }
    
    .post-thumbnail {
        width: 60px;
        height: 60px;
        border-radius: 4px;
        object-fit: cover;
        margin-right: 12px;
    }
    
    .post-info {
        flex: 1;
    }
    
    .post-caption {
        color: #fff;
        margin-bottom: 5px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
    
    .post-user {
        display: flex;
        align-items: center;
        color: #888;
        font-size: 14px;
    }
    
    .post-user-avatar {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        object-fit: cover;
        margin-right: 5px;
    }
    
    .camp-result {
        display: flex;
        padding: 12px;
        background-color: #1a1a1a;
        border-radius: 8px;
        margin-bottom: 8px;
        cursor: pointer;
    }
    
    .camp-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        background-color: #333;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
    }
    
    .camp-icon i {
        color: #FFD700;
        font-size: 20px;
    }
    
    .camp-info {
        flex: 1;
    }
    
    .camp-name {
        font-weight: 600;
        color: #fff;
        margin-bottom: 3px;
    }
    
    .camp-description {
        color: #888;
        font-size: 14px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
    
    .camp-members {
        color: #666;
        font-size: 12px;
        margin-top: 5px;
    }
    
    .no-results {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px 0;
        color: #888;
        text-align: center;
    }
    
    .no-results-icon {
        font-size: 48px;
        margin-bottom: 15px;
        color: #444;
    }
    
    .no-results h3 {
        font-size: 18px;
        margin-bottom: 5px;
        color: #fff;
    }
    
    .no-results p {
        font-size: 14px;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchInput');
        const clearSearchBtn = document.getElementById('clearSearchBtn');
        const initialContent = document.getElementById('initialContent');
        const searchResults = document.getElementById('searchResults');
        const usersSection = document.getElementById('usersSection');
        const postsSection = document.getElementById('postsSection');
        const campsSection = document.getElementById('campsSection');
        const usersResults = document.getElementById('usersResults');
        const postsResults = document.getElementById('postsResults');
        const campsResults = document.getElementById('campsResults');
        const noResults = document.getElementById('noResults');
        const clearRecentBtn = document.getElementById('clearRecentBtn');
        
        // Focus the search input when the page loads
        searchInput.focus();
        
        // Handle search input
        let debounceTimer;
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();
            
            // Show/hide clear button
            clearSearchBtn.style.display = query.length > 0 ? 'block' : 'none';
            
            // Clear previous debounce timer
            clearTimeout(debounceTimer);
            
            if (query.length === 0) {
                // Show initial content, hide search results
                initialContent.style.display = 'block';
                searchResults.style.display = 'none';
                return;
            }
            
            // Debounce search to avoid too many requests
            debounceTimer = setTimeout(() => {
                performSearch(query);
            }, 300);
        });
        
        // Clear search input
        clearSearchBtn.addEventListener('click', function() {
            searchInput.value = '';
            searchInput.focus();
            clearSearchBtn.style.display = 'none';
            initialContent.style.display = 'block';
            searchResults.style.display = 'none';
        });
        
        // Clear recent searches
        if (clearRecentBtn) {
            clearRecentBtn.addEventListener('click', function() {
                fetch('/search/clear-recent', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const recentSearchesSection = this.closest('.search-section');
                        if (recentSearchesSection) {
                            recentSearchesSection.remove();
                        }
                    }
                });
            });
        }
        
        // Handle recent and trending search clicks
        document.querySelectorAll('.recent-search-item, .trending-search-item').forEach(item => {
            item.addEventListener('click', function() {
                const query = this.getAttribute('data-query');
                searchInput.value = query;
                clearSearchBtn.style.display = 'block';
                performSearch(query);
            });
        });
        
        // Perform search
        function performSearch(query) {
            fetch(`/api/search?q=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    // Hide initial content, show search results
                    initialContent.style.display = 'none';
                    searchResults.style.display = 'block';
                    
                    // Process users
                    if (data.users && data.users.length > 0) {
                        usersSection.style.display = 'block';
                        usersResults.innerHTML = '';
                        
                        data.users.forEach(user => {
                            const userHtml = `
                                <a href="/profile/${user.username}" class="user-result">
                                    <img src="${user.profile_photo}" alt="${user.username}" class="user-avatar">
                                    <div class="user-info">
                                        <div class="user-name">
                                            ${user.name || user.username}
                                            ${user.is_verified ? '<i class="fas fa-check-circle verified-badge"></i>' : ''}
                                        </div>
                                        <div class="user-username">@${user.username}</div>
                                    </div>
                                </a>
                            `;
                            usersResults.innerHTML += userHtml;
                        });
                    } else {
                        usersSection.style.display = 'none';
                    }
                    
                    // Process posts
                    if (data.posts && data.posts.length > 0) {
                        postsSection.style.display = 'block';
                        postsResults.innerHTML = '';
                        
                        data.posts.forEach(post => {
                            const postHtml = `
                                <a href="/posts/${post.id}" class="post-result">
                                    <img src="${post.media_url}" alt="Post" class="post-thumbnail">
                                    <div class="post-info">
                                        <div class="post-caption">${post.caption || 'No caption'}</div>
                                        <div class="post-user">
                                            <img src="${post.user.profile_photo}" alt="${post.user.username}" class="post-user-avatar">
                                            <span>@${post.user.username}</span>
                                        </div>
                                    </div>
                                </a>
                            `;
                            postsResults.innerHTML += postHtml;
                        });
                    } else {
                        postsSection.style.display = 'none';
                    }
                    
                    // Process camps
                    if (data.camps && data.camps.length > 0) {
                        campsSection.style.display = 'block';
                        campsResults.innerHTML = '';
                        
                        data.camps.forEach(camp => {
                            const campHtml = `
                                <a href="/camps/${camp.id}" class="camp-result">
                                    <div class="camp-icon">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <div class="camp-info">
                                        <div class="camp-name">${camp.name}</div>
                                        <div class="camp-description">${camp.description || 'No description'}</div>
                                        <div class="camp-members">${camp.member_count || 0} members</div>
                                    </div>
                                </a>
                            `;
                            campsResults.innerHTML += campHtml;
                        });
                    } else {
                        campsSection.style.display = 'none';
                    }
                    
                    // Show no results message if needed
                    if ((!data.users || data.users.length === 0) && 
                        (!data.posts || data.posts.length === 0) && 
                        (!data.camps || data.camps.length === 0)) {
                        noResults.style.display = 'flex';
                    } else {
                        noResults.style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('Search error:', error);
                });
        }
    });
</script>
@endsection 