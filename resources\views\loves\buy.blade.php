@extends('layouts.app')

@section('content')
<div class="loves-container">
    <div class="loves-header">
        <h1>Buy Loves</h1>
        <div class="current-balance">
            <i class="fas fa-heart"></i>
            <span class="love-balance">{{ Auth::user()->loveWallet ? Auth::user()->loveWallet->love_balance : 0 }}</span>
        </div>
    </div>

    @if(session('success'))
    <div class="alert alert-success">
        {{ session('success') }}
    </div>
    @endif

    @if(session('error'))
    <div class="alert alert-error">
        {{ session('error') }}
    </div>
    @endif

    <div class="loves-description">
        <p>Support your favorite content creators by sending loves. Each love is worth $0.01 to the creator.</p>
    </div>

    <div class="love-packs-grid">
        @foreach($lovePacks as $index => $pack)
            <div class="love-pack-card">
                <div class="love-pack-icon">
                    <i class="fas fa-heart"></i>
                </div>
                <div class="love-pack-amount">{{ number_format($pack['amount']) }}</div>
                <div class="love-pack-price">BDT {{ number_format($pack['price'], 2) }}</div>
                <form action="{{ route('loves.purchase') }}" method="POST">
                    @csrf
                    <input type="hidden" name="pack_id" value="{{ $index }}">
                    <button type="submit" class="buy-pack-btn">Buy Now (Test Mode)</button>
                </form>
            </div>
        @endforeach
    </div>

    <div class="test-mode-notice">
        <p><strong>Note:</strong> This is running in test mode. No actual payment will be processed.</p>
        <p>To implement real payments, install the Stripe PHP package: <code>composer require stripe/stripe-php</code></p>
    </div>
</div>

<style>
.loves-container {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
}

.loves-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.current-balance {
    display: flex;
    align-items: center;
    gap: 8px;
    background: #222;
    padding: 8px 15px;
    border-radius: 20px;
}

.current-balance i {
    color: #ff0066;
}

.loves-description {
    margin-bottom: 30px;
    text-align: center;
}

.love-packs-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.love-pack-card {
    background: #222;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    transition: transform 0.3s ease;
}

.love-pack-card:hover {
    transform: translateY(-5px);
}

.love-pack-icon {
    font-size: 30px;
    color: #ff0066;
    margin-bottom: 10px;
}

.love-pack-amount {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
}

.love-pack-price {
    color: #FFD700;
    font-weight: bold;
    margin-bottom: 15px;
}

.buy-pack-btn {
    background: #FFD700;
    color: black;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
    width: 100%;
}

.buy-pack-btn:hover {
    background: #e6c200;
}

.alert {
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    text-align: center;
}

.alert-success {
    background: rgba(76, 175, 80, 0.2);
    border: 1px solid #4CAF50;
    color: #4CAF50;
}

.alert-error {
    background: rgba(244, 67, 54, 0.2);
    border: 1px solid #F44336;
    color: #F44336;
}

.test-mode-notice {
    background: #333;
    padding: 15px;
    border-radius: 10px;
    margin-top: 30px;
    text-align: center;
}

.test-mode-notice code {
    background: #222;
    padding: 3px 6px;
    border-radius: 3px;
}

@media (max-width: 600px) {
    .love-packs-grid {
        grid-template-columns: 1fr;
    }
}
</style>
@endsection 