<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - InstaPWA</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            background: #000;
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .signup-container {
            width: 90%;
            max-width: 400px;
            background: #111;
            padding: 30px;
            border-radius: 10px;
        }

        .logo {
            width: 200px;
            margin: 0 auto 30px;
            display: block;
        }

        .welcome-text {
            text-align: center;
            margin-bottom: 30px;
            color: #888;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-control {
            width: 100%;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 5px;
            background: #222;
            color: white;
            font-size: 16px;
            box-sizing: border-box;
        }

        .form-control:focus {
            outline: none;
            border-color: #FFD700;
        }

        .btn {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin-bottom: 15px;
        }

        .btn-primary {
            background: #FFD700;
            color: black;
        }

        .btn-outline {
            background: transparent;
            border: 1px solid #FFD700;
            color: #FFD700;
        }

        .divider {
            display: flex;
            align-items: center;
            text-align: center;
            margin: 20px 0;
            color: #666;
        }

        .divider::before,
        .divider::after {
            content: '';
            flex: 1;
            border-bottom: 1px solid #333;
        }

        .divider span {
            padding: 0 10px;
        }

        .error-message {
            color: #ff4444;
            margin-bottom: 20px;
            text-align: center;
        }

        .terms {
            text-align: center;
            font-size: 12px;
            color: #666;
            margin: 20px 0;
        }

        .terms a {
            color: #FFD700;
            text-decoration: none;
        }

        .login-link {
            text-align: center;
            margin-top: 20px;
            color: #666;
        }

        .login-link a {
            color: #FFD700;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="signup-container">
        <img src="/images/logo.png" alt="InstaPWA" class="logo">
        
        <div class="welcome-text">
            Sign up to see photos and videos from your friends.
        </div>

        @if($errors->any())
            <div class="error-message">
                {{ $errors->first() }}
            </div>
        @endif

        <button onclick="window.location.href='{{ route('signup.google') }}'" class="btn btn-outline">
            Continue with Google
        </button>

        <div class="divider">
            <span>OR</span>
        </div>

        <form action="{{ route('signup.email') }}" method="POST">
            @csrf
            <div class="form-group">
                <input type="email" name="email" class="form-control" placeholder="Email" value="{{ old('email') }}" required>
            </div>

            <div class="form-group">
                <input type="password" name="password" class="form-control" placeholder="Password" required>
            </div>

            <div class="form-group">
                <input type="password" name="password_confirmation" class="form-control" placeholder="Confirm Password" required>
            </div>

            <button type="submit" class="btn btn-primary">Sign Up</button>
        </form>

        <div class="terms">
            By signing up, you agree to our <a href="#">Terms</a>, <a href="#">Privacy Policy</a> and <a href="#">Cookies Policy</a>.
        </div>

        <div class="login-link">
            Have an account? <a href="{{ route('login') }}">Log In</a>
        </div>
    </div>
</body>
</html> 