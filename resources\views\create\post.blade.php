@extends('layouts.app')

@section('content')
<meta name="csrf-token" content="{{ csrf_token() }}">

<style>
    .create-post-container {
        min-height: 100vh;
        background: #000;
        color: white;
        display: flex;
        flex-direction: column;
    }

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        background: #111;
        border-bottom: 1px solid #222;
    }

    .back-button {
        color: white;
        font-size: 20px;
        background: none;
        border: none;
        padding: 0;
        cursor: pointer;
    }

    .header-title {
        font-size: 18px;
        font-weight: bold;
    }

    .post-type-tabs {
        display: flex;
        justify-content: center;
        gap: 15px;
        margin-top: 10px;
    }

    .tab {
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 14px;
        cursor: pointer;
        color: #999;
    }

    .tab.active {
        background: #FFD700;
        color: black;
        font-weight: 500;
    }

    .cancel-button {
        color: #FFD700;
        background: none;
        border: none;
        font-size: 14px;
        cursor: pointer;
    }

    .post-button {
        background: #FFD700;
        color: black;
        padding: 8px 20px;
        border-radius: 20px;
        border: none;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
    }

    .post-button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .content-area {
        flex: 1;
        display: flex;
        flex-direction: column;
        position: relative;
    }

    .preview-container {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #000;
        overflow: hidden;
        position: relative;
    }

    #mediaPreview {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }

    .user-input {
        padding: 15px;
        display: flex;
        gap: 10px;
        background: #111;
        border-top: 1px solid #222;
    }

    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
    }

    .caption-input {
        flex: 1;
        background: transparent;
        border: none;
        color: white;
        font-size: 16px;
        resize: none;
        padding: 10px 0;
        outline: none;
    }

    .caption-input::placeholder {
        color: #666;
    }

    .media-gallery {
        background: #111;
        padding: 10px;
        border-top: 1px solid #222;
    }

    .gallery-grid {
        display: flex;
        overflow-x: auto;
        gap: 10px;
        padding-bottom: 5px;
        scrollbar-width: none; /* Firefox */
    }

    .gallery-grid::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Edge */
    }

    .gallery-item {
        width: 70px;
        height: 70px;
        border-radius: 8px;
        overflow: hidden;
        position: relative;
        flex-shrink: 0;
    }

    .gallery-item img, .gallery-item video {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .gallery-item.selected {
        border: 2px solid #FFD700;
    }

    .bottom-toolbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        background: #111;
        border-top: 1px solid #222;
    }

    .toolbar-left {
        display: flex;
        gap: 20px;
    }

    .toolbar-right {
        display: flex;
        align-items: center;
    }

    .tool-button {
        background: none;
        border: none;
        color: white;
        font-size: 20px;
        cursor: pointer;
        padding: 5px;
    }

    .capture-button {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: white;
        border: none;
        position: relative;
        cursor: pointer;
    }

    .capture-button::after {
        content: '';
        position: absolute;
        top: 5px;
        left: 5px;
        right: 5px;
        bottom: 5px;
        border-radius: 50%;
        background: #FFD700;
    }
</style>

<div class="create-post-container">
    <div class="header">
        <button class="back-button" onclick="window.history.back()">
            <i class="fas fa-arrow-left"></i>
        </button>
        <div class="header-title">Create Post</div>
        <button class="post-button" id="postBtn" disabled>Post</button>
    </div>

    <div class="post-type-tabs">
        <div class="tab">Story</div>
        <div class="tab active">Post</div>
        <div class="tab">Reel</div>
    </div>

    <div class="content-area">
        <div class="preview-container" id="previewContainer">
            <!-- Preview will be added here by JS -->
        </div>

        <div class="user-input">
            @if(Auth::user()->profile && Auth::user()->profile->photo)
                <img src="{{ asset('storage/' . Auth::user()->profile->photo) }}" alt="Your avatar" class="user-avatar">
            @else
                <img src="/images/default-avatar.png" alt="Default avatar" class="user-avatar">
            @endif
            <textarea class="caption-input" id="captionInput" placeholder="What's happening?"></textarea>
        </div>
    </div>

    <div class="media-gallery">
        <div class="gallery-grid" id="galleryGrid">
            <!-- Gallery items will be added here by JS -->
        </div>
    </div>

    <div class="bottom-toolbar">
        <div class="toolbar-left">
            <button class="tool-button" onclick="document.getElementById('galleryInput').click()">
                <i class="fas fa-image"></i>
            </button>
            <button class="tool-button" onclick="document.getElementById('gifInput').click()">
                <i class="fas fa-gift"></i>
            </button>
            <button class="tool-button">
                <i class="fas fa-map-marker-alt"></i>
            </button>
        </div>
        <button class="capture-button" onclick="document.getElementById('cameraInput').click()"></button>
        <div class="toolbar-right">
            <button class="tool-button">
                <i class="fas fa-circle"></i>
            </button>
        </div>
    </div>

    <form id="postForm" action="{{ route('posts.store') }}" method="POST" enctype="multipart/form-data" style="display: none;">
        @csrf
        <input type="hidden" name="post_type" value="post">
        <input type="file" id="galleryInput" name="media[]" multiple accept="image/*,video/*" onchange="handleFileSelect(this, 'gallery')">
        <input type="file" id="cameraInput" name="camera_media" accept="image/*,video/*" capture onchange="handleFileSelect(this, 'camera')">
        <input type="file" id="gifInput" name="gif_media" accept="image/gif" onchange="handleFileSelect(this, 'gif')">
        <textarea name="caption" id="hiddenCaption"></textarea>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const previewContainer = document.getElementById('previewContainer');
    const galleryGrid = document.getElementById('galleryGrid');
    const postBtn = document.getElementById('postBtn');
    const captionInput = document.getElementById('captionInput');
    const hiddenCaption = document.getElementById('hiddenCaption');
    const postForm = document.getElementById('postForm');
    
    let selectedFiles = [];
    let currentPreviewIndex = 0;

    // Handle file selection (from gallery, camera, or gif)
    window.handleFileSelect = function(input, source) {
        if (input.files && input.files.length > 0) {
            // Add new files to our collection
            const newFiles = Array.from(input.files);
            
            // Process each file
            newFiles.forEach(file => {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    // Add to selected files array
                    selectedFiles.push({
                        file: file,
                        dataUrl: e.target.result,
                        type: file.type.startsWith('video/') ? 'video' : 'image'
                    });
                    
                    // Add to gallery grid
                    addToGalleryGrid(e.target.result, selectedFiles.length - 1, file.type);
                    
                    // If this is the first file, show it in preview
                    if (selectedFiles.length === 1) {
                        showPreview(0);
                    }
                    
                    // Enable post button
                    postBtn.disabled = false;
                };
                
                reader.readAsDataURL(file);
            });
        }
    };
    
    // Add item to gallery grid
    function addToGalleryGrid(dataUrl, index, fileType) {
        const div = document.createElement('div');
        div.className = 'gallery-item';
        if (index === 0) {
            div.classList.add('selected');
        }
        
        let mediaElement;
        if (fileType.startsWith('video/')) {
            mediaElement = document.createElement('video');
            mediaElement.src = dataUrl;
            mediaElement.muted = true;
        } else {
            mediaElement = document.createElement('img');
            mediaElement.src = dataUrl;
        }
        
        div.appendChild(mediaElement);
        galleryGrid.appendChild(div);
        
        div.addEventListener('click', function() {
            document.querySelectorAll('.gallery-item').forEach(item => item.classList.remove('selected'));
            div.classList.add('selected');
            showPreview(index);
        });
    }
    
    // Show preview of selected file
    function showPreview(index) {
        currentPreviewIndex = index;
        previewContainer.innerHTML = '';
        
        const selectedFile = selectedFiles[index];
        let mediaElement;
        
        if (selectedFile.type === 'video') {
            mediaElement = document.createElement('video');
            mediaElement.src = selectedFile.dataUrl;
            mediaElement.controls = true;
            mediaElement.autoplay = true;
            mediaElement.loop = true;
            mediaElement.id = 'mediaPreview';
        } else {
            mediaElement = document.createElement('img');
            mediaElement.src = selectedFile.dataUrl;
            mediaElement.id = 'mediaPreview';
        }
        
        previewContainer.appendChild(mediaElement);
    }
    
    // Handle post button click
    postBtn.addEventListener('click', function() {
        // Transfer caption to hidden input
        hiddenCaption.value = captionInput.value;
        
        // Create a FormData object
        const formData = new FormData(postForm);
        
        // Remove existing files from the form
        formData.delete('media[]');
        formData.delete('media');
        
        // Add only the first selected file (since our controller handles just one file)
        if (selectedFiles.length > 0) {
            formData.append('media', selectedFiles[0].file);
        }
        
        // Get CSRF token safely
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        const headers = {};
        
        // Only add CSRF token if it exists
        if (csrfToken) {
            headers['X-CSRF-TOKEN'] = csrfToken.content;
        }
        
        // Submit the form
        fetch(postForm.action, {
            method: 'POST',
            body: formData,
            headers: headers
        })
        .then(response => {
            // First check if the response is OK
            if (!response.ok) {
                // Try to parse as JSON first
                return response.json()
                    .catch(() => {
                        // If it's not JSON, get the text
                        return response.text().then(text => {
                            throw new Error(`Server error: ${response.status}. Details: ${text.substring(0, 100)}...`);
                        });
                    })
                    .then(errorData => {
                        // If we got JSON but status is not OK, throw the error message
                        throw new Error(errorData.message || `Server error: ${response.status}`);
                    });
            }
            // If response is OK, parse as JSON
            return response.json();
        })
        .then(data => {
            if (data.success) {
                window.location.href = data.redirect;
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('There was a problem with the fetch operation:', error);
            alert('An error occurred while creating the post: ' + error.message);
        });
    });
    
    // Handle tab switching
    document.querySelectorAll('.tab').forEach(tab => {
        tab.addEventListener('click', function() {
            document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
            this.classList.add('active');
            document.querySelector('input[name="post_type"]').value = this.textContent.toLowerCase().trim();
        });
    });
});
</script>
@endsection 