class StreamRecorder {
    constructor(stream, streamId) {
        this.stream = stream;
        this.streamId = streamId;
        this.mediaRecorder = null;
        this.recordedChunks = [];
        this.isRecording = false;
        this.uploadQueue = [];
        this.isUploading = false;
        this.chunkSize = 5 * 1024 * 1024; // 5MB chunks
        this.maxRetries = 3;

        // Bind methods
        this.handleDataAvailable = this.handleDataAvailable.bind(this);
        this.processUploadQueue = this.processUploadQueue.bind(this);
        this.uploadChunk = this.uploadChunk.bind(this);
    }

    start() {
        try {
            const options = {
                mimeType: this.getSupportedMimeType(),
                videoBitsPerSecond: 2500000, // 2.5 Mbps
                audioBitsPerSecond: 128000   // 128 kbps
            };

            this.mediaRecorder = new MediaRecorder(this.stream, options);
            this.mediaRecorder.ondataavailable = this.handleDataAvailable;
            this.mediaRecorder.start(1000); // Capture in 1-second chunks
            this.isRecording = true;

            console.log('Recording started with options:', options);
        } catch (error) {
            console.error('Error starting recording:', error);
            throw error;
        }
    }

    stop() {
        return new Promise((resolve, reject) => {
            try {
                if (!this.mediaRecorder || this.mediaRecorder.state === 'inactive') {
                    resolve();
                    return;
                }

                this.mediaRecorder.onstop = async () => {
                    try {
                        await this.uploadFinalRecording();
                        resolve();
                    } catch (error) {
                        reject(error);
                    }
                };

                this.mediaRecorder.stop();
                this.isRecording = false;
            } catch (error) {
                reject(error);
            }
        });
    }

    handleDataAvailable(event) {
        if (event.data && event.data.size > 0) {
            this.recordedChunks.push(event.data);

            // If we have enough data, add to upload queue
            if (this.getTotalSize() >= this.chunkSize) {
                this.queueChunkForUpload();
            }
        }
    }

    getTotalSize() {
        return this.recordedChunks.reduce((total, chunk) => total + chunk.size, 0);
    }

    queueChunkForUpload() {
        // Create a blob from current chunks
        const blob = new Blob(this.recordedChunks, { type: this.getSupportedMimeType() });
        
        // Split blob into chunks if needed
        const chunks = this.splitBlob(blob);
        
        // Add chunks to upload queue
        chunks.forEach(chunk => {
            this.uploadQueue.push({
                data: chunk,
                retries: 0
            });
        });

        // Clear recorded chunks after queuing
        this.recordedChunks = [];

        // Start processing queue if not already processing
        if (!this.isUploading) {
            this.processUploadQueue();
        }
    }

    async processUploadQueue() {
        if (this.isUploading || this.uploadQueue.length === 0) return;

        this.isUploading = true;

        try {
            while (this.uploadQueue.length > 0) {
                const chunk = this.uploadQueue[0];

                try {
                    await this.uploadChunk(chunk.data);
                    this.uploadQueue.shift(); // Remove successfully uploaded chunk
                } catch (error) {
                    chunk.retries++;
                    console.error(`Failed to upload chunk (attempt ${chunk.retries}):`, error);

                    if (chunk.retries >= this.maxRetries) {
                        console.error('Max retries reached for chunk, skipping...');
                        this.uploadQueue.shift();
                    } else {
                        // Wait before retrying
                        await new Promise(resolve => setTimeout(resolve, 1000 * chunk.retries));
                    }
                }
            }
        } finally {
            this.isUploading = false;
        }
    }

    async uploadChunk(blob) {
        const formData = new FormData();
        formData.append('chunk', blob);
        formData.append('stream_id', this.streamId);
        formData.append('is_final', 'false');

        const response = await fetch('/livestream/upload-chunk', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: formData
        });

        if (!response.ok) {
            throw new Error(`Failed to upload chunk: ${response.statusText}`);
        }

        return response.json();
    }

    async uploadFinalRecording() {
        // First, upload any remaining chunks
        if (this.recordedChunks.length > 0) {
            this.queueChunkForUpload();
        }

        // Wait for all chunks to upload
        while (this.uploadQueue.length > 0) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        // Create thumbnail
        const thumbnail = await this.createThumbnail();

        // Upload final metadata
        const formData = new FormData();
        formData.append('stream_id', this.streamId);
        formData.append('is_final', 'true');
        
        if (thumbnail) {
            formData.append('thumbnail', thumbnail, 'thumbnail.jpg');
        }

        const response = await fetch('/livestream/finalize-recording', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: formData
        });

        if (!response.ok) {
            throw new Error('Failed to finalize recording');
        }

        return response.json();
    }

    async createThumbnail() {
        try {
            const video = document.createElement('video');
            video.srcObject = this.stream;
            await video.play();

            const canvas = document.createElement('canvas');
            canvas.width = 320;
            canvas.height = 180;
            const ctx = canvas.getContext('2d');
            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

            return new Promise((resolve) => {
                canvas.toBlob((blob) => {
                    resolve(blob);
                }, 'image/jpeg', 0.8);
            });
        } catch (error) {
            console.error('Error creating thumbnail:', error);
            return null;
        }
    }

    splitBlob(blob) {
        const chunks = [];
        let start = 0;

        while (start < blob.size) {
            const end = Math.min(start + this.chunkSize, blob.size);
            chunks.push(blob.slice(start, end));
            start = end;
        }

        return chunks;
    }

    getSupportedMimeType() {
        const types = [
            'video/webm;codecs=vp9,opus',
            'video/webm;codecs=vp8,opus',
            'video/webm',
            'video/mp4'
        ];

        for (const type of types) {
            if (MediaRecorder.isTypeSupported(type)) {
                return type;
            }
        }

        throw new Error('No supported video MIME types found');
    }
}

export default StreamRecorder; 