<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Choose Gender - InstaPWA</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            background-color: #d31414;
            color: white;
            min-height: 100vh;
        }

        .container {
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            height: 100vh;
        }

        .logo {
            width: 150px;
            margin: 40px 0;
        }

        .title {
            font-size: 28px;
            margin-bottom: 40px;
            text-align: center;
        }

        .gender-options {
            display: flex;
            gap: 30px;
            margin-bottom: 60px;
        }

        .gender-option {
            width: 120px;
            height: 120px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .gender-option.active {
            background: rgba(255, 215, 0, 0.2);
            border: 2px solid #FFD700;
        }

        .gender-icon {
            width: 40px;
            height: 40px;
            fill: white;
        }

        .next-btn {
            width: 100%;
            padding: 15px;
            background: #FFD700;
            color: black;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin-top: auto;
            max-width: 300px;
        }

        .next-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <img src="/images/logo.png" alt="InstaPWA Logo" class="logo">
        
        <div class="title">I am a</div>

        <form action="{{ route('setup.gender') }}" method="POST" id="genderForm">
            @csrf
            <input type="hidden" name="gender" id="selectedGender">
            
            <div class="gender-options">
                <div class="gender-option" data-gender="male" onclick="selectGender('male', this)">
                    <svg class="gender-icon" viewBox="0 0 24 24" fill="white">
                        <path d="M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2M10.5,7H13.5A2,2 0 0,1 15.5,9V14.5H14V22H10V14.5H8.5V9A2,2 0 0,1 10.5,7Z"/>
                    </svg>
                    <span>Man</span>
                </div>

                <div class="gender-option" data-gender="female" onclick="selectGender('female', this)">
                    <svg class="gender-icon" viewBox="0 0 24 24" fill="white">
                        <path d="M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2M10.5,22V16H7.5L10.09,8.41C10.34,7.59 11.1,7 12,7C12.9,7 13.66,7.59 13.91,8.41L16.5,16H13.5V22H10.5Z"/>
                    </svg>
                    <span>Woman</span>
                </div>
            </div>

            <button type="submit" class="next-btn" id="nextBtn" disabled>Next</button>
        </form>
    </div>

    <script>
        function selectGender(gender, element) {
            document.querySelectorAll('.gender-option').forEach(opt => {
                opt.classList.remove('active');
            });
            element.classList.add('active');
            document.getElementById('selectedGender').value = gender;
            document.getElementById('nextBtn').disabled = false;
        }
    </script>
</body>
</html> 