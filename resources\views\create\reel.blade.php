<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Reel - InstaPWA</title>
    <style>
        /* Similar styles to story.blade.php with reel-specific adjustments */
        .effects-panel {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .effect-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
        }

        .recording-indicator {
            position: absolute;
            top: 20px;
            left: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            color: red;
            font-weight: bold;
        }

        .recording-dot {
            width: 10px;
            height: 10px;
            background: red;
            border-radius: 50%;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            50% { opacity: 0; }
        }
    </style>
</head>
<body>
    <div class="header">
        <a href="{{ route('home') }}" class="back-btn">←</a>
        <div class="title">Create Reel</div>
        <button type="submit" form="reelForm" class="post-btn" id="postBtn" disabled>Next</button>
    </div>

    <div class="type-selector">
        <button class="type-btn">Story</button>
        <button class="type-btn">Post</button>
        <button class="type-btn active">Reel</button>
    </div>

    <div class="camera-container" id="cameraContainer">
        <video id="camera-preview" autoplay playsinline></video>
        <div class="recording-indicator" style="display: none;">
            <div class="recording-dot"></div>
            <span>Recording</span>
        </div>
        <div class="effects-panel">
            <button class="effect-btn">🎵</button>
            <button class="effect-btn">⚡</button>
            <button class="effect-btn">🎨</button>
            <button class="effect-btn">🔄</button>
        </div>
        <div class="camera-controls">
            <button class="control-btn" onclick="switchCamera()">⟲</button>
            <button class="control-btn capture-btn" onclick="toggleRecording()"></button>
            <button class="control-btn" onclick="document.getElementById('fileInput').click()">🖼️</button>
        </div>
    </div>

    <form id="reelForm" action="{{ route('reel.store') }}" method="POST" enctype="multipart/form-data" style="display: none;">
        @csrf
        <input type="file" id="fileInput" name="video" accept="video/*" onchange="handleFileSelect(this)">
        <textarea name="caption" placeholder="Write a caption..."></textarea>
    </form>

    <script>
        // Camera and recording functionality
        let mediaRecorder;
        let recordedChunks = [];
        let isRecording = false;

        async function toggleRecording() {
            if (!isRecording) {
                startRecording();
            } else {
                stopRecording();
            }
        }

        async function startRecording() {
            recordedChunks = [];
            const stream = document.getElementById('camera-preview').srcObject;
            mediaRecorder = new MediaRecorder(stream);
            
            mediaRecorder.ondataavailable = (e) => {
                if (e.data.size > 0) {
                    recordedChunks.push(e.data);
                }
            };

            mediaRecorder.onstop = () => {
                const blob = new Blob(recordedChunks, { type: 'video/mp4' });
                const file = new File([blob], "reel.mp4", { type: "video/mp4" });
                const dataTransfer = new DataTransfer();
                dataTransfer.items.add(file);
                document.getElementById('fileInput').files = dataTransfer.files;
                document.getElementById('postBtn').disabled = false;
            };

            mediaRecorder.start();
            isRecording = true;
            document.querySelector('.recording-indicator').style.display = 'flex';
        }

        function stopRecording() {
            mediaRecorder.stop();
            isRecording = false;
            document.querySelector('.recording-indicator').style.display = 'none';
        }

        // Initialize camera when page loads
        initCamera();
    </script>
</body>
</html> 