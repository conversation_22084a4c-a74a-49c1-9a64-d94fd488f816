// Livestream Real-time Features
import io from 'socket.io-client';

class LivestreamRealtime {
    constructor(streamId, isStreamer, iceServers) {
        this.streamId = streamId;
        this.isStreamer = isStreamer;
        this.iceServers = iceServers;
        this.peerConnections = new Map();
        this.localStream = null;
        this.mediaRecorder = null;
        this.recordedChunks = [];
        this.isRecording = false;
        this.socket = null;
        this.viewerCount = 0;
        this.heartAnimationQueue = [];
        this.isProcessingHearts = false;
        this.thumbnailBlob = null; // Store thumbnail for later use

        // Initialize socket connection
        this.initializeSocket();

        // Bind event handlers
        this.handleStreamStart = this.handleStreamStart.bind(this);
        this.handleStreamEnd = this.handleStreamEnd.bind(this);
        this.handleViewerCount = this.handleViewerCount.bind(this);
        this.handleError = this.handleError.bind(this);
    }

    initializeSocket() {
        // Get the signaling server URL from meta tag
        const signalingServer = document.querySelector('meta[name="signaling-server"]')?.content || 'https://instanode.quantumbith.com';
        this.socket = io(signalingServer, {
            transports: ['websocket'],
            upgrade: false
        });

        // Set up socket event listeners
        this.socket.on('connect', () => {
            console.log('Connected to signaling server with ID:', this.socket.id);

            // Join the stream room
            this.socket.emit('join-stream', {
                streamId: this.streamId,
                isStreamer: this.isStreamer
            });

            if (this.isStreamer) {
                this.startStream().catch(error => {
                    console.error('Error starting stream:', error);
                });
            }
        });

        this.socket.on('stream-started', (data) => {
            console.log('Stream started event received:', data);
            if (!this.isStreamer) {
                this.initializeViewerConnection().catch(error => {
                    console.error('Error initializing viewer connection:', error);
                });
            }
        });

        this.socket.on('stream-ended', () => {
            console.log('Stream ended');
            if (this.isStreamer) {
                this.endStream();
            } else {
                const peerConnection = this.peerConnections.get('broadcaster');
                if (peerConnection) {
                    peerConnection.close();
                    this.peerConnections.delete('broadcaster');
                }

                const remoteVideo = document.getElementById('remote-video');
                if (remoteVideo) {
                    remoteVideo.srcObject = null;
                }

                window.location.href = `/live/${this.streamId}/ended`;
            }
        });

        this.socket.on('signal', async ({ signal, senderId }) => {
            try {
                console.log('Received signal:', signal.type, 'from:', senderId);

                if (this.isStreamer) {
                    // Broadcaster handling viewer signals
                    let peerConnection = this.peerConnections.get(senderId);

                    if (!peerConnection && signal.type === 'offer') {
                        console.log('Creating new peer connection for viewer:', senderId);
                        peerConnection = await this.createPeerConnection(senderId);
                    }

                    if (peerConnection) {
                        if (signal.type === 'offer') {
                            console.log('Processing offer from viewer:', senderId);
                            await peerConnection.setRemoteDescription(new RTCSessionDescription(signal.sdp));
                            const answer = await peerConnection.createAnswer();
                            await peerConnection.setLocalDescription(answer);

                            this.socket.emit('signal', {
                                streamId: this.streamId,
                                viewerId: senderId,
                                signal: {
                                    type: 'answer',
                                    sdp: answer
                                }
                            });
                        } else if (signal.type === 'candidate' && signal.candidate) {
                            console.log('Adding ICE candidate from viewer:', senderId);
                            await peerConnection.addIceCandidate(new RTCIceCandidate(signal.candidate));
                        }
                    } else {
                        console.error('No peer connection found for viewer:', senderId);
                    }
                } else {
                    // Viewer handling broadcaster signals
                    const peerConnection = this.peerConnections.get('broadcaster');

                    if (peerConnection) {
                        if (signal.type === 'answer') {
                            console.log('Processing answer from broadcaster');
                            await peerConnection.setRemoteDescription(new RTCSessionDescription(signal.sdp));
                        } else if (signal.type === 'candidate' && signal.candidate) {
                            console.log('Adding ICE candidate from broadcaster');
                            await peerConnection.addIceCandidate(new RTCIceCandidate(signal.candidate));
                        }
                    } else {
                        console.error('No peer connection found for broadcaster');
                    }
                }
            } catch (error) {
                console.error('Error handling signal:', error);
            }
        });

        this.socket.on('viewer-count', ({ count }) => {
            this.viewerCount = count;
            const viewerCountElement = document.getElementById('viewerCount');
            if (viewerCountElement) {
                viewerCountElement.textContent = count;
            }
        });

        this.socket.on('disconnect', () => {
            console.log('Disconnected from signaling server');
        });

        this.socket.on('connect_error', (error) => {
            console.error('Socket connection error:', error);
        });

        // Listen for real-time chat messages
        this.socket.on('new-comment', (data) => {
            console.log('Received new-comment event:', data);
            this.addCommentToUI({
                username: data.username,
                comment: data.comment,
                userId: data.userId,
                timestamp: data.timestamp
            });
        });

        // Listen for heart reactions
        this.socket.on('heart-reaction', (data) => {
            console.log('Received heart reaction:', data);
            this.showHeartAnimation(data);
        });
    }

    addCommentToUI(comment) {
        console.log('addCommentToUI called with:', comment);

        // Try multiple ways to find the comments container
        let commentsList = document.getElementById('commentsList');

        if (!commentsList) {
            console.warn('commentsList not found, trying chat-messages...');
            commentsList = document.getElementById('chat-messages');
        }

        if (!commentsList) {
            console.error('No chat container found!');
            return;
        }

        const commentElement = document.createElement('div');
        commentElement.className = 'comment-item';
        commentElement.innerHTML = `
            <span class="comment-username">${comment.username || 'Anonymous'}</span>
            <span class="comment-text">${comment.comment}</span>
        `;

        commentsList.appendChild(commentElement);
        commentsList.scrollTop = commentsList.scrollHeight;

        console.log('Comment added to UI successfully');

        // Remove old comments to prevent memory issues
        while (commentsList.children.length > 50) {
            commentsList.removeChild(commentsList.firstChild);
        }

        // Add fade-in animation
        commentElement.style.opacity = '0';
        commentElement.style.transform = 'translateX(-20px)';

        requestAnimationFrame(() => {
            commentElement.style.transition = 'all 0.3s ease';
            commentElement.style.opacity = '1';
            commentElement.style.transform = 'translateX(0)';
        });
    }

    showHeartAnimation(data = null) {
        const heartsContainer = document.getElementById('heartsContainer');
        if (!heartsContainer) return;

        // Create heart element
        const heart = document.createElement('i');
        heart.className = 'fas fa-heart heart';
        heart.style.left = (Math.random() * (window.innerWidth - 50)) + 'px';
        heart.style.bottom = '20px';

        heartsContainer.appendChild(heart);

        // Remove heart after animation completes
        setTimeout(() => {
            if (heart.parentNode) {
                heart.parentNode.removeChild(heart);
            }
        }, 3000);
    }

    processHeartQueue() {
        if (this.heartAnimationQueue.length === 0) {
            this.isProcessingHearts = false;
            return;
        }

        this.isProcessingHearts = true;
        const heartData = this.heartAnimationQueue.shift();

        const heartsContainer = document.getElementById('heartsContainer');
        const heart = document.createElement('div');
        heart.className = 'floating-heart';

        // Random heart emojis
        const heartEmojis = ['❤️', '💖', '💕', '💗', '💓', '💝'];
        heart.innerHTML = heartEmojis[Math.floor(Math.random() * heartEmojis.length)];

        // Random position and size
        heart.style.left = Math.random() * 30 + 'px';
        heart.style.fontSize = (Math.random() * 15 + 25) + 'px';
        heart.style.animationDelay = Math.random() * 0.5 + 's';

        heartsContainer.appendChild(heart);

        // Remove heart after animation
        setTimeout(() => {
            if (heart.parentNode) {
                heart.remove();
            }
        }, 3000);

        // Process next heart after a short delay for smooth effect
        setTimeout(() => {
            this.processHeartQueue();
        }, 200);
    }

    updateViewerCount(count) {
        const viewerCountElement = document.getElementById('viewerCount');
        if (viewerCountElement) {
            // Animate the number change
            const currentCount = parseInt(viewerCountElement.textContent) || 0;
            this.animateCountChange(viewerCountElement, currentCount, count);
        }
    }

    animateCountChange(element, fromValue, toValue) {
        const duration = 300;
        const startTime = performance.now();

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            const currentValue = Math.round(fromValue + (toValue - fromValue) * progress);
            element.textContent = currentValue;

            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        requestAnimationFrame(animate);
    }

    handleStreamEnded() {
        // Show stream ended message
        const overlay = document.createElement('div');
        overlay.className = 'stream-ended-overlay';
        overlay.innerHTML = `
            <div class="stream-ended-message">
                <h2>This live stream has ended</h2>
                <button onclick="window.location.href='/home'" class="btn btn-primary">
                    Go Back Home
                </button>
            </div>
        `;

        document.body.appendChild(overlay);

        // Redirect after 5 seconds
        setTimeout(() => {
            window.location.href = '/home';
        }, 5000);
    }

    sendComment(comment, username, userId) {
        if (!comment.trim()) return;

        console.log('=== LIVESTREAM REALTIME SENDCOMMENT DEBUG ===');
        console.log('Parameters received:');
        console.log('  comment:', comment, '(type:', typeof comment, ')');
        console.log('  username:', username, '(type:', typeof username, ')');
        console.log('  userId:', userId, '(type:', typeof userId, ')');
        console.log('  streamId:', this.streamId, '(type:', typeof this.streamId, ')');

        const commentData = {
            streamId: this.streamId,
            comment: comment,
            username: username,
            userId: userId
        };

        console.log('Final commentData object:', commentData);
        console.log('About to emit send-comment event...');

        // Emit comment through Socket.IO instead of HTTP request
        this.socket.emit('send-comment', commentData);

        console.log('send-comment event emitted successfully');
    }

    sendHeart(username, userId) {
        // Emit heart reaction through Socket.IO
        this.socket.emit('send-heart', {
            streamId: this.streamId,
            username: username,
            userId: userId
        });

        // Show local heart animation immediately for better UX
        this.showHeartAnimation();
    }

    disconnect() {
        // No explicit leaveChannel for Socket.IO, just disconnect
        this.socket.disconnect();
    }

    async startStream() {
        try {
            console.log('Starting stream...');
            // Get user media
            this.localStream = await navigator.mediaDevices.getUserMedia({
                video: {
                    width: { ideal: 1280 },
                    height: { ideal: 720 },
                    frameRate: { ideal: 30 }
                },
                audio: true
            });

            // Display local stream
            const localVideo = document.getElementById('local-video');
            if (localVideo) {
                localVideo.srcObject = this.localStream;
                // Use safe video play with better error handling
                try {
                    await localVideo.play();
                    console.log('Local video started successfully');
                } catch (error) {
                    console.warn('Local video autoplay failed (this is normal):', error.message);
                    // Local video for streamers doesn't need user interaction usually
                    // as it's their own camera
                }
            }

            // Create thumbnail immediately after stream starts
            console.log('Creating initial thumbnail...');
            this.thumbnailBlob = await this.createThumbnail();
            if (this.thumbnailBlob) {
                console.log('Initial thumbnail created successfully');
            } else {
                console.warn('Failed to create initial thumbnail');
            }

            // Start recording
            this.startRecording();

        } catch (error) {
            console.error('Error starting stream:', error);
            throw error;
        }
    }

    async joinStream() {
        try {
            console.log('Joining stream as viewer...');
            await this.initializeViewerConnection();
        } catch (error) {
            console.error('Error joining stream:', error);
            throw error;
        }
    }

    async createPeerConnection(viewerId) {
        try {
            console.log('Creating peer connection for viewer:', viewerId);
            const peerConnection = new RTCPeerConnection({ iceServers: this.iceServers });

            // Add local stream tracks to peer connection
            if (this.localStream) {
                this.localStream.getTracks().forEach(track => {
                    console.log('Adding track to peer connection:', track.kind);
                    peerConnection.addTrack(track, this.localStream);
                });
            }

            // Handle ICE candidates
            peerConnection.onicecandidate = (event) => {
                if (event.candidate) {
                    console.log('Sending ICE candidate to viewer:', viewerId);
                    this.socket.emit('signal', {
                        streamId: this.streamId,
                        viewerId: viewerId,
                        signal: {
                            type: 'candidate',
                            candidate: event.candidate
                        }
                    });
                }
            };

            peerConnection.oniceconnectionstatechange = () => {
                console.log(`ICE Connection State for ${viewerId}:`, peerConnection.iceConnectionState);
            };

            // Store the peer connection
            this.peerConnections.set(viewerId, peerConnection);
            console.log('Peer connection created and stored for viewer:', viewerId);

            return peerConnection;
        } catch (error) {
            console.error('Error creating peer connection:', error);
            throw error;
        }
    }

    getSupportedMimeType() {
        const types = [
            'video/webm;codecs=vp9,opus',
            'video/webm;codecs=vp8,opus',
            'video/webm;codecs=h264,opus',
            'video/webm'
        ];

        for (const type of types) {
            if (MediaRecorder.isTypeSupported(type)) {
                console.log('Using MIME type:', type);
                return type;
            }
        }

        throw new Error('No supported MIME type found for recording');
    }

    startRecording() {
        try {
            // Define optimal recording options
            const options = {
                // mimeType: 'video/webm;codecs=h264,opus',
                videoBitsPerSecond: 2500000, // 2.5 Mbps for good quality
                audioBitsPerSecond: 128000   // 128 kbps for clear audio
            };

            // Fallback options if h264 is not supported
            // if (!MediaRecorder.isTypeSupported(options.mimeType)) {
            //     options.mimeType = 'video/webm;codecs=vp9,opus';
            // }
            // if (!MediaRecorder.isTypeSupported(options.mimeType)) {
            //     options.mimeType = 'video/webm;codecs=vp8,opus';
            // }
            // if (!MediaRecorder.isTypeSupported(options.mimeType)) {
            //     options.mimeType = 'video/webm';
            // }
            // if (!MediaRecorder.isTypeSupported(options.mimeType)) {
            //     options = {};
            // }

            console.log('Starting recording with options:', options);
            this.mediaRecorder = new MediaRecorder(this.localStream, options);
            this.recordedChunks = [];

            // Handle data available event
            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data && event.data.size > 0) {
                    // console.log('Received chunk:', {
                    //     size: event.data.size,
                    //     type: event.data.type,
                    //     timestamp: new Date().toISOString()
                    // });
                    this.recordedChunks.push(event.data);
                }
            };

            this.mediaRecorder.onerror = (error) => {
                console.error('MediaRecorder error:', error);
            };

            this.mediaRecorder.onstart = () => {
                console.log('MediaRecorder started at:', new Date().toISOString());
            };

            this.mediaRecorder.onstop = () => {
                console.log('MediaRecorder stopped. Total chunks:', this.recordedChunks.length);
                this.uploadRecording();
            };

            // Request data every 2 seconds - more frequent chunks for better reliability
            this.mediaRecorder.start(2000);
            this.isRecording = true;

        } catch (error) {
            console.error('Error starting recording:', error);
            throw error;
        }
    }

    async uploadRecording() {
        try {
            console.log('Starting recording upload...');

            if (!this.recordedChunks.length) {
                throw new Error('No recorded chunks available');
            }

            // Verify and sort chunks by timestamp if available
            const validChunks = this.recordedChunks.filter(chunk => chunk && chunk.size > 0);
            console.log(`Found ${validChunks.length} valid chunks out of ${this.recordedChunks.length} total chunks`);

            if (!validChunks.length) {
                throw new Error('No valid recorded chunks found');
            }

            // Calculate total size for logging
            const totalSize = validChunks.reduce((acc, chunk) => acc + chunk.size, 0);
            console.log('Total recording size:', totalSize, 'bytes');

            // Create blob with specific type and quality settings
            const mimeType = this.mediaRecorder ? this.mediaRecorder.mimeType : 'video/webm';
            const blob = new Blob(validChunks, {
                type: mimeType
            });

            console.log('Created recording blob:', {
                size: blob.size,
                type: blob.type,
                chunks: validChunks.length,
                averageChunkSize: Math.round(blob.size / validChunks.length)
            });

            if (blob.size === 0) {
                throw new Error('Generated blob is empty');
            }

            if (blob.size < 1000) { // Less than 1KB
                throw new Error('Generated blob is suspiciously small');
            }

            const formData = new FormData();
            formData.append('recording', blob, `stream-${Date.now()}.webm`);

            // Add metadata to help with debugging
            formData.append('metadata', JSON.stringify({
                totalChunks: this.recordedChunks.length,
                validChunks: validChunks.length,
                mimeType: mimeType,
                totalSize: blob.size,
                timestamp: new Date().toISOString()
            }));

            // Use stored thumbnail if available
            if (this.thumbnailBlob) {
                console.log('Using stored thumbnail for upload');
                formData.append('thumbnail', this.thumbnailBlob, `thumbnail-${Date.now()}.jpg`);
            } else {
                console.log('No stored thumbnail found, creating new one...');
                const thumbnail = await this.createThumbnail();
                if (thumbnail) {
                    console.log('Fallback thumbnail created successfully');
                    formData.append('thumbnail', thumbnail, `thumbnail-${Date.now()}.jpg`);
                }
            }

            // Get CSRF token
            const csrfToken = document.querySelector('meta[name="csrf-token"]').content;
            if (!csrfToken) {
                throw new Error('CSRF token not found');
            }

            console.log('Starting upload of recording and thumbnail...');
            const response = await fetch(`/live/upload/${this.streamId}`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': csrfToken
                },
                body: formData
            });

            if (!response.ok) {
                throw new Error(`Upload failed with status: ${response.status}`);
            }

            const result = await response.json();
            if (!result.success) {
                throw new Error(result.message || 'Failed to upload recording');
            }

            console.log('Upload successful:', result);
            return result;

        } catch (error) {
            console.error('Error in uploadRecording:', error);
            throw error;
        }
    }

    async createThumbnail() {
        try {
            console.log('Starting thumbnail creation...');
            // Create a video element
            const video = document.createElement('video');
            video.autoplay = true;
            video.muted = true;

            // If we have a local stream, use it
            if (this.localStream) {
                console.log('Using localStream for thumbnail');
                video.srcObject = this.localStream;
            } else {
                // Try to get the video element from the page
                const localVideo = document.getElementById('local-video');
                if (localVideo && localVideo.srcObject) {
                    console.log('Using local-video element stream for thumbnail');
                    video.srcObject = localVideo.srcObject;
                } else {
                    console.warn('No video stream available for thumbnail');
                    return null;
                }
            }

            console.log('Waiting for video to be ready...');
            // Wait for video to be ready
            await new Promise((resolve) => {
                video.addEventListener('loadeddata', () => {
                    console.log('Video data loaded');
                    resolve();
                });
            });

            // Play the video (needed for some browsers) - this is for thumbnail capture
            try {
                await video.play();
                console.log('Thumbnail video playback started');
            } catch (error) {
                console.warn('Thumbnail video autoplay failed (expected):', error.message);
                // Continue anyway as we might still be able to capture the frame
                // This is for thumbnail creation, not viewing
            }

            // Small delay to ensure frame is rendered
            await new Promise(resolve => setTimeout(resolve, 100));

            // Create canvas and draw the video frame
            const canvas = document.createElement('canvas');
            canvas.width = 1280;  // HD width
            canvas.height = 720;  // HD height

            const ctx = canvas.getContext('2d');
            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
            console.log('Frame captured to canvas');

            // Stop video playback and clean up
            video.pause();
            video.srcObject = null;

            // Convert to blob
            return new Promise((resolve) => {
                canvas.toBlob((blob) => {
                    if (blob) {
                        console.log('Thumbnail created successfully:', {
                            size: blob.size,
                            type: blob.type
                        });
                    } else {
                        console.error('Failed to create thumbnail blob');
                    }
                    resolve(blob);
                }, 'image/jpeg', 0.85); // Higher quality JPEG
            });

        } catch (error) {
            console.error('Error creating thumbnail:', error);
            return null;
        }
    }

    async endStream() {
        try {
            console.log('Ending stream... inside');
            // Show uploading overlay immediately
            this.showUploadingOverlay();

            // Stop recording
            if (this.mediaRecorder && this.isRecording) {
                console.log('Stopping media recorder...');
                this.mediaRecorder.stop();
                this.isRecording = false;
            }

            // Stop local stream
            if (this.localStream) {
                console.log('Stopping local stream tracks...');
                this.localStream.getTracks().forEach(track => track.stop());
            }

            // Close all peer connections
            this.peerConnections.forEach(connection => {
                connection.close();
            });
            this.peerConnections.clear();

            // If we have recorded chunks, upload them
            if (this.recordedChunks.length > 0) {
                console.log('Starting recording upload process...');
                try {
                    const uploadResult = await this.uploadRecording();
                    console.log('Upload completed, waiting for processing...', uploadResult);

                    // Start polling for recording status
                    let attempts = 0;
                    const maxAttempts = 30; // 60 seconds total (2s * 30)

                    const checkStatus = async () => {
                        if (attempts >= maxAttempts) {
                            console.error('Timeout waiting for recording processing');
                            this.updateUploadingOverlay('Recording upload timed out. Please try again.');
                            return;
                        }

                        try {
                            const response = await fetch(`/live/${this.streamId}/status`, {
                                headers: {
                                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                                }
                            });
                            const data = await response.json();

                            if (data.success && data.has_recording && data.recording_path) {
                                console.log('Recording processed successfully:', data);
                                // Update overlay to success state
                                this.updateUploadingOverlay('Recording saved successfully!', true);

                                // Notify server that stream is fully ended
                                this.socket.emit('stream-stop', this.streamId);
                                this.socket.disconnect();

                                // Redirect after showing success message
                                setTimeout(() => {
                                    window.location.href = `/live/${this.streamId}/ended`;
                                }, 2000);
                            } else {
                                console.log('Recording still processing, checking again in 2s...');
                                attempts++;
                                setTimeout(checkStatus, 2000);
                            }
                        } catch (error) {
                            console.error('Error checking recording status:', error);
                            attempts++;
                            setTimeout(checkStatus, 2000);
                        }
                    };

                    // Start checking status
                    await checkStatus();
                } catch (error) {
                    console.error('Error during recording upload:', error);
                    this.updateUploadingOverlay('Error uploading recording. Please try again.');

                    // Still notify server even if upload fails
                    this.socket.emit('stream-stop', this.streamId);
                    this.socket.disconnect();
                }
            } else {
                console.log('No recording chunks to upload');
                // No recording to upload, just end the stream
                this.socket.emit('stream-stop', this.streamId);
                this.socket.disconnect();
                window.location.href = `/live/${this.streamId}/ended`;
            }

        } catch (error) {
            console.error('Error ending stream:', error);
            this.updateUploadingOverlay('Error ending stream. Please try again.');
            throw error;
        }
    }

    showUploadingOverlay() {
        const overlay = document.createElement('div');
        overlay.id = 'uploadingOverlay';
        overlay.style.position = 'fixed';
        overlay.style.top = '0';
        overlay.style.left = '0';
        overlay.style.width = '100%';
        overlay.style.height = '100%';
        overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
        overlay.style.display = 'flex';
        overlay.style.justifyContent = 'center';
        overlay.style.alignItems = 'center';
        overlay.style.zIndex = '9999';

        const messageBox = document.createElement('div');
        messageBox.id = 'uploadMessageBox';
        messageBox.style.textAlign = 'center';
        messageBox.style.color = 'white';
        messageBox.style.padding = '2rem';
        messageBox.style.borderRadius = '1rem';
        messageBox.style.maxWidth = '90%';
        messageBox.style.animation = 'fadeIn 0.5s ease';

        // Create loading spinner
        const spinner = document.createElement('div');
        spinner.style.border = '4px solid #f3f3f3';
        spinner.style.borderTop = '4px solid #3498db';
        spinner.style.borderRadius = '50%';
        spinner.style.width = '40px';
        spinner.style.height = '40px';
        spinner.style.animation = 'spin 1s linear infinite';
        spinner.style.margin = '0 auto 1rem auto';

        // Add keyframes for animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(-20px); }
                to { opacity: 1; transform: translateY(0); }
            }
        `;
        document.head.appendChild(style);

        messageBox.appendChild(spinner);
        messageBox.innerHTML += `
            <h2 style="font-size: 2rem; margin-bottom: 1rem;">Saving Your Stream</h2>
            <p style="font-size: 1.2rem; margin-bottom: 1rem;">Please wait while we process your recording...</p>
            <p style="font-size: 1rem; color: #999;">This may take a few moments</p>
            <div id="uploadProgress" style="margin-top: 1rem; font-size: 1rem; color: #3498db;"></div>
        `;

        overlay.appendChild(messageBox);
        document.body.appendChild(overlay);
    }

    updateUploadingOverlay(message, isSuccess = false) {
        const messageBox = document.getElementById('uploadMessageBox');
        if (messageBox) {
            if (isSuccess) {
                messageBox.innerHTML = `
                    <h2 style="font-size: 2rem; margin-bottom: 1rem;">Stream Ended Successfully</h2>
                    <p style="font-size: 1.2rem; margin-bottom: 2rem;">${message}</p>
                    <div style="margin-top: 1rem;">
                        <button onclick="window.location.href='/home'"
                                style="background-color: #3498db; color: white; border: none;
                                       padding: 0.8rem 2rem; border-radius: 0.5rem; cursor: pointer;
                                       font-size: 1rem; transition: background-color 0.3s ease">
                            Return Home
                        </button>
                    </div>
                `;
            } else {
                const progress = document.getElementById('uploadProgress');
                if (progress) {
                    progress.textContent = message;
                }
            }
        }
    }

    handleStreamStart(data) {
        console.log('Stream started event received:', data);
        if (!this.isStreamer) {
            // Only viewers should try to join the stream
            this.initializeViewerConnection().catch(error => {
                console.error('Error joining stream:', error);
            });
        }
    }

    handleStreamEnd() {
        console.log('Stream ended');
        if (this.isStreamer) {
            this.endStream();
        } else {
            // Close peer connection if it exists
            const peerConnection = this.peerConnections.get('broadcaster');
            if (peerConnection) {
                peerConnection.close();
                this.peerConnections.delete('broadcaster');
            }

            // Clear video element
            const remoteVideo = document.getElementById('remote-video');
            if (remoteVideo) {
                remoteVideo.srcObject = null;
            }

            // Show loading overlay while waiting for upload
            const overlay = document.createElement('div');
            overlay.style.position = 'fixed';
            overlay.style.top = '0';
            overlay.style.left = '0';
            overlay.style.width = '100%';
            overlay.style.height = '100%';
            overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
            overlay.style.display = 'flex';
            overlay.style.justifyContent = 'center';
            overlay.style.alignItems = 'center';
            overlay.style.zIndex = '9999';
            overlay.style.transition = 'opacity 0.5s ease';

            const messageBox = document.createElement('div');
            messageBox.style.textAlign = 'center';
            messageBox.style.color = 'white';
            messageBox.style.padding = '2rem';
            messageBox.style.borderRadius = '1rem';
            messageBox.style.maxWidth = '90%';
            messageBox.style.animation = 'fadeIn 0.5s ease';

            // Create loading spinner
            const spinner = document.createElement('div');
            spinner.style.border = '4px solid #f3f3f3';
            spinner.style.borderTop = '4px solid #3498db';
            spinner.style.borderRadius = '50%';
            spinner.style.width = '40px';
            spinner.style.height = '40px';
            spinner.style.animation = 'spin 1s linear infinite';
            spinner.style.margin = '0 auto 1rem auto';

            // Add keyframes for spinner
            const spinnerStyle = document.createElement('style');
            spinnerStyle.textContent = `
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
                @keyframes fadeIn {
                    from { opacity: 0; transform: translateY(-20px); }
                    to { opacity: 1; transform: translateY(0); }
                }
            `;
            document.head.appendChild(spinnerStyle);

            messageBox.appendChild(spinner);
            messageBox.innerHTML += `
                <h2 style="font-size: 2rem; margin-bottom: 1rem;">Stream Has Ended</h2>
                <p style="font-size: 1.2rem; margin-bottom: 1rem;">Please wait while we save the recording...</p>
                <p style="font-size: 1rem; color: #999;">This may take a few moments</p>
            `;

            overlay.appendChild(messageBox);
            document.body.appendChild(overlay);

            // Poll for recording status
            const checkRecordingStatus = async () => {
                try {
                    const response = await fetch(`/live/${this.streamId}/status`, {
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                        }
                    });
                    const data = await response.json();

                    if (data.has_recording) {
                        // Recording is ready, update message and redirect
                        messageBox.innerHTML = `
                            <h2 style="font-size: 2rem; margin-bottom: 1rem;">Stream Has Ended</h2>
                            <p style="font-size: 1.2rem; margin-bottom: 2rem;">Thank you for staying with us!</p>
                            <div style="margin-top: 1rem;">
                                <button onclick="window.location.href='/home'"
                                        style="background-color: #3498db; color: white; border: none;
                                               padding: 0.8rem 2rem; border-radius: 0.5rem; cursor: pointer;
                                               font-size: 1rem; transition: background-color 0.3s ease">
                                    Return Home
                                </button>
                            </div>
                        `;

                        // Redirect after showing success message
                        setTimeout(() => {
                            overlay.style.opacity = '0';
                            setTimeout(() => {
                                window.location.href = `/live/${this.streamId}/ended`;
                            }, 500);
                        }, 2000);
                    } else {
                        // Check again in 2 seconds
                        setTimeout(checkRecordingStatus, 2000);
                    }
                } catch (error) {
                    console.error('Error checking recording status:', error);
                    // Still check again in case of temporary error
                    setTimeout(checkRecordingStatus, 2000);
                }
            };

            // Start checking recording status
            checkRecordingStatus();
        }
    }

    async initializeViewerConnection() {
        try {
            console.log('Initializing viewer connection...');
            const peerConnection = new RTCPeerConnection({ iceServers: this.iceServers });

            // Set up event handlers for the peer connection
            peerConnection.ontrack = (event) => {
                console.log('Received remote track:', event.track.kind);
                const remoteVideo = document.getElementById('remote-video');
                if (remoteVideo && event.streams[0]) {
                    console.log('Setting remote stream');
                    remoteVideo.srcObject = event.streams[0];
                    // Use safe video play
                    try {
                        remoteVideo.play().then(() => {
                            console.log('Remote video started successfully');
                        }).catch(error => {
                            console.warn('Remote video autoplay failed:', error.message);
                            console.log('Click unmute button to start video');
                        });
                    } catch (error) {
                        console.error('Error setting up remote video:', error);
                    }
                }
            };

            peerConnection.onicecandidate = (event) => {
                if (event.candidate) {
                    console.log('Sending ICE candidate to broadcaster');
                    this.socket.emit('signal', {
                        streamId: this.streamId,
                        signal: {
                            type: 'candidate',
                            candidate: event.candidate
                        }
                    });
                }
            };

            peerConnection.oniceconnectionstatechange = () => {
                console.log('ICE Connection State:', peerConnection.iceConnectionState);
            };

            // Store the peer connection
            this.peerConnections.set('broadcaster', peerConnection);

            // Create and send offer
            const offer = await peerConnection.createOffer({
                offerToReceiveAudio: true,
                offerToReceiveVideo: true
            });
            await peerConnection.setLocalDescription(offer);

            console.log('Sending offer to broadcaster');
            this.socket.emit('signal', {
                streamId: this.streamId,
                signal: {
                    type: 'offer',
                    sdp: offer
                }
            });

        } catch (error) {
            console.error('Error initializing viewer connection:', error);
            throw error;
        }
    }

    handleViewerCount({ count }) {
        this.viewerCount = count;
        const viewerCountElement = document.getElementById('viewer-count');
        if (viewerCountElement) {
            viewerCountElement.textContent = count;
        }
    }

    handleError(error) {
        console.error('Socket error:', error);
    }
}

// Advanced Features for Production

class LivestreamAnalytics {
    constructor(streamId) {
        this.streamId = streamId;
        this.startTime = Date.now();
        this.events = [];
    }

    trackEvent(eventType, data = {}) {
        this.events.push({
            type: eventType,
            timestamp: Date.now(),
            data: data
        });

        // Send analytics every 30 seconds
        if (this.events.length >= 10) {
            this.sendAnalytics();
        }
    }

    sendAnalytics() {
        if (this.events.length === 0) return;

        fetch(`/live/${this.streamId}/analytics`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                events: this.events,
                session_duration: Date.now() - this.startTime
            })
        });

        this.events = [];
    }
}

class AdaptiveQuality {
    constructor(videoElement) {
        this.video = videoElement;
        this.qualities = ['1080p', '720p', '480p', '360p'];
        this.currentQuality = '720p';

        this.initializeQualitySelection();
    }

    initializeQualitySelection() {
        // Monitor connection quality
        if ('connection' in navigator) {
            navigator.connection.addEventListener('change', () => {
                this.adjustQuality();
            });
        }

        // Create quality selector UI
        this.createQualitySelector();
    }

    adjustQuality() {
        const connection = navigator.connection;
        let recommendedQuality = '720p';

        if (connection) {
            if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
                recommendedQuality = '360p';
            } else if (connection.effectiveType === '3g') {
                recommendedQuality = '480p';
            } else if (connection.effectiveType === '4g') {
                recommendedQuality = connection.downlink > 10 ? '1080p' : '720p';
            }
        }

        this.setQuality(recommendedQuality);
    }

    createQualitySelector() {
        const selector = document.createElement('div');
        selector.className = 'quality-selector';
        selector.innerHTML = `
            <button class="quality-btn" title="Video Quality">
                <i class="fas fa-cog"></i>
                <span class="quality-text">${this.currentQuality}</span>
            </button>
            <div class="quality-dropdown">
                ${this.qualities.map(quality => `
                    <div class="quality-option ${quality === this.currentQuality ? 'active' : ''}"
                         data-quality="${quality}">
                        ${quality}
                    </div>
                `).join('')}
            </div>
        `;

        // Add event listeners
        selector.querySelectorAll('.quality-option').forEach(option => {
            option.addEventListener('click', (e) => {
                this.setQuality(e.target.dataset.quality);
            });
        });

        document.querySelector('.video-container').appendChild(selector);
    }

    setQuality(quality) {
        this.currentQuality = quality;

        // Update UI
        const qualityText = document.querySelector('.quality-text');
        if (qualityText) {
            qualityText.textContent = quality;
        }

        // Update active option
        document.querySelectorAll('.quality-option').forEach(option => {
            option.classList.toggle('active', option.dataset.quality === quality);
        });

        // In a real implementation, you would switch video sources here
        console.log(`Quality changed to ${quality}`);
    }
}

// Export for global use
window.LivestreamRealtime = LivestreamRealtime;
window.LivestreamAnalytics = LivestreamAnalytics;
window.AdaptiveQuality = AdaptiveQuality;
