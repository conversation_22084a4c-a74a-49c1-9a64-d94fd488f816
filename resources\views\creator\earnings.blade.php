@extends('layouts.app')

@section('content')
<div class="earnings-container">
    <div class="earnings-header">
        <h1>Creator Earnings</h1>
        <div class="balance-display">
            <span>Available Balance:</span>
            <span class="balance-amount">${{ number_format(Auth::user()->creator_balance, 2) }}</span>
        </div>
    </div>
    
    @if(session('success'))
    <div class="alert alert-success">
        {{ session('success') }}
    </div>
    @endif
    
    @if(session('error'))
    <div class="alert alert-error">
        {{ session('error') }}
    </div>
    @endif
    
    <div class="withdrawal-section">
        <h2>Withdraw Earnings</h2>
        <p>Minimum withdrawal amount: $10.00</p>
        
        <form action="{{ route('creator.withdraw') }}" method="POST" class="withdrawal-form">
            @csrf
            <div class="form-group">
                <label for="amount">Amount to withdraw ($)</label>
                <input type="number" id="amount" name="amount" min="10" max="{{ Auth::user()->creator_balance }}" step="0.01" required>
            </div>
            
            <div class="form-group">
                <label for="payment_method">Payment Method</label>
                <select id="payment_method" name="payment_method" required>
                    <option value="bank">Bank Transfer</option>
                    <option value="paypal">PayPal</option>
                    <option value="bkash">bKash</option>
                </select>
            </div>
            
            <div class="form-group payment-details" id="bank-details">
                <label for="bank_account">Bank Account Number</label>
                <input type="text" id="bank_account" name="bank_account">
            </div>
            
            <div class="form-group payment-details hidden" id="paypal-details">
                <label for="paypal_email">PayPal Email</label>
                <input type="email" id="paypal_email" name="paypal_email">
            </div>
            
            <div class="form-group payment-details hidden" id="bkash-details">
                <label for="bkash_number">bKash Number</label>
                <input type="text" id="bkash_number" name="bkash_number">
            </div>
            
            <button type="submit" class="withdraw-btn">Request Withdrawal</button>
        </form>
    </div>
    
    <div class="earnings-history">
        <h2>Earnings History</h2>
        
        <div class="earnings-table">
            <div class="table-header">
                <div class="header-cell">Date</div>
                <div class="header-cell">Source</div>
                <div class="header-cell">Amount</div>
                <div class="header-cell">Status</div>
            </div>
            
            @foreach($earnings as $earning)
            <div class="table-row">
                <div class="table-cell">{{ $earning->earned_at->format('M d, Y') }}</div>
                <div class="table-cell">{{ ucfirst($earning->source) }}</div>
                <div class="table-cell">${{ number_format($earning->amount, 2) }}</div>
                <div class="table-cell">
                    <span class="status-badge {{ $earning->status }}">{{ ucfirst($earning->status) }}</span>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</div>

<style>
/* Styles for the earnings page */
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const paymentMethodSelect = document.getElementById('payment_method');
    const paymentDetails = document.querySelectorAll('.payment-details');
    
    paymentMethodSelect.addEventListener('change', function() {
        // Hide all payment details sections
        paymentDetails.forEach(section => {
            section.classList.add('hidden');
        });
        
        // Show the selected payment method details
        const selectedMethod = this.value;
        document.getElementById(`${selectedMethod}-details`).classList.remove('hidden');
    });
});
</script>
@endsection 