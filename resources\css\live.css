.stream-card {
    transition: all 0.3s ease;
    background: #1a1a1a;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.stream-card:hover {
    box-shadow: 0 4px 15px rgba(255, 0, 0, 0.2);
}

.live-badge {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.upcoming-stream {
    padding: 1rem;
    background: #2d2d2d;
    border-radius: 6px;
    margin-bottom: 1rem;
}

.upcoming-stream h6 {
    color: #fff;
    font-weight: 500;
}

.stream-preview {
    transition: transform 0.3s ease;
}

.stream-preview:hover {
    transform: scale(1.02);
} 