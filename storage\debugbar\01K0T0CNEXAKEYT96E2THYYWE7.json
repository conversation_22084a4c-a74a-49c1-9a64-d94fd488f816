{"__meta": {"id": "01K0T0CNEXAKEYT96E2THYYWE7", "datetime": "2025-07-22 21:24:47", "utime": **********.197809, "method": "GET", "uri": "/home", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.833974, "end": **********.197821, "duration": 0.363847017288208, "duration_str": "364ms", "measures": [{"label": "Booting", "start": **********.833974, "relative_start": 0, "end": **********.955192, "relative_end": **********.955192, "duration": 0.*****************, "duration_str": "121ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.9552, "relative_start": 0.*****************, "end": **********.197822, "relative_end": 1.1920928955078125e-06, "duration": 0.*****************, "duration_str": "243ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.969637, "relative_start": 0.*****************, "end": **********.971583, "relative_end": **********.971583, "duration": 0.0019459724426269531, "duration_str": "1.95ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.04172, "relative_start": 0.*****************, "end": **********.196292, "relative_end": **********.196292, "duration": 0.****************, "duration_str": "155ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: home", "start": **********.043447, "relative_start": 0.****************, "end": **********.043447, "relative_end": **********.043447, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.058496, "relative_start": 0.*****************, "end": **********.058496, "relative_end": **********.058496, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.065436, "relative_start": 0.2314620018005371, "end": **********.065436, "relative_end": **********.065436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.072328, "relative_start": 0.23835420608520508, "end": **********.072328, "relative_end": **********.072328, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.078872, "relative_start": 0.24489808082580566, "end": **********.078872, "relative_end": **********.078872, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.085522, "relative_start": 0.25154805183410645, "end": **********.085522, "relative_end": **********.085522, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.092098, "relative_start": 0.25812411308288574, "end": **********.092098, "relative_end": **********.092098, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.098442, "relative_start": 0.2644681930541992, "end": **********.098442, "relative_end": **********.098442, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.105886, "relative_start": 0.2719120979309082, "end": **********.105886, "relative_end": **********.105886, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.112713, "relative_start": 0.27873921394348145, "end": **********.112713, "relative_end": **********.112713, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.119033, "relative_start": 0.28505921363830566, "end": **********.119033, "relative_end": **********.119033, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.124718, "relative_start": 0.2907440662384033, "end": **********.124718, "relative_end": **********.124718, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.130375, "relative_start": 0.2964010238647461, "end": **********.130375, "relative_end": **********.130375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.138007, "relative_start": 0.3040330410003662, "end": **********.138007, "relative_end": **********.138007, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.143861, "relative_start": 0.309887170791626, "end": **********.143861, "relative_end": **********.143861, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.150298, "relative_start": 0.31632423400878906, "end": **********.150298, "relative_end": **********.150298, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.157004, "relative_start": 0.3230302333831787, "end": **********.157004, "relative_end": **********.157004, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.16315, "relative_start": 0.3291761875152588, "end": **********.16315, "relative_end": **********.16315, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.16984, "relative_start": 0.33586621284484863, "end": **********.16984, "relative_end": **********.16984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.175919, "relative_start": 0.34194517135620117, "end": **********.175919, "relative_end": **********.175919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.love-button", "start": **********.181789, "relative_start": 0.34781503677368164, "end": **********.181789, "relative_end": **********.181789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.app", "start": **********.188741, "relative_start": 0.3547670841217041, "end": **********.188741, "relative_end": **********.188741, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 26991552, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 22, "nb_templates": 22, "templates": [{"name": "home", "param_count": null, "params": [], "start": **********.043405, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.phphome", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=1", "ajax": false, "filename": "home.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.058457, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.065398, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.072278, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.078827, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.085484, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.092061, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.098404, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.105849, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.112641, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.118995, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.124655, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.130338, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.13796, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.143825, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.15026, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.156967, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.163113, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.169802, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.175882, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "components.love-button", "param_count": null, "params": [], "start": **********.181745, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.phpcomponents.love-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fcomponents%2Flove-button.blade.php&line=1", "ajax": false, "filename": "love-button.blade.php", "line": "?"}}, {"name": "layouts.app", "param_count": null, "params": [], "start": **********.188702, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\instapwa\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}]}, "queries": {"count": 75, "nb_statements": 75, "nb_visible_statements": 75, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.04314, "accumulated_duration_str": "43.14ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'ysQ2OZdBk91GzyxsvUNjStsqoUKCd3cWniDGhfUE' limit 1", "type": "query", "params": [], "bindings": ["ysQ2OZdBk91GzyxsvUNjStsqoUKCd3cWniDGhfUE"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.9808578, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "instapwa", "explain": null, "start_percent": 0, "width_percent": 3.802}, {"sql": "select * from `users` where `id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.99666, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "instapwa", "explain": null, "start_percent": 3.802, "width_percent": 1.089}, {"sql": "select * from `live_streams` where `is_live` = 1 and `visibility` = 'public' order by `started_at` desc limit 10", "type": "query", "params": [], "bindings": [1, "public"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 21}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.001793, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:21", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=21", "ajax": false, "filename": "HomeController.php", "line": "21"}, "connection": "instapwa", "explain": null, "start_percent": 4.891, "width_percent": 1.599}, {"sql": "select * from `posts` where `user_id` in (select `followed_id` from `followers` where `follower_id` = 28 and `approved` = 1) or `user_id` = 28 or `visibility` = 'public' order by `created_at` desc limit 20", "type": "query", "params": [], "bindings": [28, 1, 28, "public"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.0052128, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:35", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=35", "ajax": false, "filename": "HomeController.php", "line": "35"}, "connection": "instapwa", "explain": null, "start_percent": 6.49, "width_percent": 1.808}, {"sql": "select * from `users` where `users`.`id` in (1, 3, 4, 6, 7, 8, 27)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.00942, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:35", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=35", "ajax": false, "filename": "HomeController.php", "line": "35"}, "connection": "instapwa", "explain": null, "start_percent": 8.299, "width_percent": 1.298}, {"sql": "select * from `user_profiles` where `user_profiles`.`user_id` in (1, 3, 4, 6, 7, 8, 27)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.013392, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:35", "source": {"index": 25, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=35", "ajax": false, "filename": "HomeController.php", "line": "35"}, "connection": "instapwa", "explain": null, "start_percent": 9.597, "width_percent": 1.391}, {"sql": "select * from `love` where `love`.`post_id` in (28, 29, 30, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.016009, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:35", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=35", "ajax": false, "filename": "HomeController.php", "line": "35"}, "connection": "instapwa", "explain": null, "start_percent": 10.987, "width_percent": 1.159}, {"sql": "select * from `users` where `id` != 28 and `id` not in (select `followed_id` from `followers` where `follower_id` = 28) order by RAND() limit 5", "type": "query", "params": [], "bindings": [28, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 47}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.018063, "duration": 0.009470000000000001, "duration_str": "9.47ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "instapwa", "explain": null, "start_percent": 12.146, "width_percent": 21.952}, {"sql": "select * from `user_profiles` where `user_profiles`.`user_id` in (10, 11, 17, 21, 23)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.0295131, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:47", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=47", "ajax": false, "filename": "HomeController.php", "line": "47"}, "connection": "instapwa", "explain": null, "start_percent": 34.098, "width_percent": 1.576}, {"sql": "select * from `live_streams` where `has_recording` = 1 and `recording_path` is not null and `visibility` = 'public' and `ended_at` > '2025-07-21 21:24:47' order by `ended_at` desc limit 8", "type": "query", "params": [], "bindings": [1, "public", "2025-07-21 21:24:47"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 57}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.0318608, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:57", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=57", "ajax": false, "filename": "HomeController.php", "line": "57"}, "connection": "instapwa", "explain": null, "start_percent": 35.675, "width_percent": 1.368}, {"sql": "select * from `users` where `users`.`id` in (27)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 57}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.034488, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:57", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Http\\Controllers\\HomeController.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=57", "ajax": false, "filename": "HomeController.php", "line": "57"}, "connection": "instapwa", "explain": null, "start_percent": 37.042, "width_percent": 1.53}, {"sql": "select * from `user_profiles` where `user_profiles`.`user_id` = 28 and `user_profiles`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 508}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.044132, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "home:508", "source": {"index": 21, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 508}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=508", "ajax": false, "filename": "home.blade.php", "line": "508"}, "connection": "instapwa", "explain": null, "start_percent": 38.572, "width_percent": 1.159}, {"sql": "select * from `user_profiles` where `user_profiles`.`user_id` = 27 and `user_profiles`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 566}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.055443, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "home:566", "source": {"index": 21, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 566}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=566", "ajax": false, "filename": "home.blade.php", "line": "566"}, "connection": "instapwa", "explain": null, "start_percent": 39.731, "width_percent": 1.182}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 70 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [70, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.05892, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 40.913, "width_percent": 0.927}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 70 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [70, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.060873, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 41.841, "width_percent": 0.95}, {"sql": "select * from `comments` where `comments`.`post_id` = 70 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [70], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.0632899, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 42.791, "width_percent": 1.298}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 69 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [69, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.0657601, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 44.089, "width_percent": 1.066}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 69 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [69, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.06759, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 45.155, "width_percent": 1.043}, {"sql": "select * from `comments` where `comments`.`post_id` = 69 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [69], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.0700011, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 46.198, "width_percent": 1.437}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 68 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [68, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.072673, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 47.636, "width_percent": 1.275}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 68 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [68, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.07466, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 48.911, "width_percent": 1.066}, {"sql": "select * from `comments` where `comments`.`post_id` = 68 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [68], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.076716, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 49.977, "width_percent": 1.182}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 67 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [67, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.079214, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 51.159, "width_percent": 1.159}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 67 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [67, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.08111, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 52.318, "width_percent": 1.02}, {"sql": "select * from `comments` where `comments`.`post_id` = 67 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [67], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.083185, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 53.338, "width_percent": 1.159}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 66 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [66, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.0859258, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 54.497, "width_percent": 0.974}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 66 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [66, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.087851, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 55.471, "width_percent": 0.719}, {"sql": "select * from `comments` where `comments`.`post_id` = 66 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [66], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.089896, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 56.189, "width_percent": 0.974}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 65 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [65, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.092472, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 57.163, "width_percent": 1.136}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 65 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [65, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.094408, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 58.299, "width_percent": 0.834}, {"sql": "select * from `comments` where `comments`.`post_id` = 65 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.096342, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 59.133, "width_percent": 1.113}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 64 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [64, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.0988119, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 60.246, "width_percent": 0.997}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 64 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [64, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.101336, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 61.242, "width_percent": 0.974}, {"sql": "select * from `comments` where `comments`.`post_id` = 64 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [64], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.103446, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 62.216, "width_percent": 1.368}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 63 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [63, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.1062741, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 63.584, "width_percent": 1.113}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 63 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [63, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.108189, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 64.696, "width_percent": 0.834}, {"sql": "select * from `comments` where `comments`.`post_id` = 63 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [63], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.110094, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 65.531, "width_percent": 1.275}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 62 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [62, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.1131341, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 66.806, "width_percent": 1.089}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 62 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [62, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.1151009, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 67.895, "width_percent": 0.464}, {"sql": "select * from `comments` where `comments`.`post_id` = 62 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [62], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.1168408, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 68.359, "width_percent": 0.95}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 61 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [61, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.119394, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 69.309, "width_percent": 0.742}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 61 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [61, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.121095, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 70.051, "width_percent": 0.44}, {"sql": "select * from `comments` where `comments`.`post_id` = 61 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [61], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.122583, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 70.491, "width_percent": 0.464}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 60 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [60, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.125132, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 70.955, "width_percent": 0.765}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 60 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [60, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.126953, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 71.72, "width_percent": 0.556}, {"sql": "select * from `comments` where `comments`.`post_id` = 60 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [60], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.128664, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 72.276, "width_percent": 0.556}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 59 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [59, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.130842, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 72.833, "width_percent": 1.321}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 59 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [59, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.133275, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 74.154, "width_percent": 1.02}, {"sql": "select * from `comments` where `comments`.`post_id` = 59 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [59], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.135392, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 75.174, "width_percent": 1.252}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 58 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [58, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.138415, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 76.426, "width_percent": 1.205}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 58 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [58, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.140474, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 77.631, "width_percent": 0.649}, {"sql": "select * from `comments` where `comments`.`post_id` = 58 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [58], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.142166, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 78.28, "width_percent": 0.58}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 57 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [57, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.1441798, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 78.86, "width_percent": 0.927}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 57 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [57, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.146325, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 79.787, "width_percent": 1.043}, {"sql": "select * from `comments` where `comments`.`post_id` = 57 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [57], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.148319, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 80.83, "width_percent": 0.974}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 56 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [56, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.150661, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 81.803, "width_percent": 0.95}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 56 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [56, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.153191, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 82.754, "width_percent": 1.229}, {"sql": "select * from `comments` where `comments`.`post_id` = 56 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [56], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.155184, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 83.982, "width_percent": 0.858}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 55 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [55, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.157314, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 84.84, "width_percent": 0.742}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 55 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [55, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.1592631, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 85.582, "width_percent": 1.089}, {"sql": "select * from `comments` where `comments`.`post_id` = 55 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [55], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.161289, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 86.671, "width_percent": 0.834}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 54 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [54, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.163475, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 87.506, "width_percent": 0.719}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 54 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [54, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.165721, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 88.224, "width_percent": 1.321}, {"sql": "select * from `comments` where `comments`.`post_id` = 54 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [54], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.167927, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 89.546, "width_percent": 0.834}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 28 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [28, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.170191, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 90.38, "width_percent": 0.881}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 28 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [28, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.172061, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 91.261, "width_percent": 0.811}, {"sql": "select * from `comments` where `comments`.`post_id` = 28 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.173967, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 92.072, "width_percent": 0.834}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 29 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [29, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.1762629, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 92.907, "width_percent": 0.556}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 29 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [29, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.177836, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 93.463, "width_percent": 0.464}, {"sql": "select * from `comments` where `comments`.`post_id` = 29 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.179596, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 93.927, "width_percent": 1.159}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 30 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [30, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.18216, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 95.086, "width_percent": 0.95}, {"sql": "select exists(select * from `love` where `love`.`post_id` = 30 and `love`.`post_id` is not null and `user_id` = 28) as `exists`", "type": "query", "params": [], "bindings": [30, 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, {"index": 15, "namespace": "view", "name": "components.love-button", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/components/love-button.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.1841528, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Post.php:45", "source": {"index": 14, "namespace": null, "name": "app/Models/Post.php", "file": "C:\\xampp\\htdocs\\instapwa\\app\\Models\\Post.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=45", "ajax": false, "filename": "Post.php", "line": "45"}, "connection": "instapwa", "explain": null, "start_percent": 96.036, "width_percent": 0.95}, {"sql": "select * from `comments` where `comments`.`post_id` = 30 and `comments`.`post_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.186012, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "home:625", "source": {"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/home.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Fhome.blade.php&line=625", "ajax": false, "filename": "home.blade.php", "line": "625"}, "connection": "instapwa", "explain": null, "start_percent": 96.987, "width_percent": 0.927}, {"sql": "select count(*) as aggregate from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = 28 and `notifications`.`notifiable_id` is not null and `read_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\User", 28], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "layouts.app", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/layouts/app.blade.php", "line": 382}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.1913218, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "layouts.app:382", "source": {"index": 19, "namespace": "view", "name": "layouts.app", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/layouts/app.blade.php", "line": 382}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=382", "ajax": false, "filename": "app.blade.php", "line": "382"}, "connection": "instapwa", "explain": null, "start_percent": 97.914, "width_percent": 0.974}, {"sql": "select * from `love_wallets` where `love_wallets`.`user_id` = 28 and `love_wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "layouts.app", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/layouts/app.blade.php", "line": 413}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\instapwa\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.19338, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "layouts.app:413", "source": {"index": 21, "namespace": "view", "name": "layouts.app", "file": "C:\\xampp\\htdocs\\instapwa\\resources\\views/layouts/app.blade.php", "line": 413}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=413", "ajax": false, "filename": "app.blade.php", "line": "413"}, "connection": "instapwa", "explain": null, "start_percent": 98.887, "width_percent": 1.113}]}, "models": {"data": {"App\\Models\\Post": {"value": 20, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FPost.php&line=1", "ajax": false, "filename": "Post.php", "line": "?"}}, "App\\Models\\User": {"value": 14, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\UserProfile": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FUserProfile.php&line=1", "ajax": false, "filename": "UserProfile.php", "line": "?"}}, "App\\Models\\LiveStream": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FModels%2FLiveStream.php&line=1", "ajax": false, "filename": "LiveStream.php", "line": "?"}}}, "count": 44, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/home", "action_name": "home", "controller_action": "App\\Http\\Controllers\\HomeController@index", "uri": "GET home", "controller": "App\\Http\\Controllers\\HomeController@index<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=13\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Finstapwa%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=13\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/HomeController.php:13-60</a>", "middleware": "web, auth", "duration": "373ms", "peak_memory": "28MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-825543021 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-825543021\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-692497346 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-692497346\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1546105758 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Android&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"131 characters\">Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Mobile Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlNZMnNDb3hibVh2QTJRVFdIa0JVSWc9PSIsInZhbHVlIjoiSzJNT0x2L3NweXNja3crUDhxUktkZzJsWE43T1lSeDA0MEM5VG4xZTBlNkY2VzBpcWJxUHE2alU5MjhSSjY0bFpQVWxsMWxzOVZBUTl1VmRqMkoyT1M3WUNQUnJsSXRoUmNSTU1lWFpQR1Jwazl5TlhmbWJocWw3d3B5Z09RcnAiLCJtYWMiOiJlMmJjYTM4OTEwZmZkN2RiMTM0MGFiNDE0NjAxYzE5ZTFhZmZiZjJiODY4Yjk0ZWM2NmMzZmRiZTFmNWFmOGM4IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InUxQWxmMEwrZEU3QzdmaXFmT0phamc9PSIsInZhbHVlIjoiSW5SNVh1SGZJMVJYVm5IUXFaS2ZnV0xEVGJ5SWQ3UnB0cjdtaE40eXJBZEtRdGZiT1c3YVYvTDQ0WGZHVEEvQXZ5RDVFLzF6OGlYTnZ6K0ZFQWtvcGpTVXpST3pYeG9lVXpkVWlvQW1hdTVsRmszc01kQzdNSjMxNW9FTU5qS2giLCJtYWMiOiI3MDE3NDU2YTllNTU5ZWE3NWRhM2I2NWNhODc1ODQyODYyMjc2MTNhYjFlMzYzNGI4YmMzNWJhM2ZhNDUxZjk4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1546105758\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-467964918 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HKkQb009ur9juY1euzSJdZerzJQUHXdiHWRWdESt</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ysQ2OZdBk91GzyxsvUNjStsqoUKCd3cWniDGhfUE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-467964918\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1677423702 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 22 Jul 2025 21:24:47 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1677423702\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1245758684 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HKkQb009ur9juY1euzSJdZerzJQUHXdiHWRWdESt</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>28</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1245758684\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/home", "action_name": "home", "controller_action": "App\\Http\\Controllers\\HomeController@index"}, "badge": null}}