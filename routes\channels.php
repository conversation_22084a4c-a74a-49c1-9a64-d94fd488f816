<?php

use Illuminate\Support\Facades\Broadcast;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

// Global live stream notifications for all users
Broadcast::channel('live-streams', function ($user) {
    return $user !== null; // Any authenticated user can listen
});

// Private channel for user-specific notifications
Broadcast::channel('user.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

// Channel for specific live stream real-time features
Broadcast::channel('livestream.{streamId}', function ($user, $streamId) {
    return $user !== null; // Any authenticated user can join stream channels
});

// Channel for followers of a specific user
Broadcast::channel('user.{userId}.followers', function ($user, $userId) {
    // Check if the user follows this user or is the user themselves
    return $user->id == $userId || $user->following()->where('followed_id', $userId)->exists();
}); 