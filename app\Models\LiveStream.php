<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class LiveStream extends Model
{
    protected $table = 'live_streams';

    protected $fillable = [
        'user_id',
        'title',
        'description',
        'visibility',
        'stream_key',
        'stream_url',
        'recording_path',
        'thumbnail_path',
        'has_recording',
        'post_id',
        'duration_seconds',
        'is_live',
        'viewer_count',
        'started_at',
        'ended_at',
        'broadcaster_socket_id',
        'ice_servers'
    ];

    protected $casts = [
        'started_at' => 'datetime',
        'ended_at' => 'datetime',
        'is_live' => 'boolean',
        'has_recording' => 'boolean',
        'viewer_count' => 'integer',
        'duration_seconds' => 'integer',
        'user_id' => 'integer',
        'ice_servers' => 'array'
    ];

    protected $attributes = [
        'is_live' => false,
        'has_recording' => false,
        'viewer_count' => 0,
        'ice_servers' => '[]'
    ];

    protected $hidden = [
        'stream_key'
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function comments(): HasMany
    {
        return $this->hasMany(LivestreamComment::class, 'livestream_id');
    }

    public function post(): BelongsTo
    {
        return $this->belongsTo(Post::class);
    }

    // Accessor for recording URL
    public function getRecordingUrlAttribute()
    {
        if ($this->recording_path) {
            return asset('storage/' . $this->recording_path);
        }
        return null;
    }

    // Accessor for thumbnail URL
    public function getThumbnailUrlAttribute()
    {
        if ($this->thumbnail_path) {
            return asset('storage/' . $this->thumbnail_path);
        }
        return null;
    }

    // Accessor for stream duration formatted
    public function getFormattedDurationAttribute()
    {
        if ($this->duration_seconds) {
            return gmdate('H:i:s', $this->duration_seconds);
        }
        return '00:00:00';
    }

    // Scope for live streams
    public function scopeLive($query)
    {
        return $query->where('is_live', true);
    }

    // Scope for recorded streams
    public function scopeRecorded($query)
    {
        return $query->where('has_recording', true)->whereNotNull('recording_path');
    }

    // Calculate duration when stream ends
    public function calculateDuration()
    {
        if ($this->started_at && $this->ended_at) {
            // Ensure ended_at is after started_at
            if ($this->ended_at->greaterThan($this->started_at)) {
                $this->duration_seconds = $this->ended_at->diffInSeconds($this->started_at);
            } else {
                // If somehow ended_at is before started_at, set a default duration
                $this->duration_seconds = 0;
            }
            $this->save();
        }
    }

    // Get default ICE servers configuration
    public function getDefaultIceServers()
    {
        $iceServers = [
            [
                'urls' => [
                    'stun:stun.l.google.com:19302',
                    'stun:stun1.l.google.com:19302',
                    'stun:stun2.l.google.com:19302',
                    'stun:stun3.l.google.com:19302',
                    'stun:stun4.l.google.com:19302'
                ]
            ]
        ];

        // Add TURN server if credentials are configured
        $turnServer = env('TURN_SERVER_URL');
        $turnUsername = env('TURN_SERVER_USERNAME');
        $turnCredential = env('TURN_SERVER_CREDENTIAL');

        if ($turnServer && $turnUsername && $turnCredential) {
            $iceServers[] = [
                'urls' => $turnServer,
                'username' => $turnUsername,
                'credential' => $turnCredential
            ];
        }

        return $iceServers;
    }

    // Initialize ICE servers if not set
    public function initializeIceServers()
    {
        if (empty($this->ice_servers)) {
            $this->ice_servers = $this->getDefaultIceServers();
            $this->save();
        }
    }

    // Update viewer count
    public function updateViewerCount($count)
    {
        $this->viewer_count = max(0, $count);
        $this->save();
    }

    // Check if user is broadcaster
    public function isBroadcaster($userId)
    {
        return $this->user_id === $userId;
    }

    // Check if stream is viewable by user
    public function isViewableBy($user)
    {
        if ($this->visibility === 'public') {
            return true;
        }

        if (!$user) {
            return false;
        }

        if ($this->isBroadcaster($user->id)) {
            return true;
        }

        if ($this->visibility === 'subscribers') {
            return $user->isSubscribedTo($this->user_id);
        }

        if ($this->visibility === 'private') {
            return $user->isFollowing($this->user_id);
        }

        return false;
    }
}
