<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Live Streams') }}
            </h2>
            <a href="{{ route('streams.create') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Start New Stream
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <!-- Live Streams Section -->
                    <div class="mb-8">
                        <h3 class="text-lg font-semibold mb-4 text-gray-700">🔴 Live Now</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            @forelse ($streams->where('is_live', true) as $stream)
                                <div class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200 hover:border-blue-500 transition-colors">
                                    <div class="p-4">
                                        <div class="flex items-center mb-2">
                                            <span class="inline-block w-2 h-2 rounded-full bg-red-500 mr-2 animate-pulse"></span>
                                            <h3 class="text-lg font-semibold">{{ $stream->title }}</h3>
                                        </div>
                                        <p class="text-gray-600 mb-3">{{ $stream->description }}</p>
                                        <div class="flex justify-between items-center mb-3">
                                            <span class="text-sm text-gray-500">
                                                By {{ $stream->user->name }}
                                            </span>
                                            <span class="text-sm text-red-500 font-semibold">
                                                {{ $stream->viewer_count }} watching
                                            </span>
                                        </div>
                                        <a href="{{ route('streams.show', $stream) }}" 
                                           class="block w-full text-center bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition-colors">
                                            Join Stream
                                        </a>
                                    </div>
                                </div>
                            @empty
                                <div class="col-span-3 text-center py-8">
                                    <p class="text-gray-500">No live streams available right now.</p>
                                    <p class="text-gray-400 mt-2">Why not start your own?</p>
                                </div>
                            @endforelse
                        </div>
                    </div>

                    <!-- Upcoming/Offline Streams Section -->
                    <div>
                        <h3 class="text-lg font-semibold mb-4 text-gray-700">⭕ Other Streams</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            @forelse ($streams->where('is_live', false) as $stream)
                                <div class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200">
                                    <div class="p-4">
                                        <div class="flex items-center mb-2">
                                            <h3 class="text-lg font-semibold text-gray-700">{{ $stream->title }}</h3>
                                        </div>
                                        <p class="text-gray-600 mb-3">{{ $stream->description }}</p>
                                        <div class="flex justify-between items-center mb-3">
                                            <span class="text-sm text-gray-500">
                                                By {{ $stream->user->name }}
                                            </span>
                                            <span class="text-sm text-gray-500">
                                                Offline
                                            </span>
                                        </div>
                                        @if($stream->user_id === auth()->id())
                                            <a href="{{ route('streams.show', $stream) }}" 
                                               class="block w-full text-center bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded transition-colors">
                                                Start Streaming
                                            </a>
                                        @else
                                            <button disabled
                                                    class="block w-full text-center bg-gray-300 text-gray-500 font-bold py-2 px-4 rounded cursor-not-allowed">
                                                Currently Offline
                                            </button>
                                        @endif
                                    </div>
                                </div>
                            @empty
                                <div class="col-span-3 text-center py-8">
                                    <p class="text-gray-500">No other streams found.</p>
                                </div>
                            @endforelse
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout> 