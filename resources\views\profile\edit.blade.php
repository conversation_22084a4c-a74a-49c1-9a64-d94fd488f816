@extends('layouts.app')

@section('content')
<div class="profile-edit-container">
    <div class="profile-edit-header">
        <h1>Edit Profile</h1>
    </div>
    
    @if(session('success'))
        <div class="alert alert-success">
            {{ session('success') }}
        </div>
    @endif
    
    <form method="POST" action="{{ route('profile.update') }}" enctype="multipart/form-data" class="profile-edit-form">
        @csrf
        
        <div class="form-group">
            <label for="profile_photo">Profile Photo</label>
            <div class="profile-photo-container">
                <div class="current-photo">
                    @if($user->profile && $user->profile->photo)
                        <img src="{{ asset('storage/' . $user->profile->photo) }}" alt="{{ $user->username }}">
                    @else
                        <img src="/images/default-avatar.png" alt="{{ $user->username }}">
                    @endif
                </div>
                <div class="photo-upload">
                    <input type="file" name="profile_photo" id="profile_photo" class="form-control-file">
                    <small class="form-text text-muted">Upload a new profile photo (optional)</small>
                </div>
            </div>
        </div>
        
        <div class="form-group">
            <label for="name">Name</label>
            <input type="text" name="name" id="name" class="form-control" value="{{ $user->name }}" required>
            @error('name')
                <span class="invalid-feedback">{{ $message }}</span>
            @enderror
        </div>
        
        <div class="form-group">
            <label for="username">Username</label>
            <input type="text" name="username" id="username" class="form-control" value="{{ $user->username }}" required>
            @error('username')
                <span class="invalid-feedback">{{ $message }}</span>
            @enderror
        </div>
        
        <div class="form-group">
            <label for="bio">Bio</label>
            <textarea name="bio" id="bio" class="form-control" rows="4">{{ $user->profile ? $user->profile->bio : '' }}</textarea>
            @error('bio')
                <span class="invalid-feedback">{{ $message }}</span>
            @enderror
        </div>
        
        <div class="form-actions">
            <button type="submit" class="save-button">Save Changes</button>
            <a href="{{ route('profile') }}" class="cancel-button">Cancel</a>
        </div>
    </form>
</div>

<style>
    .profile-edit-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .profile-edit-header {
        margin-bottom: 20px;
        text-align: center;
    }
    
    .profile-edit-form {
        background: #fff;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
    }
    
    .form-control {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 16px;
    }
    
    .profile-photo-container {
        display: flex;
        align-items: center;
        gap: 20px;
    }
    
    .current-photo img {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        object-fit: cover;
    }
    
    .form-actions {
        display: flex;
        justify-content: space-between;
        margin-top: 30px;
    }
    
    .save-button {
        background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 5px;
        cursor: pointer;
        font-weight: bold;
    }
    
    .cancel-button {
        background: #f1f1f1;
        color: #333;
        border: none;
        padding: 12px 24px;
        border-radius: 5px;
        text-decoration: none;
        text-align: center;
    }
    
    .invalid-feedback {
        color: #dc3545;
        font-size: 14px;
        margin-top: 5px;
        display: block;
    }
    
    .alert {
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 5px;
    }
    
    .alert-success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
</style>
@endsection 