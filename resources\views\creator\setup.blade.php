@extends('layouts.app')

@section('content')
<div class="creator-setup-container">
    <div class="setup-header">
        <h1>Become a Creator</h1>
        <p>Set up your creator account to start offering exclusive content and earning money from your fans</p>
    </div>
    
    @if ($errors->any())
        <div class="alert alert-danger">
            <ul>
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif
    
    <form method="POST" action="{{ route('creator.settings.save') }}" class="creator-form">
        @csrf
        
        <div class="form-group">
            <label for="subscription_price">Monthly Subscription Price ($2.99-$99.99)</label>
            <div class="price-input">
                <span>$</span>
                <input type="number" step="0.01" min="2.99" max="99.99" name="subscription_price" id="subscription_price" 
                    value="{{ old('subscription_price', Auth::user()->subscription_price ?? 4.99) }}" required>
            </div>
            <p class="price-note">You will receive 50% of this amount for each subscriber</p>
        </div>
        
        <div class="form-group">
            <label for="creator_bio">Creator Bio</label>
            <textarea name="creator_bio" id="creator_bio" rows="5" 
                placeholder="Tell your fans what kind of content you'll be sharing..." required>{{ old('creator_bio', Auth::user()->creator_bio) }}</textarea>
            <p class="char-count"><span id="bioChars">0</span>/1000</p>
        </div>
        
        <div class="payment-section">
            <h2>Payment Information</h2>
            <p>We'll use this information to send you payouts when your earnings reach $5</p>
            
            <div class="form-group">
                <label for="payment_email">Payment Email</label>
                <input type="email" name="payment_email" id="payment_email" 
                    value="{{ old('payment_email', Auth::user()->payment_email) }}" required>
            </div>
            
            <div class="form-group">
                <label>Payment Method</label>
                <div class="payment-options">
                    <label class="payment-option">
                        <input type="radio" name="payment_method" value="paypal" 
                            {{ old('payment_method', Auth::user()->payment_method) === 'paypal' ? 'checked' : '' }}>
                        <span class="option-icon">
                            <i class="fab fa-paypal"></i>
                        </span>
                        <span>PayPal</span>
                    </label>
                    
                    <label class="payment-option">
                        <input type="radio" name="payment_method" value="bank" 
                            {{ old('payment_method', Auth::user()->payment_method) === 'bank' ? 'checked' : '' }}>
                        <span class="option-icon">
                            <i class="fas fa-university"></i>
                        </span>
                        <span>Bank Transfer</span>
                    </label>
                </div>
            </div>
            
            <div class="form-group">
                <label for="tax_id">Tax ID / SSN (for tax purposes)</label>
                <input type="text" name="tax_id" id="tax_id" 
                    value="{{ old('tax_id', Auth::user()->tax_id) }}" required>
                <p class="tax-note">Your tax information is encrypted and secure</p>
            </div>
        </div>
        
        <div class="terms-agreement">
            <label class="checkbox-label">
                <input type="checkbox" name="agree_terms" required>
                <span>I agree to the <a href="#">Creator Terms of Service</a> and understand the 50/50 revenue split</span>
            </label>
        </div>
        
        <button type="submit" class="submit-btn">Activate Creator Account</button>
    </form>
</div>

<style>
    .creator-setup-container {
        padding: 20px;
        max-width: 600px;
        margin: 0 auto;
        color: white;
    }
    
    .setup-header {
        text-align: center;
        margin-bottom: 30px;
    }
    
    .setup-header h1 {
        font-size: 28px;
        margin-bottom: 10px;
        color: #FFD700;
    }
    
    .creator-form {
        background: rgba(0, 0, 0, 0.3);
        border-radius: 15px;
        padding: 20px;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: bold;
    }
    
    .price-input {
        display: flex;
        align-items: center;
        background: rgba(0, 0, 0, 0.2);
        border-radius: 8px;
        overflow: hidden;
    }
    
    .price-input span {
        padding: 0 15px;
        background: rgba(0, 0, 0, 0.3);
        line-height: 46px;
    }
    
    input[type=number], input[type=email], input[type=text], textarea {
        width: 100%;
        padding: 12px 15px;
        background: rgba(0, 0, 0, 0.2);
        border: none;
        border-radius: 8px;
        color: white;
        font-size: 16px;
    }
    
    .price-input input[type=number] {
        border-radius: 0;
    }
    
    .price-note, .tax-note {
        font-size: 14px;
        color: #FFD700;
        margin-top: 5px;
    }
    
    .char-count {
        text-align: right;
        font-size: 14px;
        color: #aaa;
        margin-top: 5px;
    }
    
    .payment-section {
        margin-top: 30px;
        margin-bottom: 30px;
    }
    
    .payment-section h2 {
        font-size: 20px;
        margin-bottom: 5px;
        color: #FFD700;
    }
    
    .payment-options {
        display: flex;
        gap: 15px;
        margin-top: 10px;
    }
    
    .payment-option {
        flex: 1;
        background: rgba(0, 0, 0, 0.2);
        border-radius: 8px;
        padding: 15px;
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .payment-option:hover {
        background: rgba(255, 215, 0, 0.1);
    }
    
    .payment-option input {
        position: absolute;
        opacity: 0;
    }
    
    .payment-option input:checked + .option-icon {
        color: #FFD700;
    }
    
    .option-icon {
        font-size: 24px;
        margin-bottom: 5px;
    }
    
    .terms-agreement {
        margin-bottom: 30px;
    }
    
    .checkbox-label {
        display: flex;
        align-items: flex-start;
        gap: 10px;
        cursor: pointer;
    }
    
    .checkbox-label input {
        margin-top: 5px;
    }
    
    .checkbox-label a {
        color: #FFD700;
        text-decoration: none;
    }
    
    .submit-btn {
        width: 100%;
        padding: 15px;
        background: #FFD700;
        color: black;
        border: none;
        border-radius: 25px;
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;
    }
</style>

<script>
    // Character count for bio
    const bioTextarea = document.getElementById('creator_bio');
    const bioChars = document.getElementById('bioChars');
    
    bioTextarea.addEventListener('input', function() {
        const count = this.value.length;
        bioChars.textContent = count;
        
        if (count > 1000) {
            this.value = this.value.substring(0, 1000);
            bioChars.textContent = 1000;
        }
    });
    
    // Trigger initial character count
    bioTextarea.dispatchEvent(new Event('input'));
</script>
@endsection 