{"name": "instapwa", "version": "1.0.0", "description": "Instagram-like PWA with live streaming", "private": true, "scripts": {"dev": "vite", "build": "vite build", "signaling": "node signaling-server.js"}, "dependencies": {"axios": "^1.6.7", "dotenv": "^17.2.0", "express": "^4.18.2", "socket.io": "^4.7.4", "socket.io-client": "^4.7.4"}, "devDependencies": {"autoprefixer": "^10.4.14", "laravel-vite-plugin": "^0.7.8", "postcss": "^8.4.31", "tailwindcss": "^3.3.3", "vite": "^4.4.9"}}