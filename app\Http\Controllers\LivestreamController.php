<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\LiveStream;
use App\Models\LivestreamComment;
use App\Events\LiveStreamStartedEvent;
use App\Events\NewChatMessage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class LivestreamController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show create live stream page
     */
    public function create()
    {
        return view('livestream.create');
    }

    /**
     * Start a new live stream
     */
    public function start(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'visibility' => 'nullable|in:public,private,subscribers'
        ]);

        // Check if user already has an active stream
        $existingStream = LiveStream::where('user_id', Auth::id())
            ->where('is_live', true)
            ->first();

        if ($existingStream) {
            return response()->json([
                'success' => false,
                'message' => 'You already have an active stream. Please end your current stream first.',
                'existing_stream_id' => $existingStream->id
            ], 400);
        }

        try {
            $streamKey = Str::random(32);
            $stream = LiveStream::create([
                'user_id' => Auth::id(),
                'title' => $request->title,
                'description' => $request->description ?? '',
                'visibility' => $request->visibility ?? 'public',
                'stream_key' => $streamKey,
                'is_live' => true,
                'viewer_count' => 0,
                'has_recording' => false,
                'recording_path' => null,
                'started_at' => now()
            ]);

            // Initialize ICE servers
            $stream->initializeIceServers();

            // Load user relationship for broadcasting
            $stream->load('user');

            // Broadcast live stream started event to all users
            try {
                broadcast(new LiveStreamStartedEvent($stream));
            } catch (\Exception $e) {
                \Log::warning('Failed to broadcast stream started event: ' . $e->getMessage());
            }

            \Log::info('Live stream started successfully', [
                'stream_id' => $stream->id,
                'user_id' => $stream->user_id,
                'title' => $stream->title,
                'visibility' => $stream->visibility
            ]);

            return response()->json([
                'success' => true,
                'stream_id' => $stream->id,
                'stream_key' => $stream->stream_key,
                'ice_servers' => $stream->ice_servers,
                'message' => 'Stream started successfully!'
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to start stream', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to start stream: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show live stream
     */
    public function show($id)
    {
        $stream = LiveStream::with('user')->findOrFail($id);

        // Check if user can view this stream
        if (!$stream->isViewableBy(Auth::user())) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have permission to view this stream'
            ], 403);
        }

        if (!$stream->is_live) {
            return redirect()->route('livestream.ended', $id);
        }

        // Increment viewer count if not the streamer
        if (Auth::id() !== $stream->user_id) {
            $stream->increment('viewer_count');
        }

        return view('livestream.instagram-live', [
            'stream' => $stream,
            'isStreamer' => $stream->isBroadcaster(Auth::id()),
            'iceServers' => $stream->ice_servers,
            'currentUser' => Auth::user()
        ]);
    }

    /**
     * End live stream
     */
    public function end($id)
    {
        $stream = LiveStream::findOrFail($id);

        // Check if user owns the stream
        if ($stream->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        try {
            $stream->update([
                'is_live' => false,
                'ended_at' => now(),
                'broadcaster_socket_id' => null
            ]);

            // Calculate stream duration
            $stream->calculateDuration();

            // Load user for broadcasting
            $stream->load('user');

            // Broadcast stream ended event
            broadcast(new \App\Events\StreamEndedEvent([
                'stream_id' => $stream->id,
                'user_id' => $stream->user_id,
                'user_name' => $stream->user->name,
                'message' => $stream->user->name . ' ended their live stream',
                'duration' => $stream->formatted_duration,
                'has_recording' => $stream->has_recording
            ]));

            \Log::info('Live stream ended', [
                'stream_id' => $stream->id,
                'user_id' => $stream->user_id,
                'duration_seconds' => $stream->duration_seconds,
                'has_recording' => $stream->has_recording
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Stream ended successfully',
                'stream_id' => $stream->id,
                'duration' => $stream->formatted_duration,
                'has_recording' => $stream->has_recording,
                'redirect_url' => route('livestream.ended', $stream->id)
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to end stream', [
                'stream_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to end stream: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update broadcaster socket ID
     */
    public function updateBroadcasterSocket(Request $request, $id)
    {
        $stream = LiveStream::findOrFail($id);

        if ($stream->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        try {
            $stream->update([
                'broadcaster_socket_id' => $request->socket_id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Broadcaster socket ID updated'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update broadcaster socket ID'
            ], 500);
        }
    }

    /**
     * Add comment to live stream
     */
    public function addComment(Request $request, $id)
    {
        $request->validate([
            'comment' => 'required|string|max:255'
        ]);

        $stream = LiveStream::findOrFail($id);

        if (!$stream->is_live) {
            return response()->json([
                'success' => false,
                'message' => 'Stream is not live'
            ], 400);
        }

        try {
            $comment = LivestreamComment::create([
                'live_stream_id' => $stream->id,
                'user_id' => Auth::id(),
                'comment' => $request->comment
            ]);

            $comment->load('user');

            // Broadcast comment to all viewers
            broadcast(new NewChatMessage($comment, $stream->id))->toOthers();

            return response()->json([
                'success' => true,
                'comment' => [
                    'id' => $comment->id,
                    'username' => $comment->user->username,
                    'comment' => $comment->comment,
                    'created_at' => $comment->created_at->diffForHumans()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to add comment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display saved live stream recordings
     */
    public function saved()
    {
        $savedStreams = LiveStream::with(['user'])
            ->where('has_recording', true)
            ->whereNotNull('recording_path')
            ->orderBy('started_at', 'desc')
            ->paginate(12);

        return view('livestream.saved', compact('savedStreams'));
    }

    /**
     * Send heart reaction
     */
    public function sendHeart($id)
    {
        $stream = LiveStream::findOrFail($id);

        if (!$stream->is_live) {
            return response()->json([
                'success' => false,
                'message' => 'Stream is not live'
            ], 400);
        }

        // Broadcast heart reaction
        broadcast(new \App\Events\HeartReaction([
            'user_id' => Auth::id(),
            'username' => Auth::user()->username,
            'stream_id' => $stream->id
        ]))->toOthers();

        return response()->json(['success' => true]);
    }

    /**
     * Upload stream recording
     */
    public function uploadRecording(Request $request, $id)
    {
        \Log::info('Recording upload attempt', [
            'stream_id' => $id,
            'user_id' => Auth::id(),
            'has_file' => $request->hasFile('recording'),
            'file_size' => $request->file('recording') ? $request->file('recording')->getSize() : 0,
            'all_files' => array_keys($request->allFiles()),
            'file' => $request->file('recording')->getClientOriginalName(),
            'request_method' => $request->method(),
            'content_type' => $request->header('Content-Type')
        ]);

        try {
            // Note: Removed strict MIME type validation for 'recording' field because:
            // MediaRecorder-generated WebM files often have non-standard MIME types
            // that don't match Laravel's expected 'video/webm' type
            $request->validate([
                'recording' => 'required', // 1GB max
                'thumbnail' => 'nullable|image|mimetypes:image/jpeg,image/png|max:10240' // 10MB max
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::error('Recording upload validation failed', [
                'stream_id' => $id,
                'errors' => $e->errors(),
                'file_info' => $request->hasFile('recording') ? [
                    'size' => $request->file('recording')->getSize(),
                    'mime' => $request->file('recording')->getMimeType(),
                    'extension' => $request->file('recording')->getClientOriginalExtension()
                ] : 'No file'
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Validation failed: ' . implode(', ', $e->validator->errors()->all())
            ], 422);
        }

        $stream = LiveStream::findOrFail($id);

        // Check if user owns the stream
        if ($stream->user_id !== Auth::id()) {
            \Log::warning('Unauthorized recording upload attempt', [
                'stream_id' => $id,
                'stream_owner' => $stream->user_id,
                'uploader' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        try {
            // Create directories if they don't exist
            $recordingDir = storage_path('app/public/livestream-recordings');
            $thumbnailDir = storage_path('app/public/stream-thumbnails');

            if (!file_exists($recordingDir)) {
                mkdir($recordingDir, 0755, true);
                \Log::info('Created recording directory: ' . $recordingDir);
            }

            if (!file_exists($thumbnailDir)) {
                mkdir($thumbnailDir, 0755, true);
                \Log::info('Created thumbnail directory: ' . $thumbnailDir);
            }

            // Store the recording file
            $file = $request->file('recording');
            $originalName = $file->getClientOriginalName();
            $extension = $file->getClientOriginalExtension();
            $fileName = 'stream_' . $stream->id . '_' . time() . '.' . $extension;
            $recordingPath = 'livestream-recordings/' . $fileName;

            // Move file to the correct location
            $fullRecordingPath = storage_path('app/public/' . $recordingPath);
            $file->move(dirname($fullRecordingPath), basename($fullRecordingPath));

            \Log::info('Recording file stored successfully', [
                'stream_id' => $stream->id,
                'original_name' => $originalName,
                'stored_path' => $recordingPath,
                'full_path' => $fullRecordingPath,
                'file_size' => filesize($fullRecordingPath)
            ]);

            $thumbnailPath = null;

            // Handle thumbnail upload if provided
            if ($request->hasFile('thumbnail')) {
                $thumbnailFile = $request->file('thumbnail');
                $thumbnailFileName = 'thumb_' . $stream->id . '_' . time() . '.jpg';
                $thumbnailPath = 'stream-thumbnails/' . $thumbnailFileName;
                $fullThumbnailPath = storage_path('app/public/' . $thumbnailPath);

                $thumbnailFile->move(dirname($fullThumbnailPath), basename($fullThumbnailPath));

                \Log::info('Thumbnail uploaded successfully', [
                    'stream_id' => $stream->id,
                    'thumbnail_path' => $thumbnailPath
                ]);
            }

            // Update stream with recording info
            $updateData = [
                'recording_path' => $recordingPath,
                'has_recording' => true
            ];

            if ($thumbnailPath) {
                $updateData['thumbnail_path'] = $thumbnailPath;
            }

            \Log::info('Attempting to update stream', [
                'stream_id' => $stream->id,
                'update_data' => $updateData,
                'before_update' => [
                    'recording_path' => $stream->recording_path,
                    'thumbnail_path' => $stream->thumbnail_path,
                    'has_recording' => $stream->has_recording
                ]
            ]);

            $updated = $stream->update($updateData);

            if (!$updated) {
                \Log::error('Failed to update stream record', [
                    'stream_id' => $stream->id,
                    'update_data' => $updateData
                ]);
                throw new \Exception('Failed to update stream record');
            }

            // Verify the update worked
            $stream->refresh();

            \Log::info('Stream updated successfully', [
                'stream_id' => $stream->id,
                'after_update' => [
                    'recording_path' => $stream->recording_path,
                    'thumbnail_path' => $stream->thumbnail_path,
                    'has_recording' => $stream->has_recording
                ],
                'update_successful' => $updated
            ]);

            // Create a post from the recording if stream is ended
            if (!$stream->is_live && $stream->recording_path) {
                $post = $this->createPostFromRecording($stream);
                if ($post) {
                    \Log::info('Post created from recording', [
                        'stream_id' => $stream->id,
                        'post_id' => $post->id
                    ]);
                }
            }

            return response()->json([
                'success' => true,
                'recording_url' => asset('storage/' . $recordingPath),
                'thumbnail_url' => $thumbnailPath ? asset('storage/' . $thumbnailPath) : null,
                'recording_path' => $recordingPath,
                'thumbnail_path' => $thumbnailPath,
                'has_recording' => true,
                'stream_id' => $stream->id,
                'message' => 'Recording saved successfully'
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to upload recording', [
                'stream_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to upload recording: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle recording chunk upload
     */
    public function uploadChunk(Request $request)
    {
        try {
            $request->validate([
                'chunk' => 'required|file',
                'stream_id' => 'required|exists:live_streams,id',
                'is_final' => 'required|boolean'
            ]);

            $stream = LiveStream::findOrFail($request->stream_id);

            // Check if user owns the stream
            if ($stream->user_id !== Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 403);
            }

            // Create directory if it doesn't exist
            $recordingDir = storage_path('app/public/livestream-recordings/temp/' . $stream->id);
            if (!file_exists($recordingDir)) {
                mkdir($recordingDir, 0755, true);
            }

            // Store chunk with timestamp to maintain order
            $timestamp = time();
            $chunkPath = $recordingDir . '/chunk_' . $timestamp . '.webm';
            
            // Move uploaded chunk to storage
            $request->file('chunk')->move(dirname($chunkPath), basename($chunkPath));

            return response()->json([
                'success' => true,
                'message' => 'Chunk uploaded successfully'
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to upload chunk', [
                'stream_id' => $request->stream_id ?? null,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to upload chunk: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Finalize recording from uploaded chunks
     */
    public function finalizeRecording(Request $request)
    {
        try {
            $request->validate([
                'stream_id' => 'required|exists:live_streams,id',
                'is_final' => 'required|boolean',
                'thumbnail' => 'nullable|image|mimetypes:image/jpeg,image/png|max:10240'
            ]);

            $stream = LiveStream::findOrFail($request->stream_id);

            // Check if user owns the stream
            if ($stream->user_id !== Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 403);
            }

            // Get temp directory path
            $tempDir = storage_path('app/public/livestream-recordings/temp/' . $stream->id);
            if (!file_exists($tempDir)) {
                throw new \Exception('No recording chunks found');
            }

            // Get all chunks and sort by timestamp
            $chunks = glob($tempDir . '/chunk_*.webm');
            sort($chunks);

            if (empty($chunks)) {
                throw new \Exception('No recording chunks found');
            }

            // Create final recording directory if needed
            $recordingDir = storage_path('app/public/livestream-recordings');
            if (!file_exists($recordingDir)) {
                mkdir($recordingDir, 0755, true);
            }

            // Create final recording file
            $fileName = 'stream_' . $stream->id . '_' . time() . '.webm';
            $recordingPath = 'livestream-recordings/' . $fileName;
            $fullRecordingPath = storage_path('app/public/' . $recordingPath);

            // Concatenate all chunks
            $fp = fopen($fullRecordingPath, 'wb');
            foreach ($chunks as $chunk) {
                fwrite($fp, file_get_contents($chunk));
                unlink($chunk); // Delete chunk after use
            }
            fclose($fp);

            // Clean up temp directory
            rmdir($tempDir);

            // Handle thumbnail
            $thumbnailPath = null;
            if ($request->hasFile('thumbnail')) {
                $thumbnailDir = storage_path('app/public/stream-thumbnails');
                if (!file_exists($thumbnailDir)) {
                    mkdir($thumbnailDir, 0755, true);
                }

                $thumbnailFileName = 'thumb_' . $stream->id . '_' . time() . '.jpg';
                $thumbnailPath = 'stream-thumbnails/' . $thumbnailFileName;
                $request->file('thumbnail')->move(
                    storage_path('app/public/stream-thumbnails'),
                    $thumbnailFileName
                );
            }

            // Update stream record
            $updateData = [
                'recording_path' => $recordingPath,
                'has_recording' => true
            ];

            if ($thumbnailPath) {
                $updateData['thumbnail_path'] = $thumbnailPath;
            }

            $stream->update($updateData);

            // Create post if stream has ended
            if (!$stream->is_live) {
                $this->createPostFromRecording($stream);
            }

            return response()->json([
                'success' => true,
                'message' => 'Recording finalized successfully',
                'recording_url' => asset('storage/' . $recordingPath),
                'thumbnail_url' => $thumbnailPath ? asset('storage/' . $thumbnailPath) : null
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to finalize recording', [
                'stream_id' => $request->stream_id ?? null,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to finalize recording: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a post from stream recording
     */
    private function createPostFromRecording($stream)
    {
        try {
            $post = $stream->user->posts()->create([
                'caption' => "📺 Live stream recording: {$stream->title}",
                'media_url' => $stream->recording_path,
                'media_type' => 'video',
                'visibility' => 'public'
            ]);

            // Update stream with post reference
            $stream->update(['post_id' => $post->id]);

            return $post;
        } catch (\Exception $e) {
            \Log::error('Failed to create post from recording: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Show live streams index
     */
    public function index()
    {
        $liveStreams = LiveStream::where('is_live', true)
            ->where('visibility', 'public')
            ->with('user')
            ->orderBy('viewer_count', 'desc')
            ->orderBy('started_at', 'desc')
            ->paginate(12);

        return view('livestream.index', compact('liveStreams'));
    }

    /**
     * Show ended stream
     */
    public function ended($id)
    {
        $stream = LiveStream::with('user')->findOrFail($id);

        return view('livestream.ended', compact('stream'));
    }

    /**
     * Get stream comments
     */
    public function getComments($id)
    {
        $stream = LiveStream::findOrFail($id);

        $comments = LivestreamComment::where('live_stream_id', $stream->id)
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->take(50)
            ->get()
            ->reverse()
            ->values();

        return response()->json([
            'success' => true,
            'comments' => $comments->map(function ($comment) {
                return [
                    'id' => $comment->id,
                    'username' => $comment->user->username,
                    'comment' => $comment->comment,
                    'created_at' => $comment->created_at->diffForHumans()
                ];
            })
        ]);
    }

    /**
     * Get current viewer count
     */
    public function getViewerCount($id)
    {
        $stream = LiveStream::findOrFail($id);

        return response()->json([
            'success' => true,
            'viewer_count' => $stream->viewer_count
        ]);
    }

    /**
     * Get live streams status for API
     */
    public function getLiveStreamsStatus()
    {
        $streams = LiveStream::where('is_live', true)
            ->where('visibility', 'public')
            ->select('id', 'viewer_count', 'started_at')
            ->get();

        return response()->json([
            'success' => true,
            'streams' => $streams
        ]);
    }

    /**
     * Get recording status
     */
    public function getRecordingStatus($id)
    {
        try {
            $stream = LiveStream::findOrFail($id);
            
            return response()->json([
                'success' => true,
                'has_recording' => $stream->has_recording && $stream->recording_path !== null,
                'recording_path' => $stream->recording_path,
                'thumbnail_path' => $stream->thumbnail_path
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error checking recording status: ' . $e->getMessage()
            ], 500);
        }
    }
}
