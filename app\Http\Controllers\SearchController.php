<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Post;
use App\Models\Camp;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class SearchController extends Controller
{
    /**
     * Show the search page
     */
    public function index()
    {
        // Get trending searches (most searched terms in the last 7 days)
        $trendingSearches = DB::table('search_logs')
            ->select('query', DB::raw('count(*) as count'))
            ->where('created_at', '>=', now()->subDays(7))
            ->groupBy('query')
            ->orderBy('count', 'desc')
            ->limit(5)
            ->get();
            
        // Get user's recent searches
        $recentSearches = [];
        if (Auth::check()) {
            $recentSearches = DB::table('search_logs')
                ->where('user_id', Auth::id())
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->pluck('query')
                ->toArray();
        }
        
        return view('search.index', compact('trendingSearches', 'recentSearches'));
    }
    
    /**
     * Search API endpoint
     */
    public function search(Request $request)
    {
        $query = $request->input('q');
        
        if (empty($query)) {
            return response()->json([
                'users' => [],
                'posts' => [],
                'camps' => []
            ]);
        }
        
        // Log the search query
        if (Auth::check()) {
            DB::table('search_logs')->insert([
                'user_id' => Auth::id(),
                'query' => $query,
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }
        
        // Search users
        $users = User::where('username', 'like', "%{$query}%")
            ->orWhere('name', 'like', "%{$query}%")
            ->limit(5)
            ->get(['id', 'username', 'name', 'is_verified']);
            
        // Add profile photo URL to each user
        $users->each(function($user) {
            $user->profile_photo = $user->profile && $user->profile->photo 
                ? asset('storage/' . $user->profile->photo) 
                : asset('images/default-avatar.png');
        });
        
        // Search posts
        $posts = Post::where('caption', 'like', "%{$query}%")
            ->with('user:id,username,name')
            ->limit(5)
            ->get();
            
        // Add formatted data to posts
        $posts->each(function($post) {
            $post->media_url = asset('storage/' . $post->media_url);
            $post->user->profile_photo = $post->user->profile && $post->user->profile->photo 
                ? asset('storage/' . $post->user->profile->photo) 
                : asset('images/default-avatar.png');
        });
        
        // Search camps
        $camps = Camp::where('name', 'like', "%{$query}%")
            ->orWhere('description', 'like', "%{$query}%")
            ->limit(5)
            ->get();
            
        return response()->json([
            'users' => $users,
            'posts' => $posts,
            'camps' => $camps
        ]);
    }
    
    /**
     * Clear recent searches
     */
    public function clearRecentSearches()
    {
        if (Auth::check()) {
            DB::table('search_logs')
                ->where('user_id', Auth::id())
                ->delete();
        }
        
        return response()->json(['success' => true]);
    }
} 