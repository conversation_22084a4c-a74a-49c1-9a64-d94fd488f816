<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LoveWallet extends Model
{
    protected $fillable = [
        'user_id',
        'love_balance'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function hasEnoughLoves($count = 1)
    {
        return $this->love_balance >= $count;
    }

    public function deductLoves($count = 1)
    {
        if ($this->hasEnoughLoves($count)) {
            $this->decrement('love_balance', $count);
            return true;
        }
        return false;
    }

    public function addLoves($count)
    {
        $this->increment('love_balance', $count);
    }
} 