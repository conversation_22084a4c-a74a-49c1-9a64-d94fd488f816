<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Subscription;
use App\Models\Earning;
use App\Models\Love;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class CreatorController extends Controller
{
    /**
     * Creator dashboard
     */
    public function dashboard()
    {
        $user = Auth::user();
        
        // Redirect if not a creator
        if (!$user->is_creator) {
            return redirect()->route('creator.setup');
        }
        
        // Get earnings stats
        $totalEarned = $user->earnings()->sum('amount');
        $pendingBalance = $user->earnings()->where('status', 'pending')->sum('amount');
        $paidOut = $user->earnings()->where('status', 'paid')->sum('amount');
        
        // Get subscriber stats
        $activeSubscribers = $user->subscribers()
            ->where('status', 'active')
            ->where('expires_at', '>', now())
            ->count();
            
        $recentSubscribers = $user->subscribers()
            ->with('subscriber.profile')
            ->where('status', 'active')
            ->orderBy('started_at', 'desc')
            ->take(5)
            ->get();
            
        // Get recent earnings
        $recentEarnings = $user->earnings()
            ->latest()
            ->take(10)
            ->get();
        
        // Get subscriber count
        $subscriberCount = $user->subscribers()->count();
        
        // Get total loves received
        $totalLoves = Love::whereIn('post_id', $user->posts()->pluck('id'))->count();
        
        return view('creator.dashboard', compact(
            'totalEarned',
            'pendingBalance',
            'paidOut',
            'activeSubscribers',
            'recentSubscribers',
            'recentEarnings',
            'subscriberCount',
            'totalLoves'
        ));
    }
    
    /**
     * Creator setup page
     */
    public function setup()
    {
        return view('creator.setup');
    }
    
    /**
     * Save creator settings
     */
    public function saveSettings(Request $request)
    {
        $user = Auth::user();
        
        $validator = Validator::make($request->all(), [
            'subscription_price' => 'required|numeric|min:2.99|max:99.99',
            'creator_bio' => 'required|string|max:1000',
            'payment_email' => 'required|email',
            'payment_method' => 'required|in:paypal,bank',
            'tax_id' => 'required|string|max:50',
        ]);
        
        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }
        
        $user->update([
            'is_creator' => true,
            'subscription_price' => $request->subscription_price,
            'creator_bio' => $request->creator_bio,
            'payment_email' => $request->payment_email,
            'payment_method' => $request->payment_method,
            'tax_id' => $request->tax_id,
        ]);
        
        return redirect()->route('creator.dashboard')
            ->with('success', 'Your creator account has been set up successfully!');
    }
    
    /**
     * View subscribers
     */
    public function subscribers()
    {
        $user = Auth::user();
        
        // Redirect if not a creator
        if (!$user->is_creator) {
            return redirect()->route('creator.setup');
        }
        
        $activeSubscribers = $user->subscribers()
            ->with('subscriber.profile')
            ->where('status', 'active')
            ->where('expires_at', '>', now())
            ->paginate(20);
            
        return view('creator.subscribers', compact('activeSubscribers'));
    }
    
    /**
     * View earnings
     */
    public function earnings()
    {
        $user = Auth::user();
        
        // Redirect if not a creator
        if (!$user->is_creator) {
            return redirect()->route('creator.setup');
        }
        
        $monthlyEarnings = $user->earnings()
            ->selectRaw('SUM(amount) as total, YEAR(earned_at) as year, MONTH(earned_at) as month')
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get();
            
        $earnings = $user->earnings()
            ->latest()
            ->paginate(20);
            
        return view('creator.earnings', compact('monthlyEarnings', 'earnings'));
    }
    
    /**
     * Subscribe to a creator
     */
    public function subscribe($creatorId)
    {
        $user = Auth::user();
        $creator = User::findOrFail($creatorId);
        
        // Check if creator exists and is actually a creator
        if (!$creator->is_creator) {
            return back()->with('error', 'This user is not a creator.');
        }
        
        // Check if already subscribed
        if ($user->isSubscribedTo($creatorId)) {
            return back()->with('error', 'You are already subscribed to this creator.');
        }
        
        // Create a new subscription (in a real app, you'd connect to a payment provider first)
        $subscription = Subscription::create([
            'subscriber_id' => $user->id,
            'creator_id' => $creator->id,
            'price' => $creator->subscription_price,
            'started_at' => now(),
            'expires_at' => now()->addMonth(),
            'auto_renew' => true,
            'status' => 'active',
            'payment_method' => 'card', // This would come from the payment form
        ]);
        
        // Create earnings record
        Earning::create([
            'creator_id' => $creator->id,
            'amount' => $creator->subscription_price * 0.5, // 50% revenue share
            'source' => 'subscription',
            'source_id' => $subscription->id,
            'earned_at' => now(),
            'status' => 'pending'
        ]);
        
        return redirect()->route('profile.show', $creator->username)
            ->with('success', 'You have successfully subscribed to ' . $creator->username);
    }
    
    /**
     * Cancel subscription
     */
    public function cancelSubscription($subscriptionId)
    {
        $subscription = Subscription::findOrFail($subscriptionId);
        
        // Check if the subscription belongs to the user
        if ($subscription->subscriber_id !== Auth::id()) {
            return back()->with('error', 'You do not have permission to cancel this subscription.');
        }
        
        $subscription->update([
            'auto_renew' => false,
            'status' => 'canceled'
        ]);
        
        return back()->with('success', 'Your subscription has been canceled and will not renew.');
    }
} 