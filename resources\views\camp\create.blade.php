@extends('layouts.app')

@section('content')
<style>
    .create-camp-container {
        padding: 15px;
        max-width: 800px;
        margin: 0 auto;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
    }
    
    .form-input {
        width: 100%;
        padding: 10px;
        background: #333;
        border: none;
        border-radius: 5px;
        color: white;
    }
    
    .form-textarea {
        width: 100%;
        padding: 10px;
        background: #333;
        border: none;
        border-radius: 5px;
        color: white;
        min-height: 150px;
    }
    
    .radio-group {
        display: flex;
        gap: 15px;
    }
    
    .radio-label {
        display: flex;
        align-items: center;
        gap: 5px;
    }
    
    .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        margin-top: 20px;
    }
    
    .btn {
        padding: 10px 15px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
    }
    
    .btn-cancel {
        background: #444;
        color: white;
    }
    
    .btn-submit {
        background: #FFD700;
        color: black;
        font-weight: bold;
    }
    
    .error-message {
        color: #FF4136;
        font-size: 14px;
        margin-top: 5px;
    }
</style>

<div class="create-camp-container">
    <h1>Create New Camp</h1>
    
    @if($errors->any())
        <div style="background-color: #FF4136; color: white; padding: 10px; border-radius: 5px; margin-bottom: 15px;">
            <ul style="margin: 0; padding-left: 20px;">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif
    
    <form action="{{ route('camps.store') }}" method="POST">
        @csrf
        
        <div class="form-group">
            <label for="name" class="form-label">Camp Name</label>
            <input type="text" id="name" name="name" class="form-input" value="{{ old('name') }}" required>
            @error('name')
                <div class="error-message">{{ $message }}</div>
            @enderror
        </div>
        
        <div class="form-group">
            <label for="description" class="form-label">Description</label>
            <textarea id="description" name="description" class="form-textarea" required>{{ old('description') }}</textarea>
            @error('description')
                <div class="error-message">{{ $message }}</div>
            @enderror
        </div>
        
        <div class="form-group">
            <label class="form-label">Camp Type</label>
            <div class="radio-group">
                <label class="radio-label">
                    <input type="radio" name="type" value="public" {{ old('type', 'public') === 'public' ? 'checked' : '' }}>
                    Public (Anyone can join)
                </label>
                <label class="radio-label">
                    <input type="radio" name="type" value="private" {{ old('type') === 'private' ? 'checked' : '' }}>
                    Private (Invitation only)
                </label>
            </div>
            
            @error('type')
                <div class="error-message">{{ $message }}</div>
            @enderror
        </div>
        
        <div class="form-actions">
            <a href="{{ route('camps.index') }}" class="btn btn-cancel">Cancel</a>
            <button type="submit" class="btn btn-submit">Create Camp</button>
        </div>
    </form>
</div>
@endsection 