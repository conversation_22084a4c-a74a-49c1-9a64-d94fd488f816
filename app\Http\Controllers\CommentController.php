<?php

namespace App\Http\Controllers;

use App\Models\Comment;
use App\Models\Post;
use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CommentController extends Controller
{
    public function store(Request $request, $postId)
    {
        $request->validate([
            'content' => 'required|string|max:1000',
            'parent_id' => 'nullable|exists:comments,id'
        ]);

        $post = Post::findOrFail($postId);
        
        $comment = Comment::create([
            'user_id' => Auth::id(),
            'post_id' => $postId,
            'content' => $request->content,
            'parent_id' => $request->parent_id
        ]);

        // Create notification if commenting on someone else's post
        if ($post->user_id !== Auth::id()) {
            Notification::create([
                'user_id' => $post->user_id,
                'type' => 'comment',
                'notifiable_type' => 'post',
                'notifiable_id' => $post->id,
                'data' => json_encode([
                    'user_id' => Auth::id(),
                    'username' => Auth::user()->username,
                    'post_id' => $post->id,
                    'comment_id' => $comment->id,
                    'comment_preview' => substr($comment->content, 0, 50)
                ])
            ]);
        }

        return response()->json([
            'success' => true,
            'comment' => $comment->load('user.profile')
        ]);
    }

    public function destroy($id)
    {
        $comment = Comment::findOrFail($id);
        
        // Check if the user is authorized to delete this comment
        if ($comment->user_id !== Auth::id() && $comment->post->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to delete this comment'
            ], 403);
        }
        
        $comment->delete();
        
        return response()->json([
            'success' => true
        ]);
    }

    public function update(Request $request, $id)
    {
        $comment = Comment::findOrFail($id);
        
        // Check if user can edit this comment
        if (!$comment->can_edit) {
            return response()->json([
                'success' => false,
                'message' => 'You cannot edit this comment'
            ], 403);
        }
        
        $request->validate([
            'content' => 'required|string|max:1000'
        ]);
        
        $comment->update([
            'content' => $request->content
        ]);
        
        return response()->json([
            'success' => true,
            'comment' => $comment->fresh()->load('user')
        ]);
    }

    public function index($postId)
    {
        $comments = Comment::where('post_id', $postId)
            ->with('user.profile')
            ->latest()
            ->get()
            ->map(function ($comment) {
                return [
                    'id' => $comment->id,
                    'content' => $comment->content,
                    'created_at' => $comment->created_at->diffForHumans(),
                    'user' => [
                        'username' => $comment->user->username,
                        'profile_photo' => $comment->user->profile?->photo 
                            ? asset('storage/' . $comment->user->profile->photo) 
                            : asset('images/default-avatar.png')
                    ],
                    'can_edit' => $comment->can_edit,
                    'can_delete' => $comment->can_delete
                ];
            });
        
        return response()->json([
            'success' => true,
            'comments' => $comments
        ]);
    }
} 