<div id="comment-modal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <div class="header-icon">
                <svg viewBox="0 0 24 24" width="24" height="24">
                    <path d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z" fill="#000"/>
                </svg>
            </div>
            <h2>Comments</h2>
        </div>
        
        <div class="comments-list" id="comments-list"></div>
        
        <div class="comment-input-wrapper">
            <div class="emoji-row">
                <div class="emoji-list">
                    <span onclick="addEmoji('😀')">😀</span>
                    <span onclick="addEmoji('😂')">😂</span>
                    <span onclick="addEmoji('😊')">😊</span>
                    <span onclick="addEmoji('😍')">😍</span>
                    <span onclick="addEmoji('😎')">😎</span>
                    <span onclick="addEmoji('😜')">😜</span>
                    <span onclick="addEmoji('🥰')">🥰</span>
                    <span onclick="addEmoji('😇')">😇</span>
                    <span onclick="addEmoji('😋')">😋</span>
                </div>
            </div>
            
            <div class="comment-form-container">
                <img src="{{ Auth::user()->profile?->photo ? asset('storage/' . Auth::user()->profile->photo) : asset('images/default-avatar.png') }}" 
                     class="user-avatar" 
                     alt="{{ Auth::user()->username }}">
                     
                <form id="comment-form" class="comment-form">
                    @csrf
                    <input type="text" 
                           id="comment-input" 
                           placeholder="Add a comment as {{ Auth::user()->username }}..." 
                           autocomplete="off">
                    <button type="submit" class="send-btn">Send</button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.modal {
    display: none;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    top: 0;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.5);
}

.modal-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    transform: translateY(100%);
    transition: transform 0.3s ease-out;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
}

.modal-content.open {
    transform: translateY(0);
}

.modal-header {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    gap: 15px;
}

.modal-header h2 {
    font-size: 16px;
    font-weight: 600;
    color: #000;
}

.header-icon {
    cursor: pointer;
}

.comments-list {
    flex: 1;
    overflow-y: auto;
    padding: 15px 20px;
}

.comment-item {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
}

.comment-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.comment-content {
    flex: 1;
}

.comment-username {
    font-weight: 600;
    color: #000;
    margin-right: 8px;
}

.comment-text {
    color: #262626;
    font-size: 14px;
}

.comment-meta {
    margin-top: 8px;
    display: flex;
    gap: 16px;
    color: #8e8e8e;
    font-size: 12px;
}

.emoji-row {
    padding: 10px 20px;
    border-top: 1px solid #eee;
}

.emoji-list {
    display: flex;
    gap: 15px;
    overflow-x: auto;
    padding-bottom: 5px;
}

.emoji-list span {
    font-size: 24px;
    cursor: pointer;
    transition: transform 0.2s;
}

.emoji-list span:hover {
    transform: scale(1.2);
}

.comment-form-container {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px 20px;
    border-top: 1px solid #eee;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

.comment-form {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 12px;
    background: #f2f2f2;
    border-radius: 20px;
    padding: 8px 16px;
}

#comment-input {
    flex: 1;
    border: none;
    background: transparent;
    font-size: 14px;
    outline: none;
    color: #262626;
}

#comment-input::placeholder {
    color: #8e8e8e;
}

.send-btn {
    background: none;
    border: none;
    color: #0095f6;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    padding: 0;
}

.send-btn:disabled {
    opacity: 0.5;
    cursor: default;
}

.error-message {
    text-align: center;
    color: #ed4956;
    padding: 20px;
}

.comment-main {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.comment-actions {
    position: relative;
}

.action-btn {
    background: none;
    border: none;
    padding: 4px;
    color: #8e8e8e;
    cursor: pointer;
}

.action-menu {
    display: none;
    position: absolute;
    right: 0;
    top: 100%;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 16px rgba(0, 0, 0, 0.12);
    z-index: 10;
    min-width: 120px;
    overflow: hidden;
}

.action-menu.show {
    display: block;
}

.action-menu button {
    width: 100%;
    padding: 12px 16px;
    border: none;
    background: none;
    text-align: left;
    font-size: 14px;
    color: #262626;
    cursor: pointer;
}

.action-menu button:hover {
    background: #f8f8f8;
}

.action-menu button:last-child {
    color: #ed4956;
}

.edit-form {
    margin-top: 8px;
    display: none;
    flex-direction: column;
    gap: 8px;
}

.edit-input {
    width: 100%;
    border: 1px solid #dbdbdb;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    color: #262626;
}

.edit-actions {
    display: flex;
    gap: 12px;
}

.edit-actions button {
    background: none;
    border: none;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    padding: 0;
}

.save-btn {
    color: #0095f6;
}

.cancel-btn {
    color: #8e8e8e;
}
</style>

<script>
let currentPostId = null;

function openCommentModal(postId) {
    currentPostId = postId;
    const modal = document.getElementById('comment-modal');
    modal.style.display = 'flex';
    setTimeout(() => {
        modal.querySelector('.modal-content').classList.add('open');
        document.getElementById('comment-input').focus();
    }, 10);
    loadComments(postId);
}

function closeCommentModal() {
    const modal = document.getElementById('comment-modal');
    modal.querySelector('.modal-content').classList.remove('open');
    setTimeout(() => {
        modal.style.display = 'none';
        document.getElementById('comment-input').value = '';
    }, 300);
}

function addEmoji(emoji) {
    const input = document.getElementById('comment-input');
    const start = input.selectionStart;
    const end = input.selectionEnd;
    input.value = input.value.substring(0, start) + emoji + input.value.substring(end);
    input.focus();
    input.selectionStart = input.selectionEnd = start + emoji.length;
}

function loadComments(postId) {
    fetch(`/posts/${postId}/comments`)
        .then(response => response.json())
        .then(data => {
            const commentsList = document.getElementById('comments-list');
            if (!data.comments || data.comments.length === 0) {
                commentsList.innerHTML = '<p style="text-align: center; color: #8e8e8e; padding: 20px;">No comments yet</p>';
                return;
            }
            
            commentsList.innerHTML = data.comments.map(comment => `
                <div class="comment-item" id="comment-${comment.id}">
                    <img src="${comment.user.profile_photo}" class="comment-avatar" alt="${comment.user.username}">
                    <div class="comment-content">
                        <div class="comment-main">
                            <div class="comment-text-container">
                                <span class="comment-username">${comment.user.username}</span>
                                <span class="comment-text" id="comment-text-${comment.id}">${comment.content}</span>
                            </div>
                            ${comment.can_edit || comment.can_delete ? `
                                <div class="comment-actions">
                                    <button class="action-btn" onclick="toggleCommentActions(${comment.id})">
                                        <svg viewBox="0 0 48 48" width="16" height="16">
                                            <circle cx="8" cy="24" r="4.5" fill="currentColor"/>
                                            <circle cx="24" cy="24" r="4.5" fill="currentColor"/>
                                            <circle cx="40" cy="24" r="4.5" fill="currentColor"/>
                                        </svg>
                                    </button>
                                    <div class="action-menu" id="action-menu-${comment.id}">
                                        ${comment.can_edit ? `
                                            <button onclick="editComment(${comment.id})">Edit</button>
                                        ` : ''}
                                        ${comment.can_delete ? `
                                            <button onclick="deleteComment(${comment.id})">Delete</button>
                                        ` : ''}
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                        <form class="edit-form" id="edit-form-${comment.id}" style="display: none;">
                            <input type="text" class="edit-input" value="${comment.content}">
                            <div class="edit-actions">
                                <button type="submit" class="save-btn">Save</button>
                                <button type="button" class="cancel-btn" onclick="cancelEdit(${comment.id})">Cancel</button>
                            </div>
                        </form>
                        <div class="comment-meta">
                            <span>${comment.created_at}</span>
                            <span>0 Loves</span>
                            <span>Reply</span>
                        </div>
                    </div>
                </div>
            `).join('');

            // Add event listeners for edit forms
            document.querySelectorAll('.edit-form').forEach(form => {
                form.addEventListener('submit', handleEditSubmit);
            });
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('comments-list').innerHTML = 
                '<p class="error-message">Could not load comments</p>';
        });
}

function toggleCommentActions(commentId) {
    const menu = document.getElementById(`action-menu-${commentId}`);
    document.querySelectorAll('.action-menu').forEach(m => {
        if (m !== menu) m.classList.remove('show');
    });
    menu.classList.toggle('show');
}

function editComment(commentId) {
    const commentText = document.getElementById(`comment-text-${commentId}`);
    const editForm = document.getElementById(`edit-form-${commentId}`);
    const actionMenu = document.getElementById(`action-menu-${commentId}`);
    
    commentText.parentElement.style.display = 'none';
    editForm.style.display = 'flex';
    actionMenu.classList.remove('show');
    editForm.querySelector('.edit-input').focus();
}

function cancelEdit(commentId) {
    const commentText = document.getElementById(`comment-text-${commentId}`);
    const editForm = document.getElementById(`edit-form-${commentId}`);
    
    commentText.parentElement.style.display = 'block';
    editForm.style.display = 'none';
}

function handleEditSubmit(e) {
    e.preventDefault();
    const form = e.target;
    const commentId = form.id.split('-')[2];
    const content = form.querySelector('.edit-input').value.trim();
    
    if (!content) return;
    
    const formData = new FormData();
    formData.append('content', content);
    formData.append('_token', document.querySelector('meta[name="csrf-token"]').content);
    formData.append('_method', 'PUT');
    
    fetch(`/comments/${commentId}`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadComments(currentPostId);
        } else {
            alert(data.message || 'Could not update comment');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Could not update comment. Please try again.');
    });
}

function deleteComment(commentId) {
    if (!confirm('Are you sure you want to delete this comment?')) return;
    
    const formData = new FormData();
    formData.append('_token', document.querySelector('meta[name="csrf-token"]').content);
    formData.append('_method', 'DELETE');
    
    fetch(`/comments/${commentId}`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadComments(currentPostId);
            // Update comment count
            const countElem = document.querySelector(`#post-${currentPostId} .action-count`);
            countElem.textContent = parseInt(countElem.textContent) - 1;
        } else {
            alert(data.message || 'Could not delete comment');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Could not delete comment. Please try again.');
    });
}

// Close action menus when clicking outside
document.addEventListener('click', function(e) {
    if (!e.target.closest('.comment-actions')) {
        document.querySelectorAll('.action-menu').forEach(menu => {
            menu.classList.remove('show');
        });
    }
});

document.getElementById('comment-form').addEventListener('submit', function(e) {
    e.preventDefault();
    const input = document.getElementById('comment-input');
    const content = input.value.trim();
    
    if (!content) return;
    
    const formData = new FormData();
    formData.append('content', content);
    formData.append('_token', document.querySelector('meta[name="csrf-token"]').content);
    
    fetch(`/posts/${currentPostId}/comments`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            input.value = '';
            loadComments(currentPostId);
            const countElem = document.querySelector(`#post-${currentPostId} .action-count`);
            countElem.textContent = parseInt(countElem.textContent) + 1;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Could not post comment. Please try again.');
    });
});

// Close modal when clicking outside
document.getElementById('comment-modal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeCommentModal();
    }
});
</script> 