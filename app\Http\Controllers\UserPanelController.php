<?php

namespace App\Http\Controllers;

use App\Models\Love;
use App\Models\Earning;
use App\Models\Subscription;
use App\Models\Withdrawal;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class UserPanelController extends Controller
{
    /**
     * Show the love history page
     */
    public function loveHistory(Request $request)
    {
        $user = Auth::user();

        // Build query with filter
        $query = Love::where('user_id', $user->id)
            ->with(['post.user', 'post'])
            ->latest();

        // Apply filters if provided
        if ($request->has('filter')) {
            if ($request->filter === 'paid') {
                $query->where('is_paid', true);
            } elseif ($request->filter === 'free') {
                $query->where('is_paid', false);
            }
        }

        $loves = $query->paginate(10);

        // Get love statistics
        $lovesGiven = Love::where('user_id', $user->id)->count();
        $totalSpent = Love::where('user_id', $user->id)
            ->where('is_paid', true)
            ->sum('amount');

        return view('user-panel.love-history', compact('loves', 'lovesGiven', 'totalSpent'));
    }

    /**
     * Show the revenue page
     */
    public function revenue()
    {
        $user = Auth::user();

        // Get revenue statistics
        $loveRevenue = $user->earnings()
            ->where('source', 'loves')
            ->sum('amount');

        $subscriptionRevenue = $user->earnings()
            ->where('source', 'subscription')
            ->sum('amount');

        $totalRevenue = $loveRevenue + $subscriptionRevenue;

        $totalWithdrawn = Withdrawal::where('user_id', $user->id)
                                   ->whereIn('status', ['completed', 'processing'])
                                   ->sum('amount');

        $availableForWithdrawal = $user->creator_balance;

        // Get past payouts
        $pastWithdrawals = Withdrawal::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->get();

        // Get monthly earnings breakdown
        $monthlyEarnings = Earning::where('creator_id', $user->id)
            ->select(DB::raw('YEAR(earned_at) as year, MONTH(earned_at) as month, SUM(amount) as total'))
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->take(12)
            ->get()
            ->map(function($item) {
                $monthName = date('F', mktime(0, 0, 0, $item->month, 1));
                return [
                    'month' => $monthName . ' ' . $item->year,
                    'total' => $item->total
                ];
            });

        // Get recent earnings with proper error handling
        $recentEarnings = Earning::where('creator_id', $user->id)
            ->with(['sourcePost.user', 'sourceSubscription.subscriber'])
            ->orderBy('earned_at', 'desc')
            ->take(10)
            ->get();

        return view('user-panel.revenue', compact(
            'loveRevenue',
            'subscriptionRevenue',
            'totalRevenue',
            'totalWithdrawn',
            'availableForWithdrawal',
            'pastWithdrawals',
            'monthlyEarnings',
            'recentEarnings'
        ));
    }

    /**
     * Show the love stats page
     */
    public function loveStats()
    {
        $user = Auth::user();

        // Get love statistics
        $lovesGiven = Love::where('user_id', $user->id)->count();
        $lovesReceived = Love::whereHas('post', function($query) use ($user) {
            $query->where('user_id', $user->id);
        })->count();

        // Get top loved posts
        $topPosts = $user->posts()
            ->withCount('loves')
            ->orderBy('loves_count', 'desc')
            ->take(5)
            ->get();

        // Get monthly love trends
        $monthlyLoves = Love::whereHas('post', function($query) use ($user) {
            $query->where('user_id', $user->id);
        })
        ->select(DB::raw('YEAR(created_at) as year, MONTH(created_at) as month, COUNT(*) as count'))
        ->groupBy('year', 'month')
        ->orderBy('year', 'desc')
        ->orderBy('month', 'desc')
        ->take(12)
        ->get()
        ->map(function($item) {
            $monthName = date('F', mktime(0, 0, 0, $item->month, 1));
            return [
                'month' => $monthName . ' ' . $item->year,
                'count' => $item->count
            ];
        });

        return view('user-panel.love-stats', compact('lovesGiven', 'lovesReceived', 'topPosts', 'monthlyLoves'));
    }
}
