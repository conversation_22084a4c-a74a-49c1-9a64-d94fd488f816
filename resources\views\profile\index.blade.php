@extends('layouts.app')

@section('content')
<style>
    .profile-header {
        background: #111;
        padding: 20px;
        position: relative;
    }

    .profile-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .user-name {
        font-size: 20px;
        font-weight: bold;
    }

    .stats {
        display: flex;
        gap: 20px;
        color: #FFD700;
    }

    .stat {
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .settings-icon {
        width: 24px;
        height: 24px;
        fill: white;
    }

    .profile-info {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
    }

    .profile-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        position: relative;
    }

    .profile-avatar img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
    }

    .online-indicator {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 20px;
        height: 20px;
        background: #FFD700;
        border-radius: 50%;
        border: 2px solid #111;
    }

    .profile-details {
        flex: 1;
    }

    .profile-stats {
        display: flex;
        justify-content: space-around;
        text-align: center;
        margin-bottom: 15px;
    }

    .stat-value {
        font-weight: bold;
        font-size: 18px;
    }

    .stat-label {
        font-size: 12px;
        color: #888;
    }

    .bio {
        font-size: 14px;
        margin-bottom: 15px;
        line-height: 1.4;
    }

    .profile-actions {
        display: flex;
        gap: 10px;
    }

    .action-button {
        flex: 1;
        text-align: center;
        padding: 8px;
        border-radius: 5px;
        font-size: 14px;
        cursor: pointer;
    }

    .edit-profile {
        background: #333;
    }

    .share-profile {
        background: #333;
    }

    .interests {
        display: flex;
        overflow-x: auto;
        padding: 15px;
        gap: 15px;
        background: #111;
        margin-bottom: 10px;
    }

    .interest {
        display: flex;
        flex-direction: column;
        align-items: center;
        min-width: 70px;
    }

    .interest-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        margin-bottom: 5px;
        object-fit: cover;
    }

    .add-interest {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: #333;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 5px;
    }

    .interest-name {
        font-size: 12px;
        color: white;
        text-align: center;
    }

    .profile-nav {
        display: flex;
        justify-content: space-around;
        padding: 15px;
        background: #111;
        margin-bottom: 2px;
    }

    .profile-nav-icon {
        width: 24px;
        height: 24px;
        fill: white;
    }

    .posts-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 2px;
    }

    .post-thumbnail {
        width: 100%;
        aspect-ratio: 1;
        object-fit: cover;
    }

    .no-posts {
        padding: 40px 20px;
        text-align: center;
        background: #111;
        color: #888;
    }

    /* Live Streams Section Styles */
    .live-streams-section {
        background: #111;
        margin-bottom: 2px;
    }

    .current-live-stream {
        padding: 15px;
        border-bottom: 1px solid #333;
    }

    .live-stream-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 12px;
    }

    .live-stream-header h3 {
        color: white;
        font-size: 16px;
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .live-stream-header h3 i {
        color: #ff3040;
    }

    .live-badge-profile {
        background: #ff3040;
        color: white;
        font-size: 10px;
        font-weight: 600;
        padding: 4px 8px;
        border-radius: 12px;
        text-transform: uppercase;
        animation: pulse 2s infinite;
    }

    .live-stream-card {
        background: #1a1a1a;
        border-radius: 8px;
        padding: 12px;
        cursor: pointer;
        transition: transform 0.2s ease;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .live-stream-card:hover {
        transform: translateY(-2px);
    }

    .live-title {
        color: white;
        font-weight: 600;
        margin-bottom: 6px;
    }

    .live-meta {
        display: flex;
        gap: 15px;
        font-size: 12px;
        color: #ccc;
    }

    .live-meta i {
        margin-right: 4px;
    }

    .join-stream-btn {
        background: #ff3040;
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 6px;
    }

    .recent-live-streams {
        padding: 15px;
    }

    .live-streams-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
    }

    .live-streams-header h3 {
        color: white;
        font-size: 16px;
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .live-streams-header h3 i {
        color: #ff3040;
    }

    .view-all-link {
        color: #ff3040;
        text-decoration: none;
        font-size: 12px;
        font-weight: 500;
    }

    .live-streams-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 10px;
    }

    .live-stream-thumbnail {
        cursor: pointer;
        transition: transform 0.2s ease;
    }

    .live-stream-thumbnail:hover {
        transform: scale(1.05);
    }

    .stream-video-preview {
        position: relative;
        aspect-ratio: 16/9;
        background: #000;
        border-radius: 6px;
        overflow: hidden;
        margin-bottom: 6px;
    }

    .stream-preview-video {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .no-preview {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #333;
        color: #666;
        font-size: 1.5rem;
    }

    .stream-preview-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(transparent, rgba(0,0,0,0.7));
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.2s ease;
    }

    .live-stream-thumbnail:hover .stream-preview-overlay {
        opacity: 1;
    }

    .play-overlay {
        color: white;
        font-size: 1.5rem;
    }

    .stream-duration-badge {
        position: absolute;
        bottom: 6px;
        right: 6px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        font-size: 10px;
        padding: 2px 6px;
        border-radius: 4px;
    }

    .stream-preview-info {
        color: white;
    }

    .stream-preview-title {
        font-size: 12px;
        font-weight: 500;
        margin-bottom: 4px;
        line-height: 1.2;
    }

    .stream-preview-meta {
        font-size: 10px;
        color: #999;
        display: flex;
        justify-content: space-between;
    }

    @media (max-width: 768px) {
        .live-streams-grid {
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 8px;
        }
    }
</style>

<div class="profile-header">
    <div class="profile-top">
        <div class="user-name">{{ $user->username }}</div>
        <div class="stats">
            <div class="stat">
                <svg class="settings-icon" viewBox="0 0 24 24">
                    <path d="M12,15.39L8.24,17.66L9.23,13.38L5.91,10.5L10.29,10.13L12,6.09L13.71,10.13L18.09,10.5L14.77,13.38L15.76,17.66M22,9.24L14.81,8.63L12,2L9.19,8.63L2,9.24L7.45,13.97L5.82,21L12,17.27L18.18,21L16.54,13.97L22,9.24Z"/>
                </svg>
                22
            </div>
            <div class="stat">
                <svg class="settings-icon" viewBox="0 0 24 24">
                    <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8"/>
                </svg>
                12
            </div>
            <svg class="settings-icon" viewBox="0 0 24 24">
                <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.21,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.21,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.67 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/>
            </svg>
        </div>
    </div>

    <div class="profile-info">
        <div class="profile-avatar">
            @if($user->profile && $user->profile->photo)
                <img src="{{ asset('storage/' . $user->profile->photo) }}" alt="{{ $user->username }}">
            @else
                <img src="/images/default-avatar.png" alt="{{ $user->username }}">
            @endif
            <div class="online-indicator"></div>
        </div>
        <div class="profile-details">
            <div class="profile-stats">
                <div>
                    <div class="stat-value">{{ $postCount }}</div>
                    <div class="stat-label">Posts</div>
                </div>
                <div>
                    <div class="stat-value">{{ $totalLiveStreams }}</div>
                    <div class="stat-label">Streams</div>
                </div>
                <div>
                    <div class="stat-value">{{ $followersCount }}</div>
                    <div class="stat-label">Followers</div>
                </div>
                <div>
                    <div class="stat-value">{{ $followingCount }}</div>
                    <div class="stat-label">Following</div>
                </div>
            </div>
            <div class="bio">
                @if($user->profile && $user->profile->bio)
                    {{ $user->profile->bio }}
                @else
                    No bio yet.
                @endif
            </div>
            <div class="profile-actions">
                @if(Auth::id() === $user->id)
                    <a href="{{ route('user-panel.revenue') }}" class="revenue-btn">
                        <i class="fas fa-dollar-sign"></i>
                        <span>Revenue</span>
                    </a>
                    <a href="{{ route('profile.edit') }}" class="edit-profile-btn">
                        <i class="fas fa-cog"></i>
                        <span>Edit Profile</span>
                    </a>
                @else
                    <button class="follow-btn">Follow</button>
                    @if($user->is_creator)
                        <button class="subscribe-btn">Subscribe</button>
                    @endif
                @endif
            </div>
        </div>
    </div>
</div>

<div class="interests">
    <div class="interest">
        <div class="add-interest">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="white">
                <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
            </svg>
        </div>
        <div class="interest-name">Add</div>
    </div>
    @if($user->interests->count() > 0)
        @foreach($user->interests as $interest)
            <div class="interest">
                <img src="/images/interests/{{ strtolower($interest->name) }}.jpg" alt="{{ $interest->name }}" class="interest-icon" onerror="this.src='/images/interests/default.jpg'">
                <div class="interest-name">{{ $interest->name }}</div>
            </div>
        @endforeach
    @else
        @foreach(['Friends', 'Sport', 'Design', 'Music'] as $interest)
            <div class="interest">
                <img src="/images/interests/{{ strtolower($interest) }}.jpg" alt="{{ $interest }}" class="interest-icon" onerror="this.src='/images/interests/default.jpg'">
                <div class="interest-name">{{ $interest }}</div>
            </div>
        @endforeach
    @endif
</div>

<!-- Live Streams Section -->
@if($currentLiveStream || $recentLiveStreams->count() > 0)
<div class="live-streams-section">
    <!-- Current Live Stream -->
    @if($currentLiveStream)
        <div class="current-live-stream">
            <div class="live-stream-header">
                <h3>
                    <i class="fas fa-broadcast-tower"></i>
                    Currently Live
                </h3>
                <span class="live-badge-profile">LIVE</span>
            </div>
            <div class="live-stream-card" onclick="joinLiveStream({{ $currentLiveStream->id }})">
                <div class="live-stream-info">
                    <div class="live-title">{{ $currentLiveStream->title }}</div>
                    <div class="live-meta">
                        <span class="viewer-count-profile">
                            <i class="fas fa-eye"></i>
                            {{ $currentLiveStream->viewer_count }} viewers
                        </span>
                        <span class="live-duration">
                            <i class="fas fa-clock"></i>
                            {{ $currentLiveStream->started_at->diffForHumans() }}
                        </span>
                    </div>
                </div>
                <div class="join-stream-btn">
                    <i class="fas fa-play"></i>
                    Join Stream
                </div>
            </div>
        </div>
    @endif

    <!-- Recent Live Streams -->
    @if($recentLiveStreams->count() > 0)
        <div class="recent-live-streams">
            <div class="live-streams-header">
                <h3>
                    <i class="fas fa-video"></i>
                    Recent Live Streams
                </h3>
                @if(Auth::id() === $user->id)
                    <a href="{{ route('livestream.saved') }}" class="view-all-link">View All</a>
                @endif
            </div>
            <div class="live-streams-grid">
                @foreach($recentLiveStreams as $stream)
                    <div class="live-stream-thumbnail" onclick="viewRecording({{ $stream->id }})">
                        <div class="stream-video-preview">
                            @if($stream->recording_path)
                                <video preload="metadata" class="stream-preview-video">
                                    <source src="{{ asset('storage/' . $stream->recording_path) }}#t=1" type="video/webm">
                                    <source src="{{ asset('storage/' . $stream->recording_path) }}#t=1" type="video/mp4">
                                </video>
                            @else
                                <div class="no-preview">
                                    <i class="fas fa-play"></i>
                                </div>
                            @endif

                            <div class="stream-preview-overlay">
                                <div class="play-overlay">
                                    <i class="fas fa-play"></i>
                                </div>
                                <div class="stream-duration-badge">
                                    {{ $stream->formatted_duration }}
                                </div>
                            </div>
                        </div>

                        <div class="stream-preview-info">
                            <div class="stream-preview-title">{{ Str::limit($stream->title, 30) }}</div>
                            <div class="stream-preview-meta">
                                <span class="stream-preview-views">{{ $stream->viewer_count }} views</span>
                                <span class="stream-preview-date">{{ $stream->ended_at ? $stream->ended_at->diffForHumans() : '' }}</span>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    @endif
</div>
@endif

<div class="profile-nav">
    <svg class="profile-nav-icon" viewBox="0 0 24 24">
        <path d="M4,4H20V20H4V4M6,8V18H18V8H6Z"/>
    </svg>
    <svg class="profile-nav-icon" viewBox="0 0 24 24">
        <path d="M17,10.5V7A1,1 0 0,0 16,6H4A1,1 0 0,0 3,7V17A1,1 0 0,0 4,18H16A1,1 0 0,0 17,17V13.5L21,17.5V6.5L17,10.5Z"/>
    </svg>
    <svg class="profile-nav-icon" viewBox="0 0 24 24">
        <path d="M19,19H5V5H19M19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M13.96,12.29L11.21,15.83L9.25,13.47L6.5,17H17.5L13.96,12.29Z"/>
    </svg>
</div>

@if($posts->isNotEmpty())
    <div class="posts-grid">
        @foreach($posts as $post)
            <div class="post">
                <h2>{{ $post->caption }}</h2>
                <p>Posted by {{ $post->user->username }}</p>
                <!-- Other post details -->
            </div>
        @endforeach
    </div>
@else
    <div class="no-posts">
        <p>No posts yet</p>
        <a href="{{ route('create.post') }}" style="color: #FFD700; text-decoration: none; display: block; margin-top: 10px;">Create your first post</a>
    </div>
@endif
@endsection

<script>
// Live Stream Functions
function joinLiveStream(streamId) {
    window.location.href = `/live/${streamId}`;
}

function viewRecording(streamId) {
    window.location.href = `/live/${streamId}/ended`;
}
</script>
