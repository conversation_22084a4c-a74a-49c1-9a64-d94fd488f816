<div id="buyLovesModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>Buy Loves</h2>
            <button class="close-btn">&times;</button>
        </div>
        
        <div class="love-packs">
            @foreach($lovePacks as $index => $pack)
                <div class="love-pack" data-pack-id="{{ $index }}">
                    <div class="love-amount">
                        <i class="fas fa-heart"></i>
                        {{ number_format($pack['amount']) }}
                    </div>
                    <div class="pack-price">
                        BDT {{ number_format($pack['price'], 2) }}
                    </div>
                </div>
            @endforeach
        </div>
        
        <div class="modal-footer">
            <p class="note">Loves are non-refundable and support content creators</p>
        </div>
    </div>
</div>

<style>
.love-packs {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    padding: 20px;
}

.love-pack {
    background: #222;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.love-pack:hover {
    background: #333;
}

.love-amount {
    font-size: 24px;
    margin-bottom: 10px;
}

.love-amount i {
    color: #ff0066;
}

.pack-price {
    color: #FFD700;
    font-weight: bold;
}

.note {
    text-align: center;
    color: #666;
    font-size: 14px;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('buyLovesModal');
    const closeBtn = modal.querySelector('.close-btn');
    const lovePacks = modal.querySelectorAll('.love-pack');
    
    closeBtn.addEventListener('click', () => {
        modal.style.display = 'none';
    });
    
    lovePacks.forEach(pack => {
        pack.addEventListener('click', () => {
            const packId = pack.dataset.packId;
            
            fetch('/loves/buy', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({ pack_id: packId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    modal.style.display = 'none';
                    // Update love balance display
                    document.querySelectorAll('.love-balance').forEach(el => {
                        el.textContent = data.newBalance;
                    });
                    // Retry the love action if it was triggered by insufficient balance
                    if (window.lastLovedPostId) {
                        document.querySelector(`#love-button-${window.lastLovedPostId}`).click();
                        window.lastLovedPostId = null;
                    }
                }
            });
        });
    });
});

function showBuyLovesModal(postId = null) {
    if (postId) window.lastLovedPostId = postId;
    document.getElementById('buyLovesModal').style.display = 'flex';
}
</script> 