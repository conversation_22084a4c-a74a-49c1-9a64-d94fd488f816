<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Story;
use App\Models\Reel;
use Illuminate\Support\Facades\Auth;

class CreateController extends Controller
{
    public function story()
    {
        return view('create.story');
    }

    public function storeStory(Request $request)
    {
        $request->validate([
            'media' => 'required|file|mimes:jpeg,png,jpg,gif,mp4|max:10240',
            'type' => 'required|in:image,video'
        ]);

        try {
            $file = $request->file('media');
            $filename = time() . '_' . $file->getClientOriginalName();
            $path = $file->storeAs('stories', $filename, 'public');

            Story::create([
                'user_id' => Auth::id(),
                'media' => $path,
                'type' => $request->type,
                'expires_at' => now()->addHours(24)
            ]);

            return redirect()->route('home')->with('success', 'Story created successfully!');
        } catch (\Exception $e) {
            return back()->with('error', 'Error creating story: ' . $e->getMessage());
        }
    }

    public function reel()
    {
        return view('create.reel');
    }

    public function storeReel(Request $request)
    {
        $request->validate([
            'video' => 'required|file|mimes:mp4|max:102400', // 100MB max
            'caption' => 'nullable|string|max:2200',
        ]);

        try {
            $file = $request->file('video');
            $filename = time() . '_' . $file->getClientOriginalName();
            $path = $file->storeAs('reels', $filename, 'public');

            Reel::create([
                'user_id' => Auth::id(),
                'video' => $path,
                'caption' => $request->caption
            ]);

            return redirect()->route('home')->with('success', 'Reel created successfully!');
        } catch (\Exception $e) {
            return back()->with('error', 'Error creating reel: ' . $e->getMessage());
        }
    }
    public function live()
    {
        return view('livestream.create', [
            'pageTitle' => 'Go Live',
            'isFullscreen' => true // This can be used in your layout to remove unnecessary elements
        ]);
    }
} 