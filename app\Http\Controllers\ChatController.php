<?php

namespace App\Http\Controllers;

use App\Models\Chat;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Events\NewChatMessage;

class ChatController extends Controller
{
    public function index()
    {
        // Get live users first (for livestream integration)
        $liveUsers = DB::select("
            SELECT DISTINCT
                u.id,
                u.username,
                u.name,
                u.is_verified,
                up.photo,
                ls.title as stream_title,
                ls.viewer_count
            FROM users u
            LEFT JOIN user_profiles up ON u.id = up.user_id
            JOIN live_streams ls ON u.id = ls.user_id
            WHERE ls.is_live = 1 AND u.id != ?
            ORDER BY ls.viewer_count DESC, u.username ASC
        ", [Auth::id()]);

        // Get users with recent chats (status will be handled by Socket.IO)
        $recentChats = DB::select("
            SELECT
                u.id,
                u.username,
                u.name,
                u.is_verified,
                up.photo,
                c.message,
                c.created_at,
                (SELECT COUNT(*) FROM chats
                 WHERE receiver_id = ? AND sender_id = u.id AND read_at IS NULL) as unread_count,
                ls.title as stream_title,
                COALESCE(ls.viewer_count, 0) as viewer_count,
                CASE WHEN ls.is_live = 1 THEN 'live' ELSE 'offline' END as initial_status
            FROM users u
            LEFT JOIN user_profiles up ON u.id = up.user_id
            LEFT JOIN live_streams ls ON u.id = ls.user_id AND ls.is_live = 1
            JOIN (
                SELECT
                    CASE
                        WHEN sender_id = ? THEN receiver_id
                        ELSE sender_id
                    END as user_id,
                    message,
                    created_at,
                    ROW_NUMBER() OVER (PARTITION BY
                        CASE
                            WHEN sender_id = ? THEN receiver_id
                            ELSE sender_id
                        END
                    ORDER BY created_at DESC) as rn
                FROM chats
                WHERE sender_id = ? OR receiver_id = ?
            ) c ON u.id = c.user_id
            WHERE c.rn = 1
            ORDER BY 
                CASE WHEN ls.is_live = 1 THEN 0 ELSE 1 END,
                COALESCE(ls.viewer_count, 0) DESC,
                c.created_at DESC
        ", [Auth::id(), Auth::id(), Auth::id(), Auth::id(), Auth::id()]);
        
        // Convert arrays to collections for better blade template compatibility
        $recentChats = collect($recentChats);
        $liveUsers = collect($liveUsers);
        
        return view('chat.index', compact('recentChats', 'liveUsers'));
    }

    public function show($userId)
    {
        $user = User::with('profile')->findOrFail($userId);

        // Check if user is currently live
        $liveStream = \App\Models\LiveStream::where('user_id', $userId)
            ->where('is_live', true)
            ->first();

        // Mark messages as read
        Chat::where('sender_id', $userId)
            ->where('receiver_id', Auth::id())
            ->whereNull('read_at')
            ->update(['read_at' => now()]);

        // Get chat history
        $chats = Chat::with(['sender', 'receiver'])
            ->where(function($query) use ($userId) {
                $query->where('sender_id', Auth::id())
                      ->where('receiver_id', $userId);
            })
            ->orWhere(function($query) use ($userId) {
                $query->where('sender_id', $userId)
                      ->where('receiver_id', Auth::id());
            })
            ->orderBy('created_at')
            ->get();

        return view('chat.show', compact('user', 'chats', 'liveStream'));
    }

    public function store(Request $request, $userId)
    {
        $request->validate([
            'message' => 'required_without:media|string|max:1000',
            'media' => 'nullable|file|max:10240', // 10MB max
        ]);

        $mediaUrl = null;
        $mediaType = null;

        if ($request->hasFile('media')) {
            $file = $request->file('media');
            $filename = time() . '_' . $file->getClientOriginalName();

            // Determine media type
            $mimeType = $file->getMimeType();
            if (strpos($mimeType, 'image') !== false) {
                $mediaType = 'image';
                $path = $file->storeAs('chat-images', $filename, 'public');
            } elseif (strpos($mimeType, 'video') !== false) {
                $mediaType = 'video';
                $path = $file->storeAs('chat-videos', $filename, 'public');
            } else {
                $mediaType = 'file';
                $path = $file->storeAs('chat-files', $filename, 'public');
            }

            $mediaUrl = $path;
        }

        $chat = Chat::create([
            'sender_id' => Auth::id(),
            'receiver_id' => $userId,
            'message' => $request->message ?? '',
            'media_url' => $mediaUrl,
            'media_type' => $mediaType
        ]);

        // Load sender and receiver for the response
        $chat->load(['sender', 'receiver']);

        // Broadcast the new message
        broadcast(new NewChatMessage($chat))->toOthers();

        // Return JSON response for AJAX requests
        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'chat' => [
                    'id' => $chat->id,
                    'message' => $chat->message,
                    'sender_id' => $chat->sender_id,
                    'receiver_id' => $chat->receiver_id,
                    'media_url' => $chat->media_url,
                    'media_type' => $chat->media_type,
                    'created_at' => $chat->created_at->diffForHumans(),
                    'sender' => [
                        'id' => $chat->sender->id,
                        'username' => $chat->sender->username,
                        'name' => $chat->sender->name
                    ]
                ]
            ]);
        }

        return back();
    }

    /**
     * Get unread message count for current user
     */
    public function getUnreadCount()
    {
        $unreadCount = Chat::where('receiver_id', Auth::id())
            ->whereNull('read_at')
            ->count();

        return response()->json([
            'success' => true,
            'unread_count' => $unreadCount
        ]);
    }

    /**
     * Mark all messages as read for a specific user
     */
    public function markAsRead($userId)
    {
        $updated = Chat::where('sender_id', $userId)
            ->where('receiver_id', Auth::id())
            ->whereNull('read_at')
            ->update(['read_at' => now()]);

        return response()->json([
            'success' => true,
            'messages_marked' => $updated
        ]);
    }

    /**
     * Get online users (those currently live streaming)
     */
    public function getOnlineUsers()
    {
        $onlineUsers = DB::select("
            SELECT DISTINCT
                u.id,
                u.username,
                u.name,
                u.is_verified,
                up.photo,
                ls.title as stream_title,
                ls.viewer_count,
                'live' as status
            FROM users u
            LEFT JOIN user_profiles up ON u.id = up.user_id
            JOIN live_streams ls ON u.id = ls.user_id
            WHERE ls.is_live = 1 AND u.id != ?
            ORDER BY ls.viewer_count DESC, u.username ASC
        ", [Auth::id()]);

        return response()->json([
            'success' => true,
            'online_users' => $onlineUsers
        ]);
    }

    /**
     * Search users for chat
     */
    public function searchUsers(Request $request)
    {
        $query = $request->get('q', '');
        
        if (strlen($query) < 2) {
            return response()->json([
                'success' => false,
                'message' => 'Query must be at least 2 characters'
            ]);
        }

        $users = User::with('profile')
            ->where('id', '!=', Auth::id())
            ->where(function($q) use ($query) {
                $q->where('username', 'LIKE', "%{$query}%")
                  ->orWhere('name', 'LIKE', "%{$query}%");
            })
            ->select('id', 'username', 'name', 'is_verified')
            ->limit(20)
            ->get();

        // Check which users are currently live
        $liveUserIds = \App\Models\LiveStream::where('is_live', true)
            ->pluck('user_id')
            ->toArray();

        $results = $users->map(function($user) use ($liveUserIds) {
            return [
                'id' => $user->id,
                'username' => $user->username,
                'name' => $user->name,
                'is_verified' => $user->is_verified,
                'photo' => $user->profile?->photo ? asset('storage/' . $user->profile->photo) : null,
                'is_live' => in_array($user->id, $liveUserIds)
            ];
        });

        return response()->json([
            'success' => true,
            'users' => $results
        ]);
    }
}
