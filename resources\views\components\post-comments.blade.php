<div class="post-comments" id="comments-section-{{ $post->id }}">
    <h3>Comments ({{ $post->comments->count() }})</h3>
    
    <form class="comment-form" id="comment-form-{{ $post->id }}">
        @csrf
        <img src="{{ Auth::user()->profile && Auth::user()->profile->photo ? asset('storage/' . Auth::user()->profile->photo) : asset('images/default-avatar.png') }}" alt="{{ Auth::user()->username }}" class="comment-avatar">
        <div class="comment-input-container">
            <input type="text" name="content" placeholder="Add a comment..." class="comment-input" required>
            <button type="submit" class="comment-submit">Post</button>
        </div>
    </form>
    
    <div class="comments-list" id="comments-list-{{ $post->id }}">
        @foreach($post->comments->sortByDesc('created_at') as $comment)
            <div class="comment-item" id="comment-{{ $comment->id }}">
                <img src="{{ $comment->user->profile && $comment->user->profile->photo ? asset('storage/' . $comment->user->profile->photo) : asset('images/default-avatar.png') }}" alt="{{ $comment->user->username }}" class="comment-avatar">
                <div class="comment-content">
                    <div class="comment-header">
                        <span class="comment-username">{{ $comment->user->username }}</span>
                        <span class="comment-time" title="{{ $comment->created_at }}">
                            {{ $comment->created_at->diffForHumans() }}
                            @if($comment->created_at != $comment->updated_at)
                                (edited)
                            @endif
                        </span>
                    </div>
                    <div class="comment-text" id="comment-text-{{ $comment->id }}">
                        {{ $comment->content }}
                    </div>
                    
                    <form class="edit-form" id="edit-form-{{ $comment->id }}" style="display: none;">
                        <textarea class="edit-input" name="content">{{ $comment->content }}</textarea>
                        <div class="edit-actions">
                            <button type="submit" class="save-btn">Save</button>
                            <button type="button" class="cancel-btn">Cancel</button>
                        </div>
                    </form>

                    <div class="comment-actions">
                        @if($comment->can_edit)
                            <button class="edit-btn" data-comment-id="{{ $comment->id }}">Edit</button>
                        @endif
                        @if($comment->can_delete)
                            <button class="delete-btn" data-comment-id="{{ $comment->id }}">Delete</button>
                        @endif
                        <button class="reply-btn" data-comment-id="{{ $comment->id }}">Reply</button>
                    </div>
                </div>
            </div>
            
            <!-- Replies section -->
            <div class="replies" id="replies-{{ $comment->id }}">
                @foreach($comment->replies as $reply)
                    <!-- Similar structure as parent comment -->
                @endforeach
            </div>
        @endforeach
    </div>
</div>

<style>
    .post-comments {
        padding: 15px 0;
        border-top: 1px solid #333;
    }
    
    .comment-form {
        display: flex;
        margin-bottom: 20px;
        gap: 10px;
    }
    
    .comment-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
    }
    
    .comment-input-container {
        flex: 1;
        display: flex;
        background: #222;
        border-radius: 20px;
        overflow: hidden;
    }
    
    .comment-input {
        flex: 1;
        border: none;
        background: transparent;
        padding: 10px 15px;
        color: white;
        outline: none;
    }
    
    .comment-submit {
        background: #FFD700;
        color: black;
        border: none;
        padding: 0 15px;
        cursor: pointer;
        font-weight: 500;
    }
    
    .comments-list {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }
    
    .comment-item {
        display: flex;
        gap: 10px;
    }
    
    .comment-content {
        flex: 1;
    }
    
    .comment-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;
    }
    
    .comment-username {
        font-weight: 500;
    }
    
    .comment-time {
        font-size: 12px;
        color: #999;
    }
    
    .comment-text {
        margin-bottom: 5px;
    }
    
    .edit-form {
        margin-top: 8px;
    }
    
    .edit-input {
        width: 100%;
        min-height: 60px;
        padding: 8px;
        border: 1px solid #444;
        border-radius: 4px;
        background: #222;
        color: white;
        margin-bottom: 8px;
    }
    
    .edit-actions {
        display: flex;
        gap: 8px;
    }
    
    .edit-actions button {
        padding: 4px 12px;
        border-radius: 4px;
        border: none;
        cursor: pointer;
    }
    
    .save-btn {
        background: #FFD700;
        color: black;
    }
    
    .cancel-btn {
        background: #444;
        color: white;
    }
    
    .comment-actions {
        margin-top: 8px;
        display: flex;
        gap: 12px;
    }
    
    .comment-actions button {
        background: none;
        border: none;
        color: #888;
        font-size: 12px;
        cursor: pointer;
        padding: 0;
    }
    
    .comment-actions button:hover {
        color: #FFD700;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get the post ID from the comments section
        const postId = {{ $post->id }};
        
        // Handle comment form submission
        const commentForm = document.getElementById(`comment-form-${postId}`);
        commentForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const input = this.querySelector('.comment-input');
            const content = input.value.trim();
            
            if (!content) return;
            
            // Create FormData object
            const formData = new FormData();
            formData.append('content', content);
            formData.append('_token', document.querySelector('meta[name="csrf-token"]').content);
            
            // Send the comment to the server
            fetch(`/posts/${postId}/comments`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Clear the input
                    input.value = '';
                    
                    // Create new comment element
                    const comment = data.comment;
                    const commentsList = document.getElementById(`comments-list-${postId}`);
                    
                    // Create the comment HTML
                    const commentHtml = `
                        <div class="comment-item" id="comment-${comment.id}">
                            <img src="${comment.user.profile ? comment.user.profile.photo ? '/storage/' + comment.user.profile.photo : '/images/default-avatar.png' : '/images/default-avatar.png'}" 
                                 alt="${comment.user.username}" class="comment-avatar">
                            <div class="comment-content">
                                <div class="comment-header">
                                    <span class="comment-username">${comment.user.username}</span>
                                    <span class="comment-time">Just now</span>
                                </div>
                                <div class="comment-text" id="comment-text-${comment.id}">
                                    ${comment.content}
                                </div>
                                
                                <form class="edit-form" id="edit-form-${comment.id}" style="display: none;">
                                    <textarea class="edit-input" name="content">${comment.content}</textarea>
                                    <div class="edit-actions">
                                        <button type="submit" class="save-btn">Save</button>
                                        <button type="button" class="cancel-btn">Cancel</button>
                                    </div>
                                </form>
                                
                                <div class="comment-actions">
                                    <button class="edit-btn" data-comment-id="${comment.id}">Edit</button>
                                    <button class="delete-btn" data-comment-id="${comment.id}">Delete</button>
                                    <button class="reply-btn" data-comment-id="${comment.id}">Reply</button>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    // Add the new comment to the top of the list
                    commentsList.insertAdjacentHTML('afterbegin', commentHtml);
                    
                    // Update comment count
                    const countElem = document.querySelector(`#comments-section-${postId} h3`);
                    const currentCount = parseInt(countElem.textContent.match(/\d+/)[0]);
                    countElem.textContent = `Comments (${currentCount + 1})`;
                    
                    // Add event listeners to the new comment's buttons
                    addCommentEventListeners(postId);
                } else {
                    alert('Error adding comment: ' + (data.message || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while adding the comment.');
            });
        });
        
        // Function to add event listeners to all comment buttons
        function addCommentEventListeners(postId) {
            const commentsSection = document.getElementById(`comments-section-${postId}`);
            
            // Edit button event listeners
            commentsSection.querySelectorAll('.edit-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const commentId = this.dataset.commentId;
                    const commentText = document.getElementById(`comment-text-${commentId}`);
                    const editForm = document.getElementById(`edit-form-${commentId}`);
                    
                    commentText.style.display = 'none';
                    editForm.style.display = 'block';
                });
            });
            
            // Delete button event listeners
            commentsSection.querySelectorAll('.delete-btn').forEach(button => {
                button.addEventListener('click', handleDeleteComment);
            });
            
            // Edit form submit event listeners
            commentsSection.querySelectorAll('.edit-form').forEach(form => {
                form.addEventListener('submit', handleEditFormSubmit);
            });
            
            // Cancel edit button event listeners
            commentsSection.querySelectorAll('.cancel-btn').forEach(button => {
                button.addEventListener('click', handleCancelEdit);
            });
        }
        
        // Handle delete comment
        function handleDeleteComment() {
            if (confirm('Are you sure you want to delete this comment?')) {
                const commentId = this.dataset.commentId;
                
                fetch(`/comments/${commentId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                        'Accept': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Remove comment from DOM
                        document.getElementById(`comment-${commentId}`).remove();
                        
                        // Update comment count
                        const countElem = document.querySelector(`#comments-section-${postId} h3`);
                        const currentCount = parseInt(countElem.textContent.match(/\d+/)[0]);
                        countElem.textContent = `Comments (${currentCount - 1})`;
                    } else {
                        alert('Error deleting comment: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while deleting the comment.');
                });
            }
        }
        
        // Handle edit form submit
        function handleEditFormSubmit(e) {
            e.preventDefault();
            
            const commentId = this.id.split('-')[2];
            const formData = new FormData(this);
            
            fetch(`/comments/${commentId}`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json',
                    'X-HTTP-Method-Override': 'PUT'
                },
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const commentText = document.getElementById(`comment-text-${commentId}`);
                    commentText.textContent = data.comment.content;
                    commentText.style.display = 'block';
                    this.style.display = 'none';
                } else {
                    alert('Error updating comment: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating the comment.');
            });
        }
        
        // Handle cancel edit
        function handleCancelEdit() {
            const form = this.closest('.edit-form');
            const commentId = form.id.split('-')[2];
            const commentText = document.getElementById(`comment-text-${commentId}`);
            
            form.style.display = 'none';
            commentText.style.display = 'block';
        }
        
        // Initialize event listeners for existing comments
        addCommentEventListeners(postId);
    });
</script> 