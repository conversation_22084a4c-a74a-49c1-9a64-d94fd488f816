<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    @auth
    <meta name="user-id" content="{{ auth()->id() }}">
    @endauth
    <title>{{ config('app.name', 'InstaPWA') }}</title>
    <!-- Include Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="{{ asset('css/live.css') }}" rel="stylesheet">
    @stack('styles')

    <!-- Scripts -->
    <script src="https://cdn.socket.io/4.7.4/socket.io.min.js"></script>
    @vite(['resources/css/app.css', 'resources/js/app.js', 'resources/js/livestream-realtime.js'])


    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            background: #000;
            color: white;
            min-height: 100vh;
        }

        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: #111;
            position: sticky;
            top: 0;
            z-index: 1000;
            border-bottom: 1px solid #222;
        }

        .navbar-brand {
            color: white;
            font-size: 24px;
            font-weight: bold;
            text-decoration: none;
        }

        .navbar-menu {
            position: relative;
        }

        .navbar-icon {
            color: white;
            font-size: 24px;
            cursor: pointer;
            margin-left: 5px;
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: #222;
            border-radius: 5px;
            width: 200px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.5);
            display: none;
            z-index: 1001;
        }

        .dropdown-menu.show {
            display: block;
        }

        .dropdown-item {
            padding: 12px 15px;
            color: white;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: background-color 0.2s;
        }

        .dropdown-item:hover {
            background-color: #333;
        }

        .dropdown-item i {
            width: 20px;
            text-align: center;
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #FF0000;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: #111;
            display: flex;
            justify-content: space-around;
            padding: 10px 0;
            border-top: 1px solid #222;
            z-index: 1000;
        }

        .nav-item {
            color: white;
            text-decoration: none;
            font-size: 22px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .nav-item span {
            font-size: 12px;
            margin-top: 2px;
        }

        .container {
            padding-bottom: 70px; /* Add padding to account for bottom nav */
        }

        .create-menu-overlay {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: flex-end;
            z-index: 1000;
            backdrop-filter: blur(5px);
            height: 100%;
            transition: all 0.3s ease;
            opacity: 0;
        }

        .create-menu {
            background: #222;
            border-radius: 15px 15px 0 0;
            width: 100%;
            padding: 20px 0;
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }

        .create-menu.active {
            transform: translateY(0);
        }

        .create-menu-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            padding: 10px 20px;
        }

        .create-menu-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            color: white;
            text-decoration: none;
            padding: 15px 5px;
        }

        .create-menu-item i {
            font-size: 24px;
            margin-bottom: 8px;
            width: 50px;
            height: 50px;
            background: #333;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .create-menu-item span {
            font-size: 14px;
        }

        /* Dots indicator */
        .menu-dots {
            display: flex;
            justify-content: center;
            gap: 5px;
            margin-top: 20px;
        }

        .menu-dot {
            width: 8px;
            height: 8px;
            background: #555;
            border-radius: 50%;
        }

        .menu-dot.active {
            background: #FFD700;
        }

        /* Create button in nav */
        .nav-item.create-button {
            background: #FFD700;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: -20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            z-index: 10;
        }

        .nav-item.create-button i {
            color: black;
            font-size: 24px;
        }

        .love-balance-display {
            display: flex;
            align-items: center;
            gap: 5px;
            background: #222;
            padding: 5px 10px;
            border-radius: 15px;
            margin-right: 15px;
        }

        .love-balance-display i {
            color: #ff0066;
        }

        .love-balance {
            font-weight: bold;
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <a href="{{ route('home') }}" class="navbar-brand">InstaPWA</a>
        <div class="navbar-menu">
            <div style="position: relative; display: inline-block;">
                <i class="fas fa-bars navbar-icon" id="menuToggle"></i>
                @if(Auth::user()->unreadNotifications()->count() > 0)
                    <div class="notification-badge">{{ Auth::user()->unreadNotifications()->count() }}</div>
                @endif
            </div>
            <div class="dropdown-menu" id="dropdownMenu">
                <a href="{{ route('notifications') }}" class="dropdown-item">
                    <i class="fas fa-bell"></i> Notifications
                    @if(Auth::user()->unreadNotifications()->count() > 0)
                        <span class="notification-badge">{{ Auth::user()->unreadNotifications()->count() }}</span>
                    @endif
                </a>
                <a href="{{ route('chat.index') }}" class="dropdown-item">
                    <i class="fas fa-comments"></i> Chat
                </a>
                <a href="{{ route('create.live') }}" class="dropdown-item">
                    <i class="fas fa-broadcast-tower"></i> Live Stream
                </a>
                <a href="{{ route('audio.room') }}" class="dropdown-item">
                    <i class="fas fa-microphone"></i> Audio Room
                </a>
                <a href="{{ route('camp.index') }}" class="dropdown-item">
                    <i class="fas fa-campground"></i> Camp
                </a>
                <div style="border-top: 1px solid #333; margin: 5px 0;"></div>
                <a href="{{ route('profile') }}" class="dropdown-item">
                    <i class="fas fa-user"></i> Profile
                </a>
                <a href="{{ route('insta.plus') }}" class="dropdown-item">
                    <i class="fas fa-star"></i> InstaPWA Plus
                </a>
                <div class="love-balance-display">
                    <i class="fas fa-heart"></i>
                    <span class="love-balance">{{ Auth::user()->loveWallet ? Auth::user()->loveWallet->love_balance : 0 }}</span>
                </div>
                <form method="POST" action="{{ route('logout') }}">
                    @csrf
                    <button type="submit" class="dropdown-item" style="width: 100%; text-align: left; background: none; border: none; cursor: pointer; font-size: inherit;">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </button>
                </form>
            </div>
        </div>
    </nav>

    <div class="container">
        @yield('content')
    </div>

    <div class="create-menu-overlay" id="createMenuOverlay">
        <div class="create-menu" id="createMenu">
            <div class="create-menu-grid">
                <a href="{{ route('create.post') }}" class="create-menu-item">
                    <i class="fas fa-list"></i>
                    <span>Post</span>
                </a>
                <a href="{{ route('create.story') }}" class="create-menu-item">
                    <i class="fas fa-clock"></i>
                    <span>Story</span>
                </a>
                <a href="{{ route('create.live') }}" class="create-menu-item">
                    <i class="fas fa-broadcast-tower"></i>
                    <span>Livestream</span>
                </a>
                <a href="{{ route('create.reel') }}" class="create-menu-item">
                    <i class="fas fa-film"></i>
                    <span>Reel</span>
                </a>
                <a href="{{ route('audio.room') }}" class="create-menu-item">
                    <i class="fas fa-microphone"></i>
                    <span>Audio Room</span>
                </a>
                <a href="{{ route('insta.plus') }}" class="create-menu-item">
                    <i class="fas fa-plus-circle"></i>
                    <span>Insta Plus</span>
                </a>
            </div>
            <div class="menu-dots">
                <div class="menu-dot active"></div>
                <div class="menu-dot"></div>
            </div>
        </div>
    </div>

    <div class="bottom-nav">
        <a href="{{ route('home') }}" class="nav-item {{ Request::routeIs('home') ? 'active' : '' }}">
            <i class="fas fa-home"></i>
            <span>Home</span>
        </a>
        <a href="{{ route('search') }}" class="nav-item {{ Request::routeIs('search') ? 'active' : '' }}">
            <i class="fas fa-search"></i>
            <span>Search</span>
        </a>
        <div class="nav-item create-button" id="createButton">
            <i class="fas fa-plus"></i>
        </div>
        <a href="{{ route('user-panel.love-history')}}" class="nav-item {{ Request::routeIs('notifications') ? 'active' : '' }}">
            <i class="fas fa-heart"></i>
            <span>Activity</span>
        </a>

        <a href="{{ route('profile') }}" class="nav-item {{ Request::routeIs('profile') ? 'active' : '' }}">
            <i class="fas fa-user"></i>
            <span>Profile</span>
        </a>
        <a href="{{ route('stream.index') }}" class="nav-item">
            <i class="fas fa-broadcast-tower"></i>
            <span>Live</span>
        </a>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const menuToggle = document.getElementById('menuToggle');
            const dropdownMenu = document.getElementById('dropdownMenu');

            menuToggle.addEventListener('click', function() {
                dropdownMenu.classList.toggle('show');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(event) {
                const isClickInsideMenu = dropdownMenu.contains(event.target);
                const isClickOnToggle = menuToggle.contains(event.target);

                if (!isClickInsideMenu && !isClickOnToggle && dropdownMenu.classList.contains('show')) {
                    dropdownMenu.classList.remove('show');
                }
            });

            const createButton = document.getElementById('createButton');
            const createMenuOverlay = document.getElementById('createMenuOverlay');
            const createMenu = document.getElementById('createMenu');

            // Function to open the create menu
            function openCreateMenu() {
                createMenuOverlay.style.display = 'flex';
                // Trigger reflow to enable transitions
                void createMenuOverlay.offsetWidth;
                createMenuOverlay.style.opacity = '1';
                createMenu.classList.add('active');
                document.body.style.overflow = 'hidden';
            }

            // Function to close the create menu
            function closeCreateMenu() {
                createMenuOverlay.style.opacity = '0';
                createMenu.classList.remove('active');
                setTimeout(() => {
                    createMenuOverlay.style.display = 'none';
                    document.body.style.overflow = 'auto';
                }, 300); // Match transition duration
            }

            // Open menu when clicking the create button
            createButton.addEventListener('click', openCreateMenu);

            // Close menu when clicking outside the menu
            createMenuOverlay.addEventListener('click', function(e) {
                if (e.target === createMenuOverlay) {
                    closeCreateMenu();
                }
            });

            // Close menu when pressing Escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && createMenuOverlay.style.display === 'flex') {
                    closeCreateMenu();
                }
            });

            // Add touch swipe down to close
            let touchStartY = 0;
            let touchEndY = 0;

            createMenu.addEventListener('touchstart', function(e) {
                touchStartY = e.changedTouches[0].screenY;
            }, false);

            createMenu.addEventListener('touchend', function(e) {
                touchEndY = e.changedTouches[0].screenY;
                if (touchEndY - touchStartY > 50) { // Swipe down
                    closeCreateMenu();
                }
            }, false);
        });
    </script>

    <!-- For profile images -->
    <img src="{{ auth()->user()->avatar_url }}" class="profile-avatar">

    <!-- Real-time Notifications -->
    @auth

    <script>
        // Initialize Pusher for real-time notifications

        function showLiveStreamNotification(streamData) {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = 'live-stream-notification';
            notification.innerHTML = `
                <div class="notification-content">
                    <img src="${streamData.user.avatar}" alt="${streamData.user.name}" class="notification-avatar">
                    <div class="notification-text">
                        <strong>${streamData.user.name}</strong> started a live stream!
                        <div class="notification-title">${streamData.title}</div>
                    </div>
                    <span class="live-badge">LIVE</span>
                </div>
                <div class="notification-actions">
                    <button onclick="joinStream('${streamData.stream_url}')" class="join-btn">Join</button>
                    <button onclick="dismissNotification(this)" class="dismiss-btn">×</button>
                </div>
            `;

            // Add styles
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 16px;
                border-radius: 12px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
                z-index: 10000;
                max-width: 350px;
                transform: translateX(100%);
                transition: transform 0.4s ease;
                cursor: pointer;
            `;

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // Auto remove after 8 seconds
            setTimeout(() => {
                dismissNotification(notification);
            }, 8000);

            // Play notification sound
            try {
                const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmIdBiyH0fPTgjMGHm7A7+OZSA0PVqzn77BdGAg+ltryxnkpBSl+zPLaizsIGGS57OORTgwOUarm7r5kHgU6jdXzzn0vBSJ2xe/eizEIHWq+8OSZVQ0LTaLh8bllHgg2jdj0xXkpBSl+yPDUfzwIFGG76OZJTQ0PVqzl7a5YGgk7k9n0v2seBS16ye7dizEIHWq+8OSZVQ0LTaLh8blsHgg');
                audio.volume = 0.3;
                audio.play().catch(() => {});
            } catch (e) {}
        }

        function joinStream(streamUrl) {
            window.open(streamUrl, '_blank');
        }

        function dismissNotification(element) {
            const notification = element.closest ? element.closest('.live-stream-notification') : element;
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                notification.remove();
            }, 400);
        }

        // Add CSS for notification content
        const notificationStyles = document.createElement('style');
        notificationStyles.textContent = `
            .notification-content {
                display: flex;
                align-items: center;
                gap: 12px;
                margin-bottom: 12px;
            }
            .notification-avatar {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                border: 2px solid rgba(255, 255, 255, 0.3);
            }
            .notification-text strong {
                display: block;
                font-size: 14px;
                margin-bottom: 4px;
            }
            .notification-title {
                font-size: 12px;
                opacity: 0.9;
            }
            .live-badge {
                background: #ff3040;
                padding: 2px 8px;
                border-radius: 12px;
                font-size: 12px;
                font-weight: 600;
                margin-left: auto;
                animation: pulse 2s infinite;
            }
            .notification-actions {
                display: flex;
                gap: 8px;
            }
            .join-btn, .dismiss-btn {
                padding: 6px 12px;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                font-size: 12px;
                transition: all 0.2s;
            }
            .join-btn {
                background: rgba(255, 255, 255, 0.2);
                color: white;
                flex: 1;
            }
            .join-btn:hover {
                background: rgba(255, 255, 255, 0.3);
            }
            .dismiss-btn {
                background: rgba(255, 255, 255, 0.1);
                color: white;
                width: 28px;
                height: 28px;
                padding: 0;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .dismiss-btn:hover {
                background: rgba(255, 255, 255, 0.2);
            }
            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.7; }
            }
        `;
        document.head.appendChild(notificationStyles);
    </script>
    @endauth

    @stack('scripts')

</body>
</html>
