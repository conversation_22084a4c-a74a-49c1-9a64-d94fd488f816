<?php

namespace App\Http\Controllers;

use App\Models\Post;
use App\Models\PostMedia;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class PostController extends Controller
{
    public function store(Request $request)
    {
        try {
            // Validate the request
            $validator = Validator::make($request->all(), [
                'caption' => 'nullable|string|max:2200',
                'media' => 'required|file|mimes:jpeg,png,jpg,gif,mp4,mov|max:10240',
                'visibility' => 'nullable|in:public,subscribers',
                'is_premium' => 'nullable|boolean',
                'post_type' => 'nullable|string|in:post,story,reel'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => $validator->errors()->first()
                ], 422);
            }
            
            $user = Auth::user();
            
            // If post is set to subscribers-only but user is not a creator
            if ($request->visibility === 'subscribers' && !$user->is_creator) {
                return response()->json([
                    'success' => false,
                    'message' => 'You need to be a creator to post subscriber-only content.'
                ], 403);
            }
            
            // Handle file upload
            if ($request->hasFile('media')) {
                $file = $request->file('media');
                $mediaType = substr($file->getMimeType(), 0, 5) === 'video' ? 'video' : 'image';
                $mediaUrl = $file->store('post-media', 'public');

                
                
                // Create post
                $post = Post::create([
                    'user_id' => $user->id,
                    'caption' => $request->caption,
                    'media_url' => $mediaUrl,
                    'media_type' => $mediaType,
                    'visibility' => $request->visibility ?? 'public',
                    'is_premium' => $request->has('is_premium') && $request->is_premium ? true : false,
                    'type' => $request->post_type ?? 'post'
                ]);
                
                return response()->json([
                    'success' => true,
                    'message' => 'Post created successfully!',
                    'redirect' => route('home'),
                    'post' => $post
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'No media file was uploaded.'
                ], 422);
            }
        } catch (\Exception $e) {
            // Log the error for debugging
            Log::error('Post creation error: ' . $e->getMessage());
            Log::error($e->getTraceAsString());
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while creating your post: ' . $e->getMessage()
            ], 500);
        }
    }

    public function show($id)
    {
        $post = Post::with(['user.profile', 'comments.user', 'loves'])->findOrFail($id);
        $user = Auth::user();
        
        // Check if post is visible to the current user
        if ($post->visibility === 'subscribers' && $post->user_id !== $user->id) {
            // If not subscribed to creator, redirect
            if (!$user->isSubscribedTo($post->user_id)) {
                return redirect()->route('creator.subscribe.show', $post->user_id)
                    ->with('error', 'This post is only available to subscribers.');
            }
        }
        
        
        return view('posts.show', compact('post'));
    }

    /**
     * Show post edit form
     */
    public function edit($id)
    {
        $post = Post::findOrFail($id);
        
        // Check if user owns this post
        if ($post->user_id !== Auth::id()) {
            return redirect()->route('home')->with('error', 'You cannot edit this post.');
        }
        
        return view('posts.edit', compact('post'));
    }

    /**
     * Update a post
     */
    public function update(Request $request, $id)
    {
        $post = Post::findOrFail($id);
        
        // Check if user owns this post
        if ($post->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'You cannot edit this post.'
            ], 403);
        }
        
        $request->validate([
            'caption' => 'nullable|string|max:2200',
            'visibility' => 'nullable|in:public,subscribers'
        ]);
        
        // Update post
        $post->update([
            'caption' => $request->caption,
            'visibility' => $request->visibility
        ]);
        
        // If user is creator and changing visibility to subscribers-only
        if (Auth::user()->is_creator && $request->visibility === 'subscribers') {
            // Allow updating the premium status
            $post->update([
                'is_premium' => $request->has('is_premium') && $request->is_premium
            ]);
        }
        
        return response()->json([
            'success' => true,
            'message' => 'Post updated successfully',
            'redirect' => route('posts.show', $post->id)
        ]);
    }

    /**
     * Delete a post
     */
    public function destroy($id)
    {
        $post = Post::findOrFail($id);
        
        // Check if user owns this post
        if ($post->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'You cannot delete this post.'
            ], 403);
        }
        
        // Delete the post media file
        if ($post->media_url) {
            Storage::disk('public')->delete($post->media_url);
        }
        
        $post->delete();
        
        return response()->json([
            'success' => true,
            'message' => 'Post deleted successfully',
            'redirect' => route('profile.index')
        ]);
    }
} 