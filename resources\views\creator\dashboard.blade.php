@extends('layouts.app')

@section('content')
<div class="creator-dashboard">
    <div class="dashboard-header">
        <h1>Creator Dashboard</h1>
    </div>
    
    <div class="stats-container">
        <div class="stat-card">
            <div class="stat-value">${{ number_format($pendingBalance, 2) }}</div>
            <div class="stat-label">Pending Balance</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-value">{{ $activeSubscribers }}</div>
            <div class="stat-label">Active Subscribers</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-value">${{ number_format($totalEarned, 2) }}</div>
            <div class="stat-label">Total Earned</div>
        </div>
    </div>
    
    <div class="dashboard-grid">
        <div class="recent-subscribers">
            <div class="section-header">
                <h2>Recent Subscribers</h2>
                <a href="{{ route('creator.subscribers') }}" class="view-all">View All</a>
            </div>
            
            <div class="subscribers-list">
                @forelse($recentSubscribers as $subscription)
                    <div class="subscriber-item">
                        <div class="subscriber-avatar">
                            @if($subscription->subscriber->profile && $subscription->subscriber->profile->photo)
                                <img src="{{ asset('storage/' . $subscription->subscriber->profile->photo) }}" alt="{{ $subscription->subscriber->username }}">
                            @else
                                <div class="avatar-placeholder">{{ substr($subscription->subscriber->username, 0, 1) }}</div>
                            @endif
                        </div>
                        <div class="subscriber-info">
                            <div class="subscriber-name">{{ $subscription->subscriber->username }}</div>
                            <div class="subscription-date">Subscribed {{ Carbon\Carbon::parse($subscription->started_at)->diffForHumans() }}</div>
                        </div>
                        <div class="subscription-price">${{ number_format($subscription->price, 2) }}</div>
                    </div>
                @empty
                    <div class="empty-state">
                        <p>No subscribers yet</p>
                        <p class="empty-tip">Share your profile to attract subscribers</p>
                    </div>
                @endforelse
            </div>
        </div>
        
        <div class="recent-earnings">
            <div class="section-header">
                <h2>Recent Earnings</h2>
                <a href="{{ route('creator.earnings') }}" class="view-all">View All</a>
            </div>
            
            <div class="earnings-list">
                @forelse($recentEarnings as $earning)
                    <div class="earning-item">
                        <div class="earning-source">
                            @if($earning->source === 'subscription')
                                <i class="fas fa-star"></i>
                                <span>Subscription</span>
                            @elseif($earning->source === 'tips')
                                <i class="fas fa-gift"></i>
                                <span>Tip</span>
                            @else
                                <i class="fas fa-heart"></i>
                                <span>Loves</span>
                            @endif
                        </div>
                        <div class="earning-date">{{ Carbon\Carbon::parse($earning->earned_at)->format('M d, Y') }}</div>
                        <div class="earning-amount">${{ number_format($earning->amount, 2) }}</div>
                    </div>
                @empty
                    <div class="empty-state">
                        <p>No earnings yet</p>
                        <p class="empty-tip">Post exclusive content to start earning</p>
                    </div>
                @endforelse
            </div>
        </div>
    </div>
    
    <div class="post-creation">
        <div class="section-header">
            <h2>Create Exclusive Content</h2>
        </div>
        
        <div class="content-options">
            <a href="{{ route('create.post', ['type' => 'exclusive']) }}" class="content-option">
                <i class="fas fa-image"></i>
                <span>Post Photo/Video</span>
            </a>
            
            <a href="{{ route('create.story', ['type' => 'exclusive']) }}" class="content-option">
                <i class="fas fa-film"></i>
                <span>Create Story</span>
            </a>
            
            <a href="{{ route('livestream.create', ['type' => 'exclusive']) }}" class="content-option">
                <i class="fas fa-broadcast-tower"></i>
                <span>Go Live</span>
            </a>
        </div>
    </div>

    <div class="earnings-summary">
        <div class="earnings-card">
            <div class="earnings-icon">
                <i class="fas fa-heart"></i>
            </div>
            <div class="earnings-details">
                <h3>Love Earnings</h3>
                <div class="earnings-amount">${{ number_format(Auth::user()->creator_balance, 2) }}</div>
            </div>
        </div>
        
        <div class="earnings-card">
            <div class="earnings-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="earnings-details">
                <h3>Subscribers</h3>
                <div class="earnings-amount">{{ $subscriberCount }}</div>
            </div>
        </div>
        
        <div class="earnings-card">
            <div class="earnings-icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="earnings-details">
                <h3>Total Loves</h3>
                <div class="earnings-amount">{{ $totalLoves }}</div>
            </div>
        </div>
    </div>
</div>

<style>
    .creator-dashboard {
        padding: 20px;
        color: white;
    }
    
    .dashboard-header h1 {
        color: #FFD700;
        margin-bottom: 20px;
    }
    
    .stats-container {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 15px;
        margin-bottom: 30px;
    }
    
    .stat-card {
        background: rgba(0, 0, 0, 0.3);
        border-radius: 10px;
        padding: 20px;
        text-align: center;
    }
    
    .stat-value {
        font-size: 28px;
        font-weight: bold;
        color: #FFD700;
        margin-bottom: 5px;
    }
    
    .stat-label {
        font-size: 14px;
        opacity: 0.8;
    }
    
    .dashboard-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }
    
    .section-header h2 {
        font-size: 18px;
        color: #FFD700;
    }
    
    .view-all {
        color: white;
        text-decoration: none;
        font-size: 14px;
    }
    
    .subscribers-list, .earnings-list {
        background: rgba(0, 0, 0, 0.3);
        border-radius: 10px;
        overflow: hidden;
    }
    
    .subscriber-item, .earning-item {
        padding: 15px;
        display: flex;
        align-items: center;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .subscriber-item:last-child, .earning-item:last-child {
        border-bottom: none;
    }
    
    .subscriber-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 15px;
    }
    
    .subscriber-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .avatar-placeholder {
        width: 100%;
        height: 100%;
        background: rgba(255, 215, 0, 0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        text-transform: uppercase;
        font-weight: bold;
    }
    
    .subscriber-info {
        flex: 1;
    }
    
    .subscriber-name {
        font-weight: bold;
        margin-bottom: 3px;
    }
    
    .subscription-date {
        font-size: 12px;
        opacity: 0.7;
    }
    
    .subscription-price, .earning-amount {
        font-weight: bold;
        color: #FFD700;
    }
    
    .earning-source {
        display: flex;
        align-items: center;
        width: 120px;
    }
    
    .earning-source i {
        margin-right: 8px;
        color: #FFD700;
    }
    
    .earning-date {
        flex: 1;
        font-size: 14px;
    }
    
    .empty-state {
        padding: 30px;
        text-align: center;
    }
    
    .empty-tip {
        font-size: 14px;
        opacity: 0.7;
        margin-top: 5px;
    }
    
    .content-options {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 15px;
    }
    
    .content-option {
        background: rgba(0, 0, 0, 0.3);
        border-radius: 10px;
        padding: 25px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        color: white;
        transition: all 0.3s ease;
    }
    
    .content-option:hover {
        background: rgba(255, 215, 0, 0.1);
        color: #FFD700;
    }
    
    .content-option i {
        font-size: 24px;
        margin-bottom: 15px;
    }
    
    @media (max-width: 768px) {
        .stats-container {
            grid-template-columns: 1fr;
        }
        
        .dashboard-grid {
            grid-template-columns: 1fr;
        }
        
        .content-options {
            grid-template-columns: 1fr;
        }
    }
</style>
@endsection 