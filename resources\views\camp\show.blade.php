@extends('layouts.app')

@section('content')
<style>
    .camp-container {
        padding: 15px;
    }
    
    .camp-header {
        background-color: #222;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .camp-title {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
    }
    
    .camp-type-badge {
        display: inline-block;
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 12px;
        margin-left: 10px;
    }
    
    .camp-type-private {
        background-color: #FF4136;
        color: white;
    }
    
    .camp-type-public {
        background-color: #2ECC40;
        color: white;
    }
    
    .camp-description {
        color: #BBB;
        margin-bottom: 15px;
    }
    
    .camp-meta {
        display: flex;
        justify-content: space-between;
        color: #999;
        font-size: 14px;
    }
    
    .camp-actions {
        display: flex;
        gap: 10px;
        margin-top: 15px;
    }
    
    .camp-btn {
        padding: 8px 12px;
        border-radius: 5px;
        text-align: center;
        text-decoration: none;
        cursor: pointer;
        font-size: 14px;
        border: none;
    }
    
    .camp-leave-btn {
        background-color: #FF851B;
        color: white;
    }
    
    .camp-delete-btn {
        background-color: #FF4136;
        color: white;
    }
    
    .camp-content {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 20px;
    }
    
    .camp-posts {
        background-color: #222;
        border-radius: 10px;
        padding: 20px;
    }
    
    .post-form {
        margin-bottom: 20px;
    }
    
    .post-textarea {
        width: 100%;
        padding: 10px;
        background: #333;
        border: none;
        border-radius: 5px;
        color: white;
        min-height: 100px;
        margin-bottom: 10px;
    }
    
    .post-submit {
        background-color: #FFD700;
        color: black;
        padding: 8px 15px;
        border-radius: 5px;
        border: none;
        cursor: pointer;
        font-weight: bold;
    }
    
    .post-item {
        border-bottom: 1px solid #333;
        padding: 15px 0;
    }
    
    .post-item:last-child {
        border-bottom: none;
    }
    
    .post-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
    }
    
    .post-author {
        font-weight: bold;
    }
    
    .post-date {
        color: #999;
        font-size: 12px;
    }
    
    .post-content {
        color: #BBB;
    }
    
    .camp-members {
        background-color: #222;
        border-radius: 10px;
        padding: 20px;
    }
    
    .members-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 15px;
    }
    
    .members-list {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }
    
    .member-item {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .member-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #333;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
    }
    
    .member-name {
        flex-grow: 1;
    }
    
    .member-role {
        font-size: 12px;
        color: #999;
    }
</style>

<div class="camp-container">
    @if(session('success'))
        <div style="background-color: #2ECC40; color: white; padding: 10px; border-radius: 5px; margin-bottom: 15px;">
            {{ session('success') }}
        </div>
    @endif
    
    @if(session('error'))
        <div style="background-color: #FF4136; color: white; padding: 10px; border-radius: 5px; margin-bottom: 15px;">
            {{ session('error') }}
        </div>
    @endif
    
    <div class="camp-header">
        <div class="camp-title">
            {{ $camp->name }}
            <span class="camp-type-badge camp-type-{{ $camp->type }}">{{ ucfirst($camp->type) }}</span>
        </div>
        <div class="camp-description">{{ $camp->description }}</div>
        <div class="camp-meta">
            <span>Members: {{ $camp->members_count }}</span>
            <span>Created by: {{ $camp->owner->name }}</span>
            <span>Created: {{ $camp->created_at->diffForHumans() }}</span>
        </div>
        
        <div class="camp-actions">
            @if($camp->isMember(Auth::user()) && $camp->created_by !== Auth::id())
                <form action="{{ route('camps.leave', $camp) }}" method="POST">
                    @csrf
                    <button type="submit" class="camp-btn camp-leave-btn">Leave Camp</button>
                </form>
            @endif
            
            @if($camp->created_by === Auth::id())
                <form action="{{ route('camps.destroy', $camp) }}" method="POST" onsubmit="return confirm('Are you sure you want to delete this camp?');">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="camp-btn camp-delete-btn">Delete Camp</button>
                </form>
            @endif
        </div>
    </div>
    
    <div class="camp-content">
        <div class="camp-posts">
            <h2>Posts</h2>
            
            @if($camp->isMember(Auth::user()))
                <div class="post-form">
                    <form action="{{ route('camps.posts.store', $camp) }}" method="POST">
                        @csrf
                        <textarea name="content" class="post-textarea" placeholder="Write something to share with the camp..." required></textarea>
                        <button type="submit" class="post-submit">Post</button>
                    </form>
                </div>
            @endif
            
            @if($camp->posts->count() > 0)
                <div class="posts-list">
                    @foreach($camp->posts as $post)
                        <div class="post-item">
                            <div class="post-header">
                                <span class="post-author">{{ $post->user->name }}</span>
                                <span class="post-date">{{ $post->created_at->diffForHumans() }}</span>
                            </div>
                            <div class="post-content">{{ $post->content }}</div>
                        </div>
                    @endforeach
                </div>
            @else
                <p>No posts yet. Be the first to post!</p>
            @endif
        </div>
        
        <div class="camp-members">
            <div class="members-title">Members ({{ $camp->members_count }})</div>
            
            <div class="members-list">
                @foreach($camp->members as $member)
                    <div class="member-item">
                        <div class="member-avatar">
                            {{ substr($member->name, 0, 1) }}
                        </div>
                        <div class="member-name">{{ $member->name }}</div>
                        <div class="member-role">
                            @if($camp->created_by === $member->id)
                                Creator
                            @else
                                Member
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
</div>
@endsection 