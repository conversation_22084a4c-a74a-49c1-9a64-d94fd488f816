<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\LoveController;
use App\Http\Controllers\Api\RevenueController;
use App\Http\Controllers\Api\LiveStreamApiController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Live streaming API endpoints
Route::middleware('auth')->group(function () {
    Route::get('/live-streams/active', [LiveStreamApiController::class, 'getActiveLiveStreams']);
    Route::get('/live-streams/recent', [LiveStreamApiController::class, 'getRecentRecordedStreams']);
    Route::get('/live-streams/search', [LiveStreamApiController::class, 'searchStreams']);
    Route::get('/users/{userId}/live-streams', [LiveStreamApiController::class, 'getUserStreams']);
});

// Existing routes
Route::middleware('auth')->group(function () {
    Route::get('/love/stats', [LoveController::class, 'stats']);
    Route::get('/revenue/stats', [RevenueController::class, 'stats']);
});

// Protected API routes
Route::middleware('auth:sanctum')->group(function () {
    // User love endpoints
    Route::get('/user/loves', [LoveController::class, 'getUserLoves']);
    Route::get('/user/love-summary', [LoveController::class, 'getLoveSummary']);
    Route::get('/user/revenue', [RevenueController::class, 'getRevenue']);
    Route::post('/withdraw-request', [RevenueController::class, 'requestWithdrawal']);
}); 