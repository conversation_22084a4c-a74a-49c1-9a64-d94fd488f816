@extends('layouts.app')

@section('content')
<meta name="signaling-server" content="{{ env('SIGNALING_SERVER_URL', 'http://localhost:3000') }}">

<div class="connection-status" id="connection-status">
    <span class="status-indicator"></span>
    <span class="status-text">Connecting...</span>
</div>

@if($stream->is_live)
    <div class="live-container">
        <!-- Streamer View -->
        @if($isStreamer)
            <div class="streamer-overlay">
                <div class="live-badge">
                    <div class="pulsating-ring"></div>
                    LIVE
                </div>
                <div class="viewer-count">
                    <i class="fas fa-eye"></i>
                    <span id="viewerCount">{{ $stream->viewer_count }}</span>
                </div>
                <button class="btn btn-danger end-stream-btn" id="endStreamBtn">
                    <i class="fas fa-stop-circle"></i> End Stream
                </button>
            </div>
            <video id="local-video" 
                    playsinline 
                    muted 
                    style="width: 100%; height: 100%; object-fit: cover; background: #000;"></video>
        @else
            <!-- Viewer View -->
            <video id="remote-video" 
                    playsinline 
                    muted 
                    style="width: 100%; height: 100%; object-fit: cover; background: #000;"></video>
                            <div class="viewer-controls">
                    <button id="unmute-btn" title="Click to unmute audio">
                        <span class="volume-icon">🔊</span>
                        <span class="volume-text">Unmute</span>
                    </button>
                </div>
        @endif

        <!-- Right sidebar with reactions -->
        <div class="sidebar">
            <div class="reaction-buttons">
                <button class="heart-btn" id="send-heart" title="Send Heart">
                    <i class="fas fa-heart"></i>
                </button>
                <button class="share-btn" title="Share Stream">
                    <i class="fas fa-share"></i>
                </button>
            </div>
        </div>

        <!-- Bottom chat section -->
        <div class="comment-section">
            <div class="comments-container" id="chat-messages">
                <div id="commentsList"></div>
            </div>
            <form id="chat-form">
                <input type="text" 
                        id="chat-message" 
                        placeholder="Add a comment..." 
                        maxlength="255"
                        autocomplete="off">
                <button type="submit">Send</button>
            </form>
        </div>
    </div>
    
    <!-- Hearts animation container -->
    <div id="heartsContainer" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none; z-index: 1000;"></div>
@else
    <div class="recording-player">
        <video src="{{ Storage::url($stream->recording_path) }}" controls class="w-100"></video>
    </div>
@endif


@push('styles')
<style>
/* Reset and base styles */
* {
    box-sizing: border-box;
}

body {
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden !important; /* Prevent page scroll */
    height: 100vh !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

/* Hide navbar for full immersive experience */
.navbar {
    display: none !important;
}

/* Override app layout styles */
html {
    height: 100vh !important;
    overflow: hidden !important;
}

main {
    padding: 0 !important;
    margin: 0 !important;
}

.container {
    padding: 0;
    margin: 0;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
}

/* Main live container */
.live-container {
    position: relative;
    background: #000;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
}

/* Video styling */
#local-video, #remote-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    background: #000;
}

/* Top overlay - Live badge and controls */
.streamer-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 20px;
    background: linear-gradient(180deg, rgba(0,0,0,0.6) 0%, transparent 100%);
    display: flex;
    justify-content: space-between;
    z-index: 100;
}

.live-badge {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 59, 48, 0.9);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 0 !important;
} 

.pulsating-ring {
    width: 12px;
    height: 12px;
    background: white;
    border-radius: 50%;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { 
        transform: scale(1); 
        opacity: 1; 
    }
    50% { 
        transform: scale(1.2); 
        opacity: 0.7; 
    }
    100% { 
        transform: scale(1); 
        opacity: 1; 
    }
}

.viewer-count {
    display: flex;
    align-items: center;
    gap: 6px;
    background: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 500;
    font-size: 14px;
    backdrop-filter: blur(10px);
}

.end-stream-btn {
    background: rgba(255, 59, 48, 0.9);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
}

.end-stream-btn:hover {
    background: rgba(255, 59, 48, 1);
    transform: scale(1.05);
}

/* Connection status */
.connection-status {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 8px 16px;
    border-radius: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    display: flex;
    align-items: center;
    gap: 8px;
    z-index: 1000;
    font-size: 12px;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #ffc107;
}

.status-indicator.connected {
    background: #4CAF50;
}

.status-indicator.disconnected {
    background: #f44336;
}

/* Right sidebar - Reactions */
.sidebar {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 20px;
    z-index: 200;
}

.reaction-buttons {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.reaction-buttons button {
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 24px;
    cursor: pointer;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
}

.reaction-buttons button:hover {
    transform: scale(1.1);
    background: rgba(255, 255, 255, 0.3);
}

.heart-btn {
    color: #ff3b30;
    background: rgba(255, 59, 48, 0.2);
}

.heart-btn:hover {
    background: rgba(255, 59, 48, 0.4);
}

.share-btn {
    color: #007AFF;
    background: rgba(0, 122, 255, 0.2);
}

.share-btn:hover {
    background: rgba(0, 122, 255, 0.4);
}

/* Bottom chat section */
.comment-section {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(0deg, rgba(0,0,0,0.9) 0%, rgba(0,0,0,0.6) 50%, transparent 100%);
    padding: 20px;
    padding-bottom: 70px; /* Extra padding at bottom */
    max-height: 45vh;
    display: flex;
    flex-direction: column;
    gap: 16px;
    z-index: 200;
}

.comments-container {
    flex: 1;
    max-height: 200px;
    overflow-y: auto;
    overflow-x: hidden;
    padding-right: 8px;
    margin-bottom: 8px;
    /* Custom scrollbar */
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.comments-container::-webkit-scrollbar {
    width: 4px;
}

.comments-container::-webkit-scrollbar-track {
    background: transparent;
}

.comments-container::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
}

.comments-container::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

#commentsList {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.comment-item {
    background: rgba(0, 0, 0, 0.6);
    padding: 12px 16px;
    border-radius: 20px;
    max-width: 80%;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    animation: slideInFromBottom 0.3s ease-out;
}

@keyframes slideInFromBottom {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.comment-username {
    font-weight: 600;
    color: #ffffff;
    margin-right: 8px;
    font-size: 14px;
}

.comment-text {
    color: #e0e0e0;
    font-size: 14px;
    line-height: 1.4;
}

/* Chat input form */
#chat-form {
    display: flex;
    gap: 12px;
    align-items: center;
    background: rgba(0, 0, 0, 0.8);
    padding: 12px 16px;
    border-radius: 25px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    margin-top: auto; /* Push to bottom */
}

#chat-message {
    flex: 1;
    background: transparent;
    border: none;
    color: white;
    padding: 12px 16px;
    border-radius: 20px;
    font-size: 16px;
    outline: none;
}

#chat-message::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

#chat-form button[type="submit"] {
    background: #007AFF;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 20px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
}

#chat-form button[type="submit"]:hover {
    background: #0056b3;
    transform: scale(1.05);
}

#chat-form button[type="submit"]:active {
    transform: scale(0.98);
}

/* Viewer controls */
.viewer-controls {
    position: fixed;
    top: 50%;
    left: 20px;
    transform: translateY(-50%);
    z-index: 250;
}

.viewer-controls button {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    border: none;
    padding: 12px 16px;
    border-radius: 25px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.viewer-controls button:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: translateY(-50%) scale(1.05);
}

.viewer-controls button:active {
    transform: translateY(-50%) scale(0.98);
}

/* Hearts animation */
#heartsContainer .heart {
    position: absolute;
    font-size: 32px;
    color: #ff3b30;
    animation: floatUp 3s ease-out forwards;
    pointer-events: none;
    filter: drop-shadow(0 0 10px rgba(255, 59, 48, 0.5));
}

@keyframes floatUp {
    0% {
        transform: translateY(0) scale(1) rotate(0deg);
        opacity: 1;
    }
    50% {
        transform: translateY(-50vh) scale(1.2) rotate(180deg);
        opacity: 0.8;
    }
    100% {
        transform: translateY(-100vh) scale(1.5) rotate(360deg);
        opacity: 0;
    }
}

/* Recording player */
.recording-player {
    width: 100vw;
    height: 100vh;
    background: #000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.recording-player video {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* Responsive design */
@media (max-width: 768px) {
    .comment-section {
        max-height: 50vh;
        padding: 16px;
        padding-bottom: 25px;
    }
    
    .comments-container {
        max-height: 150px;
    }
    
    .sidebar {
        right: 16px;
    }
    
    .reaction-buttons button {
        width: 40px;
        height: 40px;
        font-size: 20px;
    }
    
    .streamer-overlay {
        padding: 16px;
    }
    
    .comment-item {
        max-width: 90%;
        padding: 10px 14px;
    }
    
    .viewer-controls {
        left: 16px;
    }
    
    .viewer-controls button {
        padding: 10px 14px;
        font-size: 12px;
    }
    
    .viewer-controls .volume-text {
        display: none; /* Hide text on mobile, show only icon */
    }
}

@media (max-width: 480px) {
    .comment-section {
        max-height: 55vh;
        padding: 12px;
        padding-bottom: 80px;
    }
    
    .comments-container {
        max-height: 120px;
    }
    
    #chat-message {
        font-size: 14px;
        padding: 10px 14px;
    }
    
    #chat-form button[type="submit"] {
        padding: 10px 16px;
        font-size: 12px;
    }
    
    #chat-form {
        padding: 10px 14px;
    }
    
    .viewer-controls {
        left: 12px;
    }
    
    .viewer-controls button {
        padding: 8px 12px;
        font-size: 11px;
    }
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', async function() {
    console.log('=== LIVESTREAM SCRIPT STARTING ===');
    
    const streamId = '{{ $stream->id }}';
    const isStreamer = {{ $isStreamer ? 'true' : 'false' }};
    const iceServers = {!! json_encode($iceServers) !!};
    
    // Get current user data safely
    const currentUser = @json([
        'id' => $currentUser?->id ?? null,
        'username' => $currentUser?->username ?? 'Guest'
    ]);
    
    console.log('Variables loaded:', {
        streamId: streamId,
        isStreamer: isStreamer,
        currentUser: currentUser
    });
    
    // Store in window scope for chat access
    window.currentUser = currentUser;
    
    // Connection status elements
    const statusIndicator = document.querySelector('.status-indicator');
    const statusText = document.querySelector('.status-text');
    
    // Update connection status
    function updateConnectionStatus(status, message, hide = false) {
        if (hide) {
            statusIndicator.parentElement.style.display = 'none';
            statusText.parentElement.style.display = 'none';
        } else {
            statusIndicator.parentElement.style.display = 'block';
            statusText.parentElement.style.display = 'block';
        }
        statusIndicator.className = 'status-indicator ' + status;
        statusText.textContent = message;
        console.log('Connection status:', message);
    }

    // Initialize livestream
    console.log('Creating LivestreamRealtime instance...');
    const livestream = new LivestreamRealtime(streamId, isStreamer, iceServers);
    
    // Enhanced video handling
    const localVideo = document.getElementById('local-video');
    const remoteVideo = document.getElementById('remote-video');
    const unmuteBtn = document.getElementById('unmute-btn');

    // Safe video play function
    function safeVideoPlay(video, videoName) {
        if (!video) return;
        
        video.play().then(() => {
            console.log(`${videoName} started playing successfully`);
        }).catch((error) => {
            console.warn(`${videoName} autoplay failed:`, error.message);
            console.log(`User interaction required for ${videoName}`);
        });
    }

    // Set up video controls
    if (remoteVideo && unmuteBtn) {
        unmuteBtn.onclick = () => {
            remoteVideo.muted = !remoteVideo.muted;
            const volumeIcon = unmuteBtn.querySelector('.volume-icon');
            const volumeText = unmuteBtn.querySelector('.volume-text');
            
            if (remoteVideo.muted) {
                volumeIcon.textContent = '🔊';
                volumeText.textContent = 'Unmute';
                unmuteBtn.title = 'Click to unmute audio';
            } else {
                volumeIcon.textContent = '🔇';
                volumeText.textContent = 'Mute';
                unmuteBtn.title = 'Click to mute audio';
            }
            
            safeVideoPlay(remoteVideo, 'Remote Video');
        };
    }

    // Start streaming or join stream based on role
    try {
        if (isStreamer) {
            console.log('Starting stream...');
            await livestream.startStream();
            updateConnectionStatus('connected', 'Live', true);
        } else {
            console.log('Joining stream...');
            await livestream.joinStream();
            updateConnectionStatus('connected', 'Live');
        }
    } catch (error) {
        console.error('Error initializing stream:', error);
        updateConnectionStatus('disconnected', 'Connection lost');
    }

    // Enhanced chat form submission with user data
    const chatForm = document.getElementById('chat-form');
    const chatInput = document.getElementById('chat-message');
    const chatMessages = document.getElementById('chat-messages');

    if (chatForm) {
        console.log('Setting up chat form...');
        chatForm.addEventListener('submit', (e) => {
            e.preventDefault();
            const message = chatInput.value.trim();
            if (!message) return;

            console.log('Sending chat message:', {
                message: message,
                user: currentUser
            });

            // Send comment with user data
            const username = currentUser.username || 'Anonymous';
            const userId = currentUser.id || 0;
            
            livestream.sendComment(message, username, userId);
            chatInput.value = '';
        });
        console.log('Chat form setup complete');
    }

    // Enhanced heart reactions with user data
    const heartButton = document.getElementById('send-heart');
    if (heartButton) {
        heartButton.addEventListener('click', () => {
            const username = currentUser.username || 'Anonymous';
            const userId = currentUser.id || 0;
            
            console.log('Sending heart reaction from:', username);
            livestream.sendHeart(username, userId);
        });
    }

    // Handle end stream button
    const endStreamBtn = document.getElementById('endStreamBtn');
    if (endStreamBtn) {
        endStreamBtn.addEventListener('click', async () => {
            try {
                console.log('Ending stream...');
                await livestream.endStream();
                
                const response = await fetch(`/live/${streamId}/end`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                });

                const result = await response.json();
                if (result.success) {
                    window.location.href = result.redirect_url;
                }
            } catch (error) {
                console.error('Error ending stream:', error);
            }
        });
    }

    // Listen for connection events
    livestream.socket.on('connect', () => {
        updateConnectionStatus('connected', 'Live');
    });

    livestream.socket.on('disconnect', () => {
        updateConnectionStatus('disconnected', 'Offline');
    });

    livestream.socket.on('connection-success', (data) => {
        console.log('Connection success:', data);
        updateConnectionStatus('connected', 'Live');
    });

    livestream.socket.on('connect_error', (error) => {
        console.error('Connection error:', error);
        updateConnectionStatus('disconnected', 'Connection error');
    });

    console.log('=== LIVESTREAM INITIALIZATION COMPLETE ===');
});
</script>
@endpush
@endsection
