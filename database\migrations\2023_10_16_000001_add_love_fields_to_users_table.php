<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            if (!Schema::hasColumn('users', 'love_balance')) {
                $table->integer('love_balance')->default(10)->after('remember_token');
            }
            
            if (!Schema::hasColumn('users', 'love_revenue')) {
                $table->decimal('love_revenue', 10, 2)->default(0)->after('love_balance');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            if (Schema::hasColumn('users', 'love_balance')) {
                $table->dropColumn('love_balance');
            }
            
            if (Schema::hasColumn('users', 'love_revenue')) {
                $table->dropColumn('love_revenue');
            }
        });
    }
}; 