-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.2
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Jun 23, 2025 at 06:39 PM
-- Server version: 8.4.3
-- PHP Version: 8.3.16

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `instapwa`
--

-- --------------------------------------------------------

--
-- Table structure for table `cache`
--

CREATE TABLE `cache` (
  `key` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `cache_locks`
--

CREATE TABLE `cache_locks` (
  `key` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `owner` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `camps`
--

CREATE TABLE `camps` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `image` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `created_by` bigint UNSIGNED NOT NULL,
  `type` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `camps`
--

INSERT INTO `camps` (`id`, `name`, `image`, `description`, `created_by`, `type`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 'Camp 1', NULL, 'This is a public camp for testing purposes.', 5, 'public', '2025-04-17 05:18:33', '2025-04-17 05:18:33', NULL),
(2, 'Camp 2', NULL, 'This is a private camp for testing purposes.', 2, 'private', '2025-04-17 05:18:33', '2025-04-17 05:18:33', NULL),
(3, 'Camp 3', NULL, 'This is a public camp for testing purposes.', 6, 'public', '2025-04-17 05:18:33', '2025-04-17 05:18:33', NULL),
(4, 'Camp 4', NULL, 'This is a public camp for testing purposes.', 2, 'public', '2025-04-17 05:18:33', '2025-04-17 05:18:33', NULL),
(5, 'Camp 5', NULL, 'This is a private camp for testing purposes.', 3, 'private', '2025-04-17 05:18:33', '2025-04-17 05:18:33', NULL),
(6, 'Camp 6', NULL, 'This is a private camp for testing purposes.', 6, 'private', '2025-04-17 05:18:34', '2025-04-17 05:18:34', NULL),
(7, 'Camp 7', NULL, 'This is a private camp for testing purposes.', 5, 'private', '2025-04-17 05:18:34', '2025-04-17 05:18:34', NULL),
(8, 'Camp 8', NULL, 'This is a public camp for testing purposes.', 4, 'public', '2025-04-17 05:18:34', '2025-04-17 05:18:34', NULL),
(9, 'Camp 9', NULL, 'This is a public camp for testing purposes.', 6, 'public', '2025-04-17 05:18:34', '2025-04-17 05:18:34', NULL),
(10, 'Camp 10', NULL, 'This is a private camp for testing purposes.', 2, 'private', '2025-04-17 05:18:34', '2025-04-17 05:18:34', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `camp_posts`
--

CREATE TABLE `camp_posts` (
  `id` bigint UNSIGNED NOT NULL,
  `camp_id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `media_url` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `media_type` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `camp_user`
--

CREATE TABLE `camp_user` (
  `id` bigint UNSIGNED NOT NULL,
  `camp_id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `camp_user`
--

INSERT INTO `camp_user` (`id`, `camp_id`, `user_id`, `created_at`, `updated_at`) VALUES
(1, 1, 5, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(2, 1, 2, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(3, 1, 3, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(4, 1, 4, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(5, 1, 6, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(6, 2, 2, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(7, 2, 5, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(8, 2, 6, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(9, 3, 6, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(10, 3, 3, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(11, 3, 5, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(12, 4, 2, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(13, 4, 5, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(14, 4, 6, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(15, 5, 3, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(16, 5, 4, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(17, 5, 5, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(18, 6, 6, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(19, 6, 2, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(20, 6, 3, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(21, 6, 4, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(22, 7, 5, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(23, 7, 2, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(24, 7, 3, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(25, 8, 4, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(26, 8, 2, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(27, 8, 5, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(28, 8, 6, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(29, 9, 6, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(30, 9, 3, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(31, 9, 4, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(32, 10, 2, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(33, 10, 4, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(34, 10, 6, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(38, 8, 8, '2025-04-19 08:52:21', '2025-04-19 08:52:21');

-- --------------------------------------------------------

--
-- Table structure for table `chats`
--

CREATE TABLE `chats` (
  `id` bigint UNSIGNED NOT NULL,
  `sender_id` bigint UNSIGNED NOT NULL,
  `receiver_id` bigint UNSIGNED NOT NULL,
  `message` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `media_url` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `media_type` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `comments`
--

CREATE TABLE `comments` (
  `id` bigint UNSIGNED NOT NULL,
  `post_id` int NOT NULL,
  `parent_id` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `earnings`
--

CREATE TABLE `earnings` (
  `id` bigint UNSIGNED NOT NULL,
  `creator_id` bigint UNSIGNED NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `source` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `source_id` bigint UNSIGNED DEFAULT NULL,
  `earned_at` datetime NOT NULL,
  `paid_at` datetime DEFAULT NULL,
  `status` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `failed_jobs`
--

CREATE TABLE `failed_jobs` (
  `id` bigint UNSIGNED NOT NULL,
  `uuid` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `connection` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `followers`
--

CREATE TABLE `followers` (
  `id` bigint UNSIGNED NOT NULL,
  `follower_id` bigint UNSIGNED NOT NULL,
  `followed_id` bigint UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `groups`
--

CREATE TABLE `groups` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `image_path` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_by` bigint UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `interests`
--

CREATE TABLE `interests` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `interests`
--

INSERT INTO `interests` (`id`, `name`, `created_at`, `updated_at`) VALUES
(1, 'Harray potter', NULL, NULL),
(2, 'Game', NULL, NULL),
(3, 'Travaling', NULL, NULL),
(4, 'Cycling', NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `jobs`
--

CREATE TABLE `jobs` (
  `id` bigint UNSIGNED NOT NULL,
  `queue` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `attempts` tinyint UNSIGNED NOT NULL,
  `reserved_at` int UNSIGNED DEFAULT NULL,
  `available_at` int UNSIGNED NOT NULL,
  `created_at` int UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `jobs`
--

INSERT INTO `jobs` (`id`, `queue`, `payload`, `attempts`, `reserved_at`, `available_at`, `created_at`) VALUES
(1, 'default', '{\"uuid\":\"68d7d31d-e1fd-41ea-bbae-567f33aeed2d\",\"displayName\":\"App\\\\Events\\\\LiveStreamStartedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:33:\\\"App\\\\Events\\\\LiveStreamStartedEvent\\\":1:{s:6:\\\"stream\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:21:\\\"App\\\\Models\\\\Livestream\\\";s:2:\\\"id\\\";i:46;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750338295,\"delay\":null}', 0, NULL, 1750338295, 1750338295),
(2, 'default', '{\"uuid\":\"4a0e210c-a155-4494-b49a-d432e6fb3856\",\"displayName\":\"App\\\\Events\\\\LiveStreamStartedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:33:\\\"App\\\\Events\\\\LiveStreamStartedEvent\\\":1:{s:6:\\\"stream\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:21:\\\"App\\\\Models\\\\Livestream\\\";s:2:\\\"id\\\";i:47;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750339420,\"delay\":null}', 0, NULL, 1750339420, 1750339420),
(3, 'default', '{\"uuid\":\"7dfe670c-44fc-4a51-97a4-7ff237109245\",\"displayName\":\"App\\\\Events\\\\LiveStreamStartedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:33:\\\"App\\\\Events\\\\LiveStreamStartedEvent\\\":1:{s:6:\\\"stream\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:21:\\\"App\\\\Models\\\\Livestream\\\";s:2:\\\"id\\\";i:48;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750340454,\"delay\":null}', 0, NULL, 1750340454, 1750340454),
(4, 'default', '{\"uuid\":\"49eb5b87-3e8a-428e-b010-5059234fc101\",\"displayName\":\"App\\\\Events\\\\LiveStreamStartedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:33:\\\"App\\\\Events\\\\LiveStreamStartedEvent\\\":1:{s:6:\\\"stream\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:21:\\\"App\\\\Models\\\\Livestream\\\";s:2:\\\"id\\\";i:49;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750341714,\"delay\":null}', 0, NULL, 1750341714, 1750341714),
(5, 'default', '{\"uuid\":\"899d4574-c2f6-4cf5-a0ca-cbe35556ec75\",\"displayName\":\"App\\\\Events\\\\LiveStreamStartedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:33:\\\"App\\\\Events\\\\LiveStreamStartedEvent\\\":1:{s:6:\\\"stream\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:21:\\\"App\\\\Models\\\\LiveStream\\\";s:2:\\\"id\\\";i:1;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750343521,\"delay\":null}', 0, NULL, 1750343521, 1750343521),
(6, 'default', '{\"uuid\":\"d3f39517-6d45-4658-9e09-63d60d3972f6\",\"displayName\":\"App\\\\Events\\\\LiveStreamStartedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:33:\\\"App\\\\Events\\\\LiveStreamStartedEvent\\\":1:{s:6:\\\"stream\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:21:\\\"App\\\\Models\\\\LiveStream\\\";s:2:\\\"id\\\";i:2;s:9:\\\"relations\\\";a:1:{i:0;s:4:\\\"user\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750343940,\"delay\":null}', 0, NULL, 1750343940, 1750343940),
(7, 'default', '{\"uuid\":\"b16cecda-d1bc-4bb2-b861-5cd1e875e5d2\",\"displayName\":\"App\\\\Events\\\\LiveStreamStartedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:33:\\\"App\\\\Events\\\\LiveStreamStartedEvent\\\":1:{s:6:\\\"stream\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:21:\\\"App\\\\Models\\\\LiveStream\\\";s:2:\\\"id\\\";i:3;s:9:\\\"relations\\\";a:1:{i:0;s:4:\\\"user\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750344191,\"delay\":null}', 0, NULL, 1750344191, 1750344191),
(8, 'default', '{\"uuid\":\"61394e24-a340-4153-8ed1-59f96d5f9845\",\"displayName\":\"App\\\\Events\\\\LiveStreamStartedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:33:\\\"App\\\\Events\\\\LiveStreamStartedEvent\\\":1:{s:6:\\\"stream\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:21:\\\"App\\\\Models\\\\LiveStream\\\";s:2:\\\"id\\\";i:4;s:9:\\\"relations\\\";a:1:{i:0;s:4:\\\"user\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750345718,\"delay\":null}', 0, NULL, 1750345718, 1750345718),
(9, 'default', '{\"uuid\":\"47c0b883-b12b-4cd4-893f-3d4b33c16de9\",\"displayName\":\"App\\\\Events\\\\StreamEndedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:27:\\\"App\\\\Events\\\\StreamEndedEvent\\\":1:{s:4:\\\"data\\\";a:5:{s:9:\\\"stream_id\\\";i:4;s:7:\\\"user_id\\\";i:8;s:9:\\\"user_name\\\";N;s:7:\\\"message\\\";s:24:\\\" ended their live stream\\\";s:8:\\\"duration\\\";s:8:\\\"23:59:49\\\";}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750345729,\"delay\":null}', 0, NULL, 1750345729, 1750345729),
(10, 'default', '{\"uuid\":\"e881a90a-e7a9-40bc-a31a-4d48b61ee6be\",\"displayName\":\"App\\\\Events\\\\LiveStreamStartedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:33:\\\"App\\\\Events\\\\LiveStreamStartedEvent\\\":1:{s:6:\\\"stream\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:21:\\\"App\\\\Models\\\\LiveStream\\\";s:2:\\\"id\\\";i:5;s:9:\\\"relations\\\";a:1:{i:0;s:4:\\\"user\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750345934,\"delay\":null}', 0, NULL, 1750345934, 1750345934),
(11, 'default', '{\"uuid\":\"27af9c42-0e72-483e-a3c3-18a006b4a8b4\",\"displayName\":\"App\\\\Events\\\\StreamEndedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:27:\\\"App\\\\Events\\\\StreamEndedEvent\\\":1:{s:4:\\\"data\\\";a:5:{s:9:\\\"stream_id\\\";i:5;s:7:\\\"user_id\\\";i:8;s:9:\\\"user_name\\\";N;s:7:\\\"message\\\";s:24:\\\" ended their live stream\\\";s:8:\\\"duration\\\";s:8:\\\"23:59:49\\\";}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750345945,\"delay\":null}', 0, NULL, 1750345945, 1750345945),
(12, 'default', '{\"uuid\":\"0807d7cd-745d-45dd-845a-14819a0a9559\",\"displayName\":\"App\\\\Events\\\\LiveStreamStartedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:33:\\\"App\\\\Events\\\\LiveStreamStartedEvent\\\":1:{s:6:\\\"stream\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:21:\\\"App\\\\Models\\\\LiveStream\\\";s:2:\\\"id\\\";i:6;s:9:\\\"relations\\\";a:1:{i:0;s:4:\\\"user\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750345980,\"delay\":null}', 0, NULL, 1750345980, 1750345980),
(13, 'default', '{\"uuid\":\"e84d567e-61b0-4f14-862f-821aef53b4aa\",\"displayName\":\"App\\\\Events\\\\LiveStreamStartedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:33:\\\"App\\\\Events\\\\LiveStreamStartedEvent\\\":1:{s:6:\\\"stream\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:21:\\\"App\\\\Models\\\\LiveStream\\\";s:2:\\\"id\\\";i:8;s:9:\\\"relations\\\";a:1:{i:0;s:4:\\\"user\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750347208,\"delay\":null}', 0, NULL, 1750347208, 1750347208),
(14, 'default', '{\"uuid\":\"3410d0e2-a5f4-47d0-bede-187a5c23fe45\",\"displayName\":\"App\\\\Events\\\\StreamEndedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:27:\\\"App\\\\Events\\\\StreamEndedEvent\\\":1:{s:4:\\\"data\\\";a:5:{s:9:\\\"stream_id\\\";i:8;s:7:\\\"user_id\\\";i:8;s:9:\\\"user_name\\\";N;s:7:\\\"message\\\";s:24:\\\" ended their live stream\\\";s:8:\\\"duration\\\";s:8:\\\"23:59:51\\\";}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750347217,\"delay\":null}', 0, NULL, 1750347217, 1750347217),
(15, 'default', '{\"uuid\":\"7c835e0a-3a80-487e-b076-aa35316f6c96\",\"displayName\":\"App\\\\Events\\\\LiveStreamStartedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:33:\\\"App\\\\Events\\\\LiveStreamStartedEvent\\\":1:{s:6:\\\"stream\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:21:\\\"App\\\\Models\\\\LiveStream\\\";s:2:\\\"id\\\";i:9;s:9:\\\"relations\\\";a:1:{i:0;s:4:\\\"user\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750347306,\"delay\":null}', 0, NULL, 1750347306, 1750347306),
(16, 'default', '{\"uuid\":\"21661897-464f-4229-8405-40909278cf60\",\"displayName\":\"App\\\\Events\\\\StreamEndedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:27:\\\"App\\\\Events\\\\StreamEndedEvent\\\":1:{s:4:\\\"data\\\";a:5:{s:9:\\\"stream_id\\\";i:9;s:7:\\\"user_id\\\";i:8;s:9:\\\"user_name\\\";N;s:7:\\\"message\\\";s:24:\\\" ended their live stream\\\";s:8:\\\"duration\\\";s:8:\\\"23:59:28\\\";}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750347338,\"delay\":null}', 0, NULL, 1750347338, 1750347338),
(17, 'default', '{\"uuid\":\"458f89c9-8300-4105-ac7c-a2c0468c9a3f\",\"displayName\":\"App\\\\Events\\\\LiveStreamStartedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:33:\\\"App\\\\Events\\\\LiveStreamStartedEvent\\\":1:{s:6:\\\"stream\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:21:\\\"App\\\\Models\\\\LiveStream\\\";s:2:\\\"id\\\";i:10;s:9:\\\"relations\\\";a:1:{i:0;s:4:\\\"user\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750347783,\"delay\":null}', 0, NULL, 1750347783, 1750347783),
(18, 'default', '{\"uuid\":\"18f4a1d1-7e6a-494e-becc-457147e77cb8\",\"displayName\":\"App\\\\Events\\\\StreamEndedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:27:\\\"App\\\\Events\\\\StreamEndedEvent\\\":1:{s:4:\\\"data\\\";a:5:{s:9:\\\"stream_id\\\";i:10;s:7:\\\"user_id\\\";i:8;s:9:\\\"user_name\\\";N;s:7:\\\"message\\\";s:24:\\\" ended their live stream\\\";s:8:\\\"duration\\\";s:8:\\\"23:59:35\\\";}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750347808,\"delay\":null}', 0, NULL, 1750347808, 1750347808),
(19, 'default', '{\"uuid\":\"df3ce18f-89fd-4a1b-9605-511f43ad3fc8\",\"displayName\":\"App\\\\Events\\\\LiveStreamStartedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:33:\\\"App\\\\Events\\\\LiveStreamStartedEvent\\\":1:{s:6:\\\"stream\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:21:\\\"App\\\\Models\\\\LiveStream\\\";s:2:\\\"id\\\";i:11;s:9:\\\"relations\\\";a:1:{i:0;s:4:\\\"user\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750348167,\"delay\":null}', 0, NULL, 1750348167, 1750348167),
(20, 'default', '{\"uuid\":\"3f6604f3-d7c7-4e0f-98a9-0fe7489769e8\",\"displayName\":\"App\\\\Events\\\\StreamEndedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:27:\\\"App\\\\Events\\\\StreamEndedEvent\\\":1:{s:4:\\\"data\\\";a:5:{s:9:\\\"stream_id\\\";i:11;s:7:\\\"user_id\\\";i:8;s:9:\\\"user_name\\\";N;s:7:\\\"message\\\";s:24:\\\" ended their live stream\\\";s:8:\\\"duration\\\";s:8:\\\"23:59:54\\\";}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750348173,\"delay\":null}', 0, NULL, 1750348173, 1750348173),
(21, 'default', '{\"uuid\":\"6e30324f-c39f-40b8-9fb2-4b4dce2658d5\",\"displayName\":\"App\\\\Events\\\\LiveStreamStartedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:33:\\\"App\\\\Events\\\\LiveStreamStartedEvent\\\":1:{s:6:\\\"stream\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:21:\\\"App\\\\Models\\\\LiveStream\\\";s:2:\\\"id\\\";i:12;s:9:\\\"relations\\\";a:1:{i:0;s:4:\\\"user\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750348324,\"delay\":null}', 0, NULL, 1750348324, 1750348324),
(22, 'default', '{\"uuid\":\"99267304-327b-40af-b985-9a6975ba265e\",\"displayName\":\"App\\\\Events\\\\StreamEndedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:27:\\\"App\\\\Events\\\\StreamEndedEvent\\\":1:{s:4:\\\"data\\\";a:5:{s:9:\\\"stream_id\\\";i:12;s:7:\\\"user_id\\\";i:8;s:9:\\\"user_name\\\";N;s:7:\\\"message\\\";s:24:\\\" ended their live stream\\\";s:8:\\\"duration\\\";s:8:\\\"23:59:35\\\";}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750348349,\"delay\":null}', 0, NULL, 1750348349, 1750348349),
(23, 'default', '{\"uuid\":\"9e8249c4-4c51-4d48-a156-975b464b3118\",\"displayName\":\"App\\\\Events\\\\LiveStreamStartedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:33:\\\"App\\\\Events\\\\LiveStreamStartedEvent\\\":1:{s:6:\\\"stream\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:21:\\\"App\\\\Models\\\\LiveStream\\\";s:2:\\\"id\\\";i:13;s:9:\\\"relations\\\";a:1:{i:0;s:4:\\\"user\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750394350,\"delay\":null}', 0, NULL, 1750394351, 1750394351),
(24, 'default', '{\"uuid\":\"0ba728a3-a1d7-42a0-a01d-ecb6013ef30a\",\"displayName\":\"App\\\\Events\\\\StreamEndedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:27:\\\"App\\\\Events\\\\StreamEndedEvent\\\":1:{s:4:\\\"data\\\";a:6:{s:9:\\\"stream_id\\\";i:13;s:7:\\\"user_id\\\";i:8;s:9:\\\"user_name\\\";N;s:7:\\\"message\\\";s:24:\\\" ended their live stream\\\";s:8:\\\"duration\\\";s:8:\\\"23:59:36\\\";s:13:\\\"has_recording\\\";b:0;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750394371,\"delay\":null}', 0, NULL, 1750394371, 1750394371),
(25, 'default', '{\"uuid\":\"a7a625fe-9938-4e5b-abc5-3937120f4bf8\",\"displayName\":\"App\\\\Events\\\\LiveStreamStartedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:33:\\\"App\\\\Events\\\\LiveStreamStartedEvent\\\":1:{s:6:\\\"stream\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:21:\\\"App\\\\Models\\\\LiveStream\\\";s:2:\\\"id\\\";i:14;s:9:\\\"relations\\\";a:1:{i:0;s:4:\\\"user\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750394587,\"delay\":null}', 0, NULL, 1750394587, 1750394587),
(26, 'default', '{\"uuid\":\"8631a191-3b7f-4585-a1df-3f533c86fb48\",\"displayName\":\"App\\\\Events\\\\StreamEndedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:27:\\\"App\\\\Events\\\\StreamEndedEvent\\\":1:{s:4:\\\"data\\\";a:6:{s:9:\\\"stream_id\\\";i:14;s:7:\\\"user_id\\\";i:8;s:9:\\\"user_name\\\";N;s:7:\\\"message\\\";s:24:\\\" ended their live stream\\\";s:8:\\\"duration\\\";s:8:\\\"23:59:04\\\";s:13:\\\"has_recording\\\";b:0;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750394643,\"delay\":null}', 0, NULL, 1750394643, 1750394643),
(27, 'default', '{\"uuid\":\"5daf2f0d-fa9b-47a0-8187-a92d12421868\",\"displayName\":\"App\\\\Events\\\\LiveStreamStartedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:33:\\\"App\\\\Events\\\\LiveStreamStartedEvent\\\":1:{s:6:\\\"stream\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:21:\\\"App\\\\Models\\\\LiveStream\\\";s:2:\\\"id\\\";i:15;s:9:\\\"relations\\\";a:1:{i:0;s:4:\\\"user\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750395546,\"delay\":null}', 0, NULL, 1750395546, 1750395546),
(28, 'default', '{\"uuid\":\"78098cca-6d9a-49b2-a40a-dab09485fab4\",\"displayName\":\"App\\\\Events\\\\StreamEndedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:27:\\\"App\\\\Events\\\\StreamEndedEvent\\\":1:{s:4:\\\"data\\\";a:6:{s:9:\\\"stream_id\\\";i:15;s:7:\\\"user_id\\\";i:8;s:9:\\\"user_name\\\";N;s:7:\\\"message\\\";s:24:\\\" ended their live stream\\\";s:8:\\\"duration\\\";s:8:\\\"23:59:44\\\";s:13:\\\"has_recording\\\";b:0;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750395561,\"delay\":null}', 0, NULL, 1750395561, 1750395561),
(29, 'default', '{\"uuid\":\"9bf98909-e9a7-44f0-b98b-6ca36ab9356e\",\"displayName\":\"App\\\\Events\\\\LiveStreamStartedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:33:\\\"App\\\\Events\\\\LiveStreamStartedEvent\\\":1:{s:6:\\\"stream\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:21:\\\"App\\\\Models\\\\LiveStream\\\";s:2:\\\"id\\\";i:16;s:9:\\\"relations\\\";a:1:{i:0;s:4:\\\"user\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750395589,\"delay\":null}', 0, NULL, 1750395589, 1750395589),
(30, 'default', '{\"uuid\":\"7b210e74-ea89-471b-9369-1d8f281b36d2\",\"displayName\":\"App\\\\Events\\\\StreamEndedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:27:\\\"App\\\\Events\\\\StreamEndedEvent\\\":1:{s:4:\\\"data\\\";a:6:{s:9:\\\"stream_id\\\";i:16;s:7:\\\"user_id\\\";i:8;s:9:\\\"user_name\\\";N;s:7:\\\"message\\\";s:24:\\\" ended their live stream\\\";s:8:\\\"duration\\\";s:8:\\\"23:59:34\\\";s:13:\\\"has_recording\\\";b:0;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750395614,\"delay\":null}', 0, NULL, 1750395614, 1750395614),
(31, 'default', '{\"uuid\":\"65536425-91dd-415c-87e2-816961b533a2\",\"displayName\":\"App\\\\Events\\\\LiveStreamStartedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:33:\\\"App\\\\Events\\\\LiveStreamStartedEvent\\\":1:{s:6:\\\"stream\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:21:\\\"App\\\\Models\\\\LiveStream\\\";s:2:\\\"id\\\";i:17;s:9:\\\"relations\\\";a:1:{i:0;s:4:\\\"user\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750397444,\"delay\":null}', 0, NULL, 1750397444, 1750397444),
(32, 'default', '{\"uuid\":\"cb8dcfaf-849e-4617-b448-420f6e621b40\",\"displayName\":\"App\\\\Events\\\\StreamEndedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:27:\\\"App\\\\Events\\\\StreamEndedEvent\\\":1:{s:4:\\\"data\\\";a:6:{s:9:\\\"stream_id\\\";i:17;s:7:\\\"user_id\\\";i:8;s:9:\\\"user_name\\\";N;s:7:\\\"message\\\";s:24:\\\" ended their live stream\\\";s:8:\\\"duration\\\";s:8:\\\"23:59:38\\\";s:13:\\\"has_recording\\\";b:0;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750397466,\"delay\":null}', 0, NULL, 1750397466, 1750397466),
(33, 'default', '{\"uuid\":\"7d918aa7-ea2f-4b99-baa9-476c04f106a4\",\"displayName\":\"App\\\\Events\\\\LiveStreamStartedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:33:\\\"App\\\\Events\\\\LiveStreamStartedEvent\\\":1:{s:6:\\\"stream\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:21:\\\"App\\\\Models\\\\LiveStream\\\";s:2:\\\"id\\\";i:18;s:9:\\\"relations\\\";a:1:{i:0;s:4:\\\"user\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750398525,\"delay\":null}', 0, NULL, 1750398525, 1750398525),
(34, 'default', '{\"uuid\":\"fd320d50-4d08-4932-9d75-a179656320a8\",\"displayName\":\"App\\\\Events\\\\StreamEndedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:27:\\\"App\\\\Events\\\\StreamEndedEvent\\\":1:{s:4:\\\"data\\\";a:6:{s:9:\\\"stream_id\\\";i:18;s:7:\\\"user_id\\\";i:8;s:9:\\\"user_name\\\";N;s:7:\\\"message\\\";s:24:\\\" ended their live stream\\\";s:8:\\\"duration\\\";s:8:\\\"23:59:44\\\";s:13:\\\"has_recording\\\";b:0;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750398541,\"delay\":null}', 0, NULL, 1750398541, 1750398541),
(35, 'default', '{\"uuid\":\"581122a7-5f90-4c05-86a4-3211da1b62a7\",\"displayName\":\"App\\\\Events\\\\LiveStreamStartedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:33:\\\"App\\\\Events\\\\LiveStreamStartedEvent\\\":1:{s:6:\\\"stream\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:21:\\\"App\\\\Models\\\\LiveStream\\\";s:2:\\\"id\\\";i:19;s:9:\\\"relations\\\";a:1:{i:0;s:4:\\\"user\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750398856,\"delay\":null}', 0, NULL, 1750398856, 1750398856),
(36, 'default', '{\"uuid\":\"0a35b59c-c4e1-4479-b736-9630f0bc6725\",\"displayName\":\"App\\\\Events\\\\StreamEndedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:27:\\\"App\\\\Events\\\\StreamEndedEvent\\\":1:{s:4:\\\"data\\\";a:6:{s:9:\\\"stream_id\\\";i:19;s:7:\\\"user_id\\\";i:8;s:9:\\\"user_name\\\";N;s:7:\\\"message\\\";s:24:\\\" ended their live stream\\\";s:8:\\\"duration\\\";s:8:\\\"23:59:55\\\";s:13:\\\"has_recording\\\";b:0;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750398861,\"delay\":null}', 0, NULL, 1750398861, 1750398861),
(37, 'default', '{\"uuid\":\"e1cb50cd-76ea-4ed7-a160-32cb91466ec7\",\"displayName\":\"App\\\\Events\\\\LiveStreamStartedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:33:\\\"App\\\\Events\\\\LiveStreamStartedEvent\\\":1:{s:6:\\\"stream\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:21:\\\"App\\\\Models\\\\LiveStream\\\";s:2:\\\"id\\\";i:22;s:9:\\\"relations\\\";a:1:{i:0;s:4:\\\"user\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750400516,\"delay\":null}', 0, NULL, 1750400516, 1750400516),
(38, 'default', '{\"uuid\":\"262c7320-94fc-447a-9e86-cd403741d04f\",\"displayName\":\"App\\\\Events\\\\StreamEndedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:27:\\\"App\\\\Events\\\\StreamEndedEvent\\\":1:{s:4:\\\"data\\\";a:6:{s:9:\\\"stream_id\\\";i:22;s:7:\\\"user_id\\\";i:8;s:9:\\\"user_name\\\";N;s:7:\\\"message\\\";s:24:\\\" ended their live stream\\\";s:8:\\\"duration\\\";s:8:\\\"23:58:40\\\";s:13:\\\"has_recording\\\";b:0;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750400596,\"delay\":null}', 0, NULL, 1750400596, 1750400596),
(39, 'default', '{\"uuid\":\"88a4bfa6-f691-4b91-bd55-9a63f4883e5f\",\"displayName\":\"App\\\\Events\\\\LiveStreamStartedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:33:\\\"App\\\\Events\\\\LiveStreamStartedEvent\\\":1:{s:6:\\\"stream\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:21:\\\"App\\\\Models\\\\LiveStream\\\";s:2:\\\"id\\\";i:23;s:9:\\\"relations\\\";a:1:{i:0;s:4:\\\"user\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750400662,\"delay\":null}', 0, NULL, 1750400662, 1750400662),
(40, 'default', '{\"uuid\":\"*************-4c1c-a6c4-cf6ebad18e0b\",\"displayName\":\"App\\\\Events\\\\StreamEndedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:27:\\\"App\\\\Events\\\\StreamEndedEvent\\\":1:{s:4:\\\"data\\\";a:6:{s:9:\\\"stream_id\\\";i:23;s:7:\\\"user_id\\\";i:8;s:9:\\\"user_name\\\";N;s:7:\\\"message\\\";s:24:\\\" ended their live stream\\\";s:8:\\\"duration\\\";s:8:\\\"23:59:55\\\";s:13:\\\"has_recording\\\";b:1;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750400667,\"delay\":null}', 0, NULL, 1750400667, 1750400667),
(41, 'default', '{\"uuid\":\"acaa4f42-66f3-4133-8cd6-feea86689fcf\",\"displayName\":\"App\\\\Events\\\\LiveStreamStartedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:33:\\\"App\\\\Events\\\\LiveStreamStartedEvent\\\":1:{s:6:\\\"stream\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:21:\\\"App\\\\Models\\\\LiveStream\\\";s:2:\\\"id\\\";i:24;s:9:\\\"relations\\\";a:1:{i:0;s:4:\\\"user\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750703138,\"delay\":null}', 0, NULL, 1750703138, 1750703138);
INSERT INTO `jobs` (`id`, `queue`, `payload`, `attempts`, `reserved_at`, `available_at`, `created_at`) VALUES
(42, 'default', '{\"uuid\":\"6525adb7-c318-4fbf-abb6-1d839e383135\",\"displayName\":\"App\\\\Events\\\\StreamEndedEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:27:\\\"App\\\\Events\\\\StreamEndedEvent\\\":1:{s:4:\\\"data\\\";a:6:{s:9:\\\"stream_id\\\";i:24;s:7:\\\"user_id\\\";i:8;s:9:\\\"user_name\\\";N;s:7:\\\"message\\\";s:24:\\\" ended their live stream\\\";s:8:\\\"duration\\\";s:8:\\\"23:59:38\\\";s:13:\\\"has_recording\\\";b:0;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"},\"createdAt\":1750703157,\"delay\":null}', 0, NULL, 1750703157, 1750703157);

-- --------------------------------------------------------

--
-- Table structure for table `job_batches`
--

CREATE TABLE `job_batches` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `total_jobs` int NOT NULL,
  `pending_jobs` int NOT NULL,
  `failed_jobs` int NOT NULL,
  `failed_job_ids` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `options` mediumtext COLLATE utf8mb4_unicode_ci,
  `cancelled_at` int DEFAULT NULL,
  `created_at` int NOT NULL,
  `finished_at` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `livestreams`
--

CREATE TABLE `livestreams` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `title` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `stream_key` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'scheduled',
  `is_live` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `visibility` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `scheduled_at` timestamp NULL DEFAULT NULL,
  `started_at` timestamp NULL DEFAULT NULL,
  `ended_at` timestamp NULL DEFAULT NULL,
  `viewer_count` int NOT NULL DEFAULT '0',
  `is_private` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `start_time` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `livestreams`
--

INSERT INTO `livestreams` (`id`, `user_id`, `title`, `description`, `stream_key`, `status`, `is_live`, `visibility`, `scheduled_at`, `started_at`, `ended_at`, `viewer_count`, `is_private`, `created_at`, `updated_at`, `start_time`) VALUES
(1, 7, 'fdsafdsa', NULL, 'mR8D46xD2JOR9WwO', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-04-17 06:52:10', '2025-04-17 06:52:10', NULL),
(2, 7, 'gdsg', NULL, 'cd7IoLD5vCBrKvFz', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-04-17 06:54:40', '2025-04-17 06:54:40', NULL),
(3, 7, 'gdsg', NULL, 'eqPCyZumahKIBGya', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-04-17 07:11:57', '2025-04-17 07:11:57', NULL),
(4, 7, 'hhhh', NULL, 'UovHgcL8KgrSwiqr', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-04-17 07:14:44', '2025-04-17 07:14:44', NULL),
(5, 7, 'hhhh', NULL, 'IvaM4bquGoDBkghu', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-04-17 07:19:36', '2025-04-17 07:19:36', NULL),
(6, 7, 'ttt', NULL, 'E21R3kSkAcb9WqTr', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-04-17 07:25:58', '2025-04-17 07:25:58', NULL),
(7, 7, 'tedy', NULL, 'GSTDPczz7AQM802k', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-04-17 08:07:45', '2025-04-17 08:07:45', NULL),
(8, 7, 'tedy', NULL, 'gGinhjdR625xgfWb', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-04-17 08:14:53', '2025-04-17 08:14:53', NULL),
(9, 7, 'nnn', NULL, 'DOwN3aqjJIfZDeKk', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-04-17 08:21:58', '2025-04-17 08:21:58', NULL),
(10, 7, 'fdsfsad', NULL, 'ibx05CDalCGASkFj', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-04-17 08:23:34', '2025-04-17 08:23:34', NULL),
(11, 8, 'fdsa', NULL, 'bWmhsKpDlrl8lwqd', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-04-17 12:27:03', '2025-04-17 12:27:03', NULL),
(12, 8, 'fff', NULL, 'vsM7XANi3JKNTJxF', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-04-17 12:34:54', '2025-04-17 12:34:54', NULL),
(13, 8, 'maruf', NULL, 'TtKKCi3OOPPz7IgZ', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-04-17 12:40:27', '2025-04-17 12:40:27', NULL),
(14, 8, 'maruf', NULL, '1bLv7GKSL4gL3qFu', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-04-17 12:41:11', '2025-04-17 12:41:11', NULL),
(15, 8, 'fa', NULL, 'zFUVnI2WPrwq5tP0', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-04-17 12:41:41', '2025-04-17 12:41:41', NULL),
(16, 8, 'fdsa', NULL, 'J8rfQ7MikghHdfhu', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-04-17 12:42:00', '2025-04-17 12:42:00', NULL),
(17, 8, 'fdsa', NULL, 'VAjvT7WibR2zfAHs', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-04-17 12:42:33', '2025-04-17 12:42:33', NULL),
(18, 8, 'fdsa', NULL, 'YXpOVoGOMwD1EUFa', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-04-17 12:42:47', '2025-04-17 12:42:47', NULL),
(19, 8, 'fda', NULL, 'TwsVUKBPlBoyfbtd', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-04-17 12:43:22', '2025-04-17 12:43:22', NULL),
(20, 8, 'kkk', NULL, 'CeASMuntUsA6Sq8E', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-04-17 12:45:21', '2025-04-17 12:45:21', NULL),
(21, 8, 'kfkf', NULL, '0gBYlBRSexoSVxM9', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-04-17 12:54:51', '2025-04-17 12:54:51', NULL),
(22, 8, 'kfkf', NULL, '9rdvgNRxwWFPv21p', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-04-17 12:59:32', '2025-04-17 12:59:32', NULL),
(23, 8, 'mmm', NULL, 'uHpI6WczdiwjZtMh', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-04-17 13:07:36', '2025-04-17 13:07:36', NULL),
(24, 8, 'ggggggg', NULL, 'h4cgJRpUvfrf6uiV', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-04-18 23:27:06', '2025-04-18 23:27:06', NULL),
(25, 8, 'uuuuu', NULL, 'XndfZEBFU41guybZ', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-04-18 23:33:00', '2025-04-18 23:33:00', NULL),
(26, 8, 'kkkkk', NULL, 'jyCP5fZM4RBS2Suq', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-04-18 23:48:33', '2025-04-18 23:48:33', NULL),
(27, 8, 'dsafsd', NULL, 'uIRnOxohgSk5DIuA', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-04-19 00:48:03', '2025-04-19 00:48:03', NULL),
(28, 8, 'jjjjfh', NULL, 'gsS4geLPupU0fhbq', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-04-19 00:48:49', '2025-04-19 00:48:49', NULL),
(29, 8, 'kkkk', NULL, 'Orz1mfoUHea1jodU', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-04-19 01:11:55', '2025-04-19 01:11:55', NULL),
(30, 8, 'kkkk', NULL, '7z8T4GD9GItC6j0F', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-04-19 01:16:06', '2025-04-19 01:16:06', NULL),
(31, 8, 'kkk', NULL, 'vRuScfupdYGBccS3', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-04-19 01:27:22', '2025-04-19 01:27:22', NULL),
(32, 14, 'gfdgf', NULL, 'Ct5FC2Zjr9SCFMJX', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-05-12 18:54:07', '2025-05-12 18:54:07', NULL),
(33, 18, 'yyyy', NULL, 'WcIofEVO0Pj0wRg1', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-05-19 13:14:46', '2025-05-19 13:14:46', NULL),
(34, 17, 'testing', NULL, 'wAqn418HswVVfKte', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-05-19 13:15:04', '2025-05-19 13:15:04', NULL),
(35, 20, 'test', NULL, 'JZFV5JQnxRDCdsGZ', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-05-27 05:43:52', '2025-05-27 05:43:52', NULL),
(36, 21, 'fdsfsda', NULL, 'U6F867VKssz6LqLT', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-05-27 14:33:38', '2025-05-27 14:33:38', NULL),
(37, 21, 'test', NULL, 'ulYqpsabshGgELSA', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-05-27 14:35:37', '2025-05-27 14:35:37', NULL),
(38, 8, 'testing', NULL, 'sEx8wXjhIfYoLHtz', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-06-19 05:01:43', '2025-06-19 05:01:43', NULL),
(39, 8, 'fdsa', NULL, 'jheFvTSvtD7tYqmq', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-06-19 05:03:14', '2025-06-19 05:03:14', NULL),
(40, 8, 'test', NULL, 'OgrL3JVquHZqhjvM', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-06-19 06:13:55', '2025-06-19 06:13:55', NULL),
(41, 8, 'test', NULL, 'sRRvJBXRLMSwtwwm', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-06-19 06:40:28', '2025-06-19 06:40:28', NULL),
(42, 8, 'tes', NULL, 'sK2i6FEWgiHRxUfA', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-06-19 06:40:52', '2025-06-19 06:40:52', NULL),
(43, 8, 'dsa', NULL, 'jLV49afRGlVokTcg', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-06-19 06:42:42', '2025-06-19 06:42:42', NULL),
(44, 8, 'test', NULL, 'Z2pCkesb1j4Es1Ix', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-06-19 06:43:30', '2025-06-19 06:43:30', NULL),
(45, 8, 'testmaruf', NULL, '1rb8kgCtVPSDDCOG', 'scheduled', NULL, NULL, NULL, NULL, NULL, 0, 0, '2025-06-19 06:53:35', '2025-06-19 06:53:35', NULL),
(46, 8, 'hello everyone', '', '03BsjAT3XstW0i3BXRiIuILTadv5Bbc1', 'scheduled', NULL, NULL, NULL, '2025-06-19 07:04:52', NULL, 0, 0, '2025-06-19 07:04:52', '2025-06-19 07:04:52', NULL),
(47, 8, 'hello', '', 'bs8swtaUdPeTFQy4jdwQx7ZF3zG07snp', 'scheduled', NULL, NULL, NULL, '2025-06-19 07:23:40', NULL, 0, 0, '2025-06-19 07:23:40', '2025-06-19 07:23:40', NULL),
(48, 8, 'fffff', '', 'Bglk6PE876KcxrRdOUmEImzqgoCcDEoh', 'scheduled', NULL, NULL, NULL, '2025-06-19 07:40:54', NULL, 0, 0, '2025-06-19 07:40:54', '2025-06-19 07:40:54', NULL),
(49, 8, 'testing both', '', 'GW9qlPdgq9PcwTjlFPzvAnUJeAy1glaq', 'scheduled', NULL, NULL, NULL, '2025-06-19 08:01:54', NULL, 0, 0, '2025-06-19 08:01:54', '2025-06-19 08:01:54', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `livestream_comments`
--

CREATE TABLE `livestream_comments` (
  `id` bigint UNSIGNED NOT NULL,
  `live_stream_id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `livestream_id` int DEFAULT NULL,
  `comment` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `live_streams`
--

CREATE TABLE `live_streams` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `title` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `visibility` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'public',
  `stream_url` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `recording_path` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `thumbnail_path` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `has_recording` tinyint(1) NOT NULL DEFAULT '0',
  `stream_key` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_live` tinyint(1) NOT NULL DEFAULT '1',
  `viewer_count` int NOT NULL DEFAULT '0',
  `started_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `start_time` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ended_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `post_id` bigint UNSIGNED DEFAULT NULL,
  `duration_seconds` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `live_streams`
--

INSERT INTO `live_streams` (`id`, `user_id`, `title`, `description`, `visibility`, `stream_url`, `recording_path`, `thumbnail_path`, `has_recording`, `stream_key`, `is_live`, `viewer_count`, `started_at`, `start_time`, `ended_at`, `created_at`, `updated_at`, `post_id`, `duration_seconds`) VALUES
(1, 8, 'fdfsda', '', 'public', 'rtmp://localhost/live/Z3tiC3IWw3t55pUs', NULL, NULL, 0, 'W1uUsU4mgWbtumFjiTBM2DgKvXoi3R2o', 0, 0, '2025-06-19 08:32:01', NULL, NULL, '2025-06-19 08:32:01', '2025-06-19 08:32:09', NULL, NULL),
(2, 8, 'testing', '', 'public', 'rtmp://localhost/live/boi18B1U3jhQAST8', NULL, NULL, 0, 'wuo7J87xgaJuFbehVKoQzxISVzFIntN7', 0, 0, '2025-06-19 08:39:00', NULL, NULL, '2025-06-19 08:39:00', '2025-06-19 08:39:08', NULL, NULL),
(3, 8, 'maruf live', '', 'public', 'rtmp://localhost/live/KrBOtfHSNuHG4V0m', NULL, NULL, 0, 'RiLx3DjsGiHkb0K8TKBApmn2F4RZLnWb', 0, 0, '2025-06-19 08:43:11', NULL, NULL, '2025-06-19 08:43:11', '2025-06-19 08:44:02', NULL, NULL),
(4, 8, 'auto record', '', 'public', 'rtmp://localhost/live/fPlWQIG3OUjRANb3', NULL, NULL, 0, 'glHUIIqIEP2K0rTdNLFrXpG5YR7KCB82', 0, 0, '2025-06-19 09:08:38', NULL, '2025-06-19 09:08:49', '2025-06-19 09:08:38', '2025-06-19 09:08:49', NULL, -11),
(5, 8, 'dddd', '', 'public', 'rtmp://localhost/live/FoSK6lzHR75Cul02', NULL, NULL, 0, 'i8iNqdG4uLbYaTzSAdIPQy26Kazd21SV', 0, 0, '2025-06-19 09:12:14', NULL, '2025-06-19 09:12:25', '2025-06-19 09:12:14', '2025-06-19 09:12:25', NULL, -11),
(6, 8, 'dddd', '', 'public', 'rtmp://localhost/live/VSkFkay1mKDVt0DZ', NULL, NULL, 0, 'U6tpsF5TUaGMUTLJRlNiIVUNQqGgdw2c', 0, 0, '2025-06-19 09:13:00', NULL, NULL, '2025-06-19 09:13:00', '2025-06-19 09:32:02', NULL, NULL),
(7, 1, 'Test', NULL, 'public', 'test', 'livestream-recordings/test_recording.webm', 'stream-thumbnails/test_thumbnail.jpg', 1, 'test', 1, 0, '2025-06-19 09:24:34', NULL, NULL, '2025-06-19 09:24:34', '2025-06-20 00:06:33', NULL, NULL),
(8, 8, 'gfdgdfsg', '', 'public', 'rtmp://localhost/live/WdTaRDl7j2eHFjjn', NULL, NULL, 0, 'Rg2uWaPQEVBJ2O9zTsROUHfmhJUXf6p0', 0, 0, '2025-06-19 09:33:28', NULL, '2025-06-19 09:33:37', '2025-06-19 09:33:28', '2025-06-19 09:33:37', NULL, -9),
(9, 8, 'bbb', '', 'public', 'rtmp://localhost/live/xJkqnOBjnmkNLHX0', NULL, NULL, 0, 'R08eYRtywd9SDUbB80UDFCpv0G72B2D6', 0, 0, '2025-06-19 09:35:06', NULL, '2025-06-19 09:35:38', '2025-06-19 09:35:06', '2025-06-19 09:35:38', NULL, -32),
(10, 8, 'gggg', '', 'public', 'rtmp://localhost/live/TkOHXju9oXgQvTt7', NULL, NULL, 0, 'awvrAHseLxzkWNmm3jEoS3zANMa8WHBP', 0, 0, '2025-06-19 09:43:03', NULL, '2025-06-19 09:43:28', '2025-06-19 09:43:03', '2025-06-19 09:43:28', NULL, -25),
(11, 8, 'gggg', '', 'public', 'rtmp://localhost/live/DZ0OrySagua9OlMV', NULL, NULL, 0, '8V5SF9fSByi5lKSufQ5Qe5n2UL5RnJhF', 0, 0, '2025-06-19 09:49:27', NULL, '2025-06-19 09:49:33', '2025-06-19 09:49:27', '2025-06-19 09:49:33', NULL, -6),
(12, 8, 'fgdsfsdfsd', '', 'public', 'rtmp://localhost/live/4uYacGk5032s1lJU', NULL, NULL, 0, 'wEcgWZDl1KYR2QvN8NXpgd8PSowq9AsC', 0, 0, '2025-06-19 09:52:04', NULL, '2025-06-19 09:52:29', '2025-06-19 09:52:04', '2025-06-19 09:52:29', NULL, -25),
(13, 8, 'gjgj', '', 'public', 'rtmp://localhost/live/P52VZqYeAbjdMWckYctu8wrpPlwq5Ux0', 'livestream-recordings/stream_13_1750394347.mp4', NULL, 0, 'P52VZqYeAbjdMWckYctu8wrpPlwq5Ux0', 0, 0, '2025-06-19 22:39:07', NULL, '2025-06-19 22:39:31', '2025-06-19 22:39:07', '2025-06-19 22:39:31', NULL, -24),
(14, 8, 'ttttt', '', 'public', 'rtmp://localhost/live/Ge6vdCv3GvmytsHcT1iTvS91C9w0qFCp', 'livestream-recordings/stream_14_1750394587.mp4', NULL, 0, 'Ge6vdCv3GvmytsHcT1iTvS91C9w0qFCp', 0, 0, '2025-06-19 22:43:07', NULL, '2025-06-19 22:44:03', '2025-06-19 22:43:07', '2025-06-19 22:44:03', NULL, -56),
(15, 8, 'tttttddd', '', 'public', 'rtmp://localhost/live/Lq1Uo46zIGX8LIXd9qnbHLjrEuoNnAfw', 'livestream-recordings/stream_15_1750395545.mp4', NULL, 0, 'Lq1Uo46zIGX8LIXd9qnbHLjrEuoNnAfw', 0, 0, '2025-06-19 22:59:05', NULL, '2025-06-19 22:59:21', '2025-06-19 22:59:05', '2025-06-19 22:59:21', NULL, -16),
(16, 8, 'tttttdddttt', '', 'public', 'rtmp://localhost/live/2vKrCxatDFYcszHQOqnLmx3iyq2wIurm', 'livestream-recordings/stream_16_1750395588.mp4', NULL, 0, '2vKrCxatDFYcszHQOqnLmx3iyq2wIurm', 0, 0, '2025-06-19 22:59:48', NULL, '2025-06-19 23:00:14', '2025-06-19 22:59:48', '2025-06-19 23:00:14', NULL, -26),
(17, 8, 'ffffffffffffff', '', 'public', 'rtmp://localhost/live/E7fntJFn5hmNZurwQBUT7ijnsRPqompC', NULL, NULL, 0, 'E7fntJFn5hmNZurwQBUT7ijnsRPqompC', 0, 0, '2025-06-19 23:30:44', NULL, '2025-06-19 23:31:06', '2025-06-19 23:30:44', '2025-06-19 23:31:06', NULL, -22),
(18, 8, 'testing1', '', 'public', 'rtmp://localhost/live/fDWX9deszA9VwYrJYfBlePnT6dUwPnPt', NULL, NULL, 0, 'fDWX9deszA9VwYrJYfBlePnT6dUwPnPt', 0, 0, '2025-06-19 23:48:45', NULL, '2025-06-19 23:49:01', '2025-06-19 23:48:45', '2025-06-19 23:49:01', NULL, -16),
(19, 8, 'gfdag', '', 'public', 'rtmp://localhost/live/0t0QKbJbm5nRTPqaCxL2tRICKDiHDm00', NULL, NULL, 0, '0t0QKbJbm5nRTPqaCxL2tRICKDiHDm00', 0, 0, '2025-06-19 23:54:16', NULL, '2025-06-19 23:54:21', '2025-06-19 23:54:16', '2025-06-19 23:54:21', NULL, -5),
(21, 1, 'Test Upload Stream', 'Testing recording upload', 'public', 'rtmp://test', 'livestream-recordings/stream_21_1750400052.webm', 'stream-thumbnails/thumb_21_1750400052.jpg', 1, 'upload_test_1750400052', 0, 0, '2025-06-20 00:14:12', NULL, '2025-06-20 00:14:12', '2025-06-20 00:14:12', '2025-06-20 00:14:12', 61, NULL),
(22, 8, 'gfdag6666', '', 'public', 'rtmp://localhost/live/IlUxtUAM0yK5QEYqQ1TqKRGJjp6LkTrP', NULL, NULL, 0, 'IlUxtUAM0yK5QEYqQ1TqKRGJjp6LkTrP', 0, 0, '2025-06-20 00:21:56', NULL, '2025-06-20 00:23:16', '2025-06-20 00:21:56', '2025-06-20 00:23:16', NULL, -80),
(23, 8, '111', '', 'public', 'rtmp://localhost/live/aDezkNtbfCldTWuBTN8IuccDnPsveoUQ', 'livestream-recordings/stream_23_1750400667.webm', 'stream-thumbnails/thumb_23_1750400667.jpg', 1, 'aDezkNtbfCldTWuBTN8IuccDnPsveoUQ', 0, 0, '2025-06-20 00:24:22', NULL, '2025-06-20 00:24:27', '2025-06-20 00:24:22', '2025-06-20 00:24:27', 62, -5),
(24, 8, 'te', '', 'public', 'rtmp://localhost/live/0tyOnZ90SMT5Eeh9nvGaGsxP39EY4Wk9', NULL, NULL, 0, '0tyOnZ90SMT5Eeh9nvGaGsxP39EY4Wk9', 0, 0, '2025-06-23 12:25:35', NULL, '2025-06-23 12:25:57', '2025-06-23 12:25:35', '2025-06-23 12:25:57', NULL, -22);

-- --------------------------------------------------------

--
-- Table structure for table `love`
--

CREATE TABLE `love` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `post_id` bigint UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `love_wallets`
--

CREATE TABLE `love_wallets` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `love_balance` int NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `love_wallets`
--

INSERT INTO `love_wallets` (`id`, `user_id`, `love_balance`, `created_at`, `updated_at`) VALUES
(1, 20, 300, '2025-05-27 05:42:48', '2025-05-27 05:42:49'),
(2, 8, 2500, '2025-06-19 07:25:41', '2025-06-19 07:25:41');

-- --------------------------------------------------------

--
-- Table structure for table `migrations`
--

CREATE TABLE `migrations` (
  `id` int UNSIGNED NOT NULL,
  `migration` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `migrations`
--

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(1, '00001_10_12_000000_create_users_table', 1),
(2, '0001_01_01_000001_create_cache_table', 1),
(3, '0001_01_01_000002_create_jobs_table', 1),
(4, '2022_03_21_create_camps_table', 1),
(5, '2023_03_20_create_groups_table', 1),
(6, '2023_07_15_000000_create_live_streams_table', 1),
(7, '2023_10_15_000002_create_camp_user_table', 1),
(8, '2023_10_16_000001_add_love_fields_to_users_table', 1),
(9, '2024_03_15_000000_create_user_profiles_table', 1),
(10, '2024_03_15_000001_create_interests_table', 1),
(11, '2024_03_15_000002_create_posts_table', 1),
(12, '2024_03_16_000000_add_onboarding_completed_to_users_table', 1),
(13, '2024_03_16_000001_create_chats_table', 1),
(14, '2024_03_16_000002_create_notifications_table', 1),
(15, '2024_03_17_000006_create_camp_posts_table', 1),
(16, '2024_03_18_000001_add_is_verified_to_users_table', 1),
(17, '2024_04_01_000001_add_creator_fields_to_users_table', 1),
(18, '2024_04_01_000002_create_subscriptions_table', 1),
(19, '2024_04_01_000003_create_earnings_table', 1),
(20, '2024_04_01_000004_add_visibility_to_posts_table', 1),
(21, '2024_04_01_000005_create_user_interests_relation', 1),
(22, '2024_04_03_000002_create_love_wallets_table', 1),
(23, '2024_04_10_000001_add_creator_balance_to_users_table', 1),
(24, '2024_04_15_000001_create_search_logs_table', 1),
(25, '2024_04_20_000001_create_withdrawals_table', 1),
(26, '2024_04_25_000001_create_followers_table', 1),
(27, '2025_04_04_192042_create_sessions_table', 1),
(28, '2025_04_07_134820_create_comments_table', 1),
(29, '2025_04_14_133839_create_payout_requests_table', 1),
(30, '2025_04_14_153105_create_revenues_table', 1),
(31, '2028_10_16_000002_create_loves_table', 1),
(32, 'xxxx_xx_xx_create_post_media_table', 1),
(33, 'xxxx_xx_xx_xxxxxx_update_posts_table_for_single_media', 1),
(34, '2024_12_30_000001_create_livestream_comments_table', 2),
(35, '2024_12_30_000003_add_visibility_to_live_streams', 3),
(37, '2025_06_20_040757_add_thumbnail_path_to_live_streams_table', 4);

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `type` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `notifiable_type` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `notifiable_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ;

-- --------------------------------------------------------

--
-- Table structure for table `payout_requests`
--

CREATE TABLE `payout_requests` (
  `id` bigint UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `posts`
--

CREATE TABLE `posts` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `live_stream_id` int DEFAULT NULL,
  `live_streams` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `caption` text COLLATE utf8mb4_unicode_ci,
  `media_url` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `media_type` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `type` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'post',
  `visibility` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'public',
  `is_premium` tinyint(1) NOT NULL DEFAULT '0',
  `camp_id` bigint UNSIGNED DEFAULT NULL,
  `approved` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `content` text COLLATE utf8mb4_unicode_ci,
  `image` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `posts`
--

INSERT INTO `posts` (`id`, `user_id`, `live_stream_id`, `live_streams`, `caption`, `media_url`, `media_type`, `type`, `visibility`, `is_premium`, `camp_id`, `approved`, `content`, `image`, `created_at`, `updated_at`) VALUES
(1, 5, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 1, NULL, 'This is post 1 in Camp 1', NULL, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(2, 3, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 1, NULL, 'This is post 2 in Camp 1', NULL, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(3, 6, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 1, NULL, 'This is post 3 in Camp 1', NULL, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(4, 2, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 1, NULL, 'This is post 4 in Camp 1', NULL, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(5, 6, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 2, NULL, 'This is post 1 in Camp 2', NULL, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(6, 2, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 2, NULL, 'This is post 2 in Camp 2', NULL, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(7, 5, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 2, NULL, 'This is post 3 in Camp 2', NULL, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(8, 5, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 3, NULL, 'This is post 1 in Camp 3', NULL, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(9, 3, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 3, NULL, 'This is post 2 in Camp 3', NULL, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(10, 6, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 3, NULL, 'This is post 3 in Camp 3', NULL, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(11, 5, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 3, NULL, 'This is post 4 in Camp 3', NULL, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(12, 5, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 3, NULL, 'This is post 5 in Camp 3', NULL, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(13, 6, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 3, NULL, 'This is post 6 in Camp 3', NULL, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(14, 6, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 4, NULL, 'This is post 1 in Camp 4', NULL, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(15, 5, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 4, NULL, 'This is post 2 in Camp 4', NULL, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(16, 2, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 4, NULL, 'This is post 3 in Camp 4', NULL, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(17, 2, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 4, NULL, 'This is post 4 in Camp 4', NULL, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(18, 5, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 4, NULL, 'This is post 5 in Camp 4', NULL, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(19, 2, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 4, NULL, 'This is post 6 in Camp 4', NULL, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(20, 5, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 4, NULL, 'This is post 7 in Camp 4', NULL, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(21, 3, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 5, NULL, 'This is post 1 in Camp 5', NULL, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(22, 5, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 5, NULL, 'This is post 2 in Camp 5', NULL, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(23, 3, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 5, NULL, 'This is post 3 in Camp 5', NULL, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(24, 4, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 5, NULL, 'This is post 4 in Camp 5', NULL, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(25, 5, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 5, NULL, 'This is post 5 in Camp 5', NULL, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(26, 3, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 5, NULL, 'This is post 6 in Camp 5', NULL, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(27, 3, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 5, NULL, 'This is post 7 in Camp 5', NULL, '2025-04-17 05:18:33', '2025-04-17 05:18:33'),
(28, 4, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 5, NULL, 'This is post 8 in Camp 5', NULL, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(29, 6, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 6, NULL, 'This is post 1 in Camp 6', NULL, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(30, 3, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 6, NULL, 'This is post 2 in Camp 6', NULL, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(31, 3, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 6, NULL, 'This is post 3 in Camp 6', NULL, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(32, 6, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 6, NULL, 'This is post 4 in Camp 6', NULL, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(33, 5, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 7, NULL, 'This is post 1 in Camp 7', NULL, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(34, 5, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 7, NULL, 'This is post 2 in Camp 7', NULL, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(35, 2, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 7, NULL, 'This is post 3 in Camp 7', NULL, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(36, 3, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 7, NULL, 'This is post 4 in Camp 7', NULL, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(37, 3, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 7, NULL, 'This is post 5 in Camp 7', NULL, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(38, 5, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 7, NULL, 'This is post 6 in Camp 7', NULL, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(39, 5, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 7, NULL, 'This is post 7 in Camp 7', NULL, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(40, 5, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 8, NULL, 'This is post 1 in Camp 8', NULL, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(41, 5, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 8, NULL, 'This is post 2 in Camp 8', NULL, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(42, 4, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 8, NULL, 'This is post 3 in Camp 8', NULL, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(43, 4, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 8, NULL, 'This is post 4 in Camp 8', NULL, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(44, 6, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 8, NULL, 'This is post 5 in Camp 8', NULL, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(45, 6, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 8, NULL, 'This is post 6 in Camp 8', NULL, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(46, 2, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 8, NULL, 'This is post 7 in Camp 8', NULL, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(47, 4, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 9, NULL, 'This is post 1 in Camp 9', NULL, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(48, 6, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 9, NULL, 'This is post 2 in Camp 9', NULL, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(49, 3, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 9, NULL, 'This is post 3 in Camp 9', NULL, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(50, 3, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 9, NULL, 'This is post 4 in Camp 9', NULL, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(51, 6, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 10, NULL, 'This is post 1 in Camp 10', NULL, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(52, 6, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 10, NULL, 'This is post 2 in Camp 10', NULL, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(53, 6, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 10, NULL, 'This is post 3 in Camp 10', NULL, '2025-04-17 05:18:34', '2025-04-17 05:18:34'),
(54, 7, NULL, NULL, NULL, 'post-media/ZkTC5azKwDkCtuW0yBNYJHANCRtHRXQey06P9yix.jpg', 'image', 'post', 'public', 0, NULL, NULL, NULL, NULL, '2025-04-17 05:34:08', '2025-04-17 05:34:08'),
(55, 7, NULL, NULL, NULL, 'post-media/SQFxAIokBmAcLCtaBNMQ92TJuvE6C87k9WDpYTa5.jpg', 'image', 'post', 'public', 0, NULL, NULL, NULL, NULL, '2025-04-17 05:34:35', '2025-04-17 05:34:35'),
(56, 7, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, 8, NULL, 'fdsafsd', NULL, '2025-04-17 07:27:21', '2025-04-17 07:27:21'),
(57, 8, NULL, NULL, NULL, 'post-media/A8YrWidbHtw31JI79JnpAdkQlA1U7YVnkqTgP093.jpg', 'image', 'post', 'public', 0, NULL, NULL, NULL, NULL, '2025-04-18 10:35:44', '2025-04-18 10:35:44'),
(58, 8, NULL, NULL, NULL, 'post-media/Fd1fLqQXJ7igIhrduuxkSKCc0cXUNaPiroPSM0Un.mp4', 'video', 'post', 'public', 0, NULL, NULL, NULL, NULL, '2025-04-19 08:56:51', '2025-04-19 08:56:51'),
(59, 8, NULL, NULL, NULL, NULL, NULL, 'post', 'public', 0, NULL, NULL, 'Live stream recording: hello everyone', NULL, '2025-06-19 07:05:01', '2025-06-19 07:05:01'),
(60, 8, NULL, NULL, NULL, 'post-media/2wDJSAMqa1AIDWAN8HQzkZVrc430JWfRgwwMXgmk.png', 'image', 'post', 'public', 0, NULL, NULL, NULL, NULL, '2025-06-19 08:05:54', '2025-06-19 08:05:54'),
(61, 1, NULL, NULL, NULL, 'livestream-recordings/stream_21_1750400052.webm', 'video', 'post', 'public', 0, NULL, NULL, NULL, NULL, '2025-06-20 00:14:12', '2025-06-20 00:14:12'),
(62, 8, NULL, NULL, NULL, 'livestream-recordings/stream_23_1750400667.webm', 'video', 'post', 'public', 0, NULL, NULL, NULL, NULL, '2025-06-20 00:24:27', '2025-06-20 00:24:27');

-- --------------------------------------------------------

--
-- Table structure for table `post_media`
--

CREATE TABLE `post_media` (
  `id` bigint UNSIGNED NOT NULL,
  `post_id` bigint UNSIGNED NOT NULL,
  `media_url` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `media_type` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `revenues`
--

CREATE TABLE `revenues` (
  `id` bigint UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `search_logs`
--

CREATE TABLE `search_logs` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED DEFAULT NULL,
  `query` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `search_logs`
--

INSERT INTO `search_logs` (`id`, `user_id`, `query`, `created_at`, `updated_at`) VALUES
(1, 7, 'm', '2025-04-17 05:55:48', '2025-04-17 05:55:48'),
(2, 7, 'm', '2025-04-17 05:55:56', '2025-04-17 05:55:56'),
(3, 7, 'msa', '2025-04-17 05:55:57', '2025-04-17 05:55:57'),
(4, 7, 'm', '2025-04-17 05:55:58', '2025-04-17 05:55:58'),
(5, 7, 'ma', '2025-04-17 05:55:59', '2025-04-17 05:55:59'),
(6, 7, 'mar', '2025-04-17 05:55:59', '2025-04-17 05:55:59'),
(7, 7, 'maru', '2025-04-17 05:56:00', '2025-04-17 05:56:00'),
(8, 7, 'mar', '2025-04-17 05:56:01', '2025-04-17 05:56:01'),
(9, 7, 'a', '2025-04-17 05:56:02', '2025-04-17 05:56:02'),
(10, 7, 'ad', '2025-04-17 05:56:03', '2025-04-17 05:56:03'),
(11, 7, 'a', '2025-04-17 05:56:04', '2025-04-17 05:56:04'),
(12, 8, 'm', '2025-04-19 08:52:35', '2025-04-19 08:52:35'),
(13, 10, 'mar', '2025-04-19 19:52:14', '2025-04-19 19:52:14'),
(14, 10, '123', '2025-04-19 19:52:18', '2025-04-19 19:52:18'),
(15, 26, 'ad', '2025-06-19 08:43:32', '2025-06-19 08:43:32'),
(16, 26, 'ad', '2025-06-19 08:43:42', '2025-06-19 08:43:42');

-- --------------------------------------------------------

--
-- Table structure for table `sessions`
--

CREATE TABLE `sessions` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` bigint UNSIGNED DEFAULT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8mb4_unicode_ci,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_activity` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `sessions`
--

INSERT INTO `sessions` (`id`, `user_id`, `ip_address`, `user_agent`, `payload`, `last_activity`) VALUES
('3sU6uFcAvn9Qv6HplkcjpvllG1wjSzKROTwA5rxC', 8, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoiOVNrS2wzY01KOVRWcTJORm5yZ1JDZ3ZGOU1RSnFWaUtYc01qMXBaSiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NDU6Imh0dHA6Ly8xMjcuMC4wLjE6ODAwMC9hcGkvbGl2ZS1zdHJlYW1zLXN0YXR1cyI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjg7fQ==', 1750703938);

-- --------------------------------------------------------

--
-- Table structure for table `subscriptions`
--

CREATE TABLE `subscriptions` (
  `id` bigint UNSIGNED NOT NULL,
  `subscriber_id` bigint UNSIGNED NOT NULL,
  `creator_id` bigint UNSIGNED NOT NULL,
  `price` decimal(8,2) NOT NULL,
  `started_at` datetime NOT NULL,
  `expires_at` datetime NOT NULL,
  `auto_renew` tinyint(1) NOT NULL DEFAULT '1',
  `status` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `payment_method` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `transaction_id` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `username` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `signup_method` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'email',
  `onboarding_completed` tinyint(1) NOT NULL DEFAULT '0',
  `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_verified` tinyint(1) NOT NULL DEFAULT '0',
  `love_balance` int NOT NULL DEFAULT '10',
  `love_revenue` decimal(10,2) NOT NULL DEFAULT '0.00',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `is_creator` tinyint(1) NOT NULL DEFAULT '0',
  `subscription_price` decimal(8,2) DEFAULT NULL,
  `creator_bio` text COLLATE utf8mb4_unicode_ci,
  `earned_balance` decimal(10,2) NOT NULL DEFAULT '0.00',
  `paid_balance` decimal(10,2) NOT NULL DEFAULT '0.00',
  `payment_email` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `payment_method` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `tax_id` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `creator_balance` decimal(10,2) NOT NULL DEFAULT '0.00'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `name`, `username`, `email`, `phone`, `email_verified_at`, `password`, `signup_method`, `onboarding_completed`, `remember_token`, `is_verified`, `love_balance`, `love_revenue`, `created_at`, `updated_at`, `is_creator`, `subscription_price`, `creator_bio`, `earned_balance`, `paid_balance`, `payment_email`, `payment_method`, `tax_id`, `creator_balance`) VALUES
(1, 'Test User', NULL, '<EMAIL>', NULL, '2025-04-17 05:18:33', '$2y$12$lEnilm6BJaSGSlmTevLLEeZw.4kT3M847NNJYum9UKdY3EwJgCZHO', 'email', 0, '4aEfH53zut', 0, 10, 0.00, '2025-04-17 05:18:33', '2025-04-17 05:18:33', 0, NULL, NULL, 0.00, 0.00, NULL, NULL, NULL, 0.00),
(2, 'Hellen Littel DVM', NULL, '<EMAIL>', NULL, '2025-04-17 05:18:33', '$2y$12$lEnilm6BJaSGSlmTevLLEeZw.4kT3M847NNJYum9UKdY3EwJgCZHO', 'email', 0, '7JFWJMeeB3', 0, 10, 0.00, '2025-04-17 05:18:33', '2025-04-17 05:18:33', 0, NULL, NULL, 0.00, 0.00, NULL, NULL, NULL, 0.00),
(3, 'Viviane Skiles', NULL, '<EMAIL>', NULL, '2025-04-17 05:18:33', '$2y$12$lEnilm6BJaSGSlmTevLLEeZw.4kT3M847NNJYum9UKdY3EwJgCZHO', 'email', 0, 'zjziak5XwZ', 0, 10, 0.00, '2025-04-17 05:18:33', '2025-04-17 05:18:33', 0, NULL, NULL, 0.00, 0.00, NULL, NULL, NULL, 0.00),
(4, 'Herbert Goldner DDS', NULL, '<EMAIL>', NULL, '2025-04-17 05:18:33', '$2y$12$lEnilm6BJaSGSlmTevLLEeZw.4kT3M847NNJYum9UKdY3EwJgCZHO', 'email', 0, '3uzYfRQf30', 0, 10, 0.00, '2025-04-17 05:18:33', '2025-04-17 05:18:33', 0, NULL, NULL, 0.00, 0.00, NULL, NULL, NULL, 0.00),
(5, 'Antonette Smith', NULL, '<EMAIL>', NULL, '2025-04-17 05:18:33', '$2y$12$lEnilm6BJaSGSlmTevLLEeZw.4kT3M847NNJYum9UKdY3EwJgCZHO', 'email', 0, 'R4iPCgt3aE', 0, 10, 0.00, '2025-04-17 05:18:33', '2025-04-17 05:18:33', 0, NULL, NULL, 0.00, 0.00, NULL, NULL, NULL, 0.00),
(6, 'Gilberto Romaguera', NULL, '<EMAIL>', NULL, '2025-04-17 05:18:33', '$2y$12$lEnilm6BJaSGSlmTevLLEeZw.4kT3M847NNJYum9UKdY3EwJgCZHO', 'email', 0, 'joSCr1DJi3', 0, 10, 0.00, '2025-04-17 05:18:33', '2025-04-17 05:18:33', 0, NULL, NULL, 0.00, 0.00, NULL, NULL, NULL, 0.00),
(7, 'Google User', 'fsdafdsa', '<EMAIL>', NULL, NULL, '$2y$12$9WVoEulMdgQhRn/02EtMUOcww1rTaw37QcwQxevulT7lU3ZqRUtum', 'google', 1, NULL, 0, 10, 0.00, '2025-04-17 05:18:52', '2025-04-17 05:19:40', 0, NULL, NULL, 0.00, 0.00, NULL, NULL, NULL, 0.00),
(8, NULL, 'Admin', '<EMAIL>', NULL, NULL, '$2y$12$82WT030fjayW9fUJEaMGPuqPKoaW6kD5NgFliIRhMCYIQlfb2CR/K', 'email', 1, NULL, 0, 10, 0.00, '2025-04-17 12:25:52', '2025-04-17 12:26:28', 0, NULL, NULL, 0.00, 0.00, NULL, NULL, NULL, 0.00),
(9, 'Google User', 'Mironneu', '<EMAIL>', NULL, NULL, '$2y$12$D/ZzK0Pbp8OoJjxPkHGfWej/tZXyQFTgIXCcZZG2z55cAeDhq3/Ji', 'google', 1, NULL, 0, 10, 0.00, '2025-04-19 12:31:54', '2025-04-19 12:32:33', 0, NULL, NULL, 0.00, 0.00, NULL, NULL, NULL, 0.00),
(10, 'Google User', '123', '<EMAIL>', NULL, NULL, '$2y$12$0fkh9WG97W3X27i69jqxw.A/QQsUCRQm0eZtfVzPpkw7WI3J3ediC', 'google', 1, NULL, 0, 10, 0.00, '2025-04-19 19:50:12', '2025-04-19 19:54:28', 0, NULL, NULL, 0.00, 0.00, NULL, NULL, NULL, 0.00),
(11, 'Google User', NULL, '<EMAIL>', NULL, NULL, '$2y$12$iSBBwdfZL4dSjm538jlXbeGCVuyyge.a0X7ukmI4k.pqU6npIcQtq', 'google', 0, NULL, 0, 10, 0.00, '2025-04-20 18:23:23', '2025-04-20 18:23:23', 0, NULL, NULL, 0.00, 0.00, NULL, NULL, NULL, 0.00),
(12, 'Google User', 'maruf11', '<EMAIL>', NULL, NULL, '$2y$12$SK9PXb2RivTy8tMfc1zXQOG5ExNGEGbZU88dXO.RLrARHy/gkq3n.', 'google', 1, NULL, 0, 10, 0.00, '2025-05-03 17:47:35', '2025-05-03 17:48:56', 0, NULL, NULL, 0.00, 0.00, NULL, NULL, NULL, 0.00),
(13, 'Google User', 'admin4444', '<EMAIL>', NULL, NULL, '$2y$12$9q9nIdXgPuJ/lz3o/8rjHudpbNjtqMuM7yLZP2zJkXJCy5JE6D846', 'google', 1, NULL, 0, 10, 0.00, '2025-05-12 14:36:22', '2025-05-12 14:36:52', 0, NULL, NULL, 0.00, 0.00, NULL, NULL, NULL, 0.00),
(14, 'Google User', 'mmmm', '<EMAIL>', NULL, NULL, '$2y$12$Hq866QAu56hjKTA0wJ0rWeSkaV2UbtVWkSm8rGbmAOgQMNSq1fSXq', 'google', 1, NULL, 0, 10, 0.00, '2025-05-12 18:51:29', '2025-05-12 18:53:05', 0, NULL, NULL, 0.00, 0.00, NULL, NULL, NULL, 0.00),
(15, 'Google User', 'Tttt', '<EMAIL>', NULL, NULL, '$2y$12$zEho7ojh0LHzOWiTSQdl5uL8mAp6J/1JxUUdMZ2wwySqzsL48IGWC', 'google', 1, NULL, 0, 10, 0.00, '2025-05-13 14:35:38', '2025-05-13 14:36:26', 0, NULL, NULL, 0.00, 0.00, NULL, NULL, NULL, 0.00),
(16, 'Google User', 'username', '<EMAIL>', NULL, NULL, '$2y$12$kXY1MDSjsonXjxxYTOnFXOtdcIh4JuwsQvagdGlxXr9m8tOdXhW2m', 'google', 0, NULL, 0, 10, 0.00, '2025-05-19 13:10:46', '2025-05-19 13:10:55', 0, NULL, NULL, 0.00, 0.00, NULL, NULL, NULL, 0.00),
(17, 'Google User', 'testing', '<EMAIL>', NULL, NULL, '$2y$12$MwLLRuhYl/bGl.fZipqe3eruCRTNZqKmckMRMcVomge/mH5klFax6', 'google', 1, NULL, 0, 10, 0.00, '2025-05-19 13:12:30', '2025-05-19 13:13:55', 0, NULL, NULL, 0.00, 0.00, NULL, NULL, NULL, 0.00),
(18, 'Google User', NULL, '<EMAIL>', NULL, NULL, '$2y$12$eHwn1SThqh4W.vQ.AQUDDOd8izvi17optCUzQeF5zDtIy9No/NMFC', 'google', 0, NULL, 0, 10, 0.00, '2025-05-19 13:12:34', '2025-05-19 13:12:34', 0, NULL, NULL, 0.00, 0.00, NULL, NULL, NULL, 0.00),
(19, 'Google User', NULL, '<EMAIL>', NULL, NULL, '$2y$12$KAtWDQFqiwgapJt/PaOecOfUgUp.LS68tDEt.MZMf7F0grPZveNxe', 'google', 0, NULL, 0, 10, 0.00, '2025-05-21 20:20:40', '2025-05-21 20:20:40', 0, NULL, NULL, 0.00, 0.00, NULL, NULL, NULL, 0.00),
(20, 'Google User', 'asman20', '<EMAIL>', NULL, NULL, '$2y$12$WNDl/KlF8Pw5skaXueuDA.1FxusVyQ05xusJpU/UcPoQN33lpHdh.', 'google', 1, NULL, 0, 10, 0.00, '2025-05-27 05:41:27', '2025-05-27 05:42:14', 0, NULL, NULL, 0.00, 0.00, NULL, NULL, NULL, 0.00),
(21, 'Google User', 'maruf23', '<EMAIL>', NULL, NULL, '$2y$12$PiG/blVPrlEdquQ6QiL2RONzdwoaH/NYGR7OtX4esx8hR1ehpjRMm', 'google', 1, NULL, 0, 10, 0.00, '2025-05-27 14:30:35', '2025-05-27 14:30:57', 0, NULL, NULL, 0.00, 0.00, NULL, NULL, NULL, 0.00),
(22, 'Google User', 'fff', '<EMAIL>', NULL, NULL, '$2y$12$z6MSuUFJeukkv/jO0W9GM.LKoUqWVF5J2pBNIB6.k8KFd/GBmIKwW', 'google', 1, NULL, 0, 10, 0.00, '2025-06-06 08:55:01', '2025-06-06 08:55:44', 0, NULL, NULL, 0.00, 0.00, NULL, NULL, NULL, 0.00),
(23, 'Google User', 'm123456', '<EMAIL>', NULL, NULL, '$2y$12$bbuxodulO2LdJyvdsa9vG.Dt77qiFwmLnR4NtGUvfW0HA2dJmTdhy', 'google', 1, NULL, 0, 10, 0.00, '2025-06-06 05:18:42', '2025-06-06 05:19:19', 0, NULL, NULL, 0.00, 0.00, NULL, NULL, NULL, 0.00),
(24, 'Google User', '123456', '<EMAIL>', NULL, NULL, '$2y$12$LDjllDbSr5N/RfRgNVxwfegmSpvoe9jNm8Zh.CBc6gzP9CQUN4/Fu', 'google', 1, NULL, 0, 10, 0.00, '2025-06-18 13:22:23', '2025-06-18 13:22:58', 0, NULL, NULL, 0.00, 0.00, NULL, NULL, NULL, 0.00),
(25, 'Google User', NULL, '<EMAIL>', NULL, NULL, '$2y$12$819aqjnoT22gYPVrNHtZZeIRIaiYM.Go6ieWonDQmvFlguMHnQRgi', 'google', 0, NULL, 0, 10, 0.00, '2025-06-19 00:17:41', '2025-06-19 00:17:41', 0, NULL, NULL, 0.00, 0.00, NULL, NULL, NULL, 0.00),
(26, NULL, '555', '<EMAIL>', NULL, NULL, '$2y$12$n3VSgyTq85es/wAKjOi1DOkEZQ3Elsg/ZMBUHgCzNEdSPL0OaEPQi', 'email', 1, NULL, 0, 10, 0.00, '2025-06-19 08:40:03', '2025-06-19 08:40:27', 0, NULL, NULL, 0.00, 0.00, NULL, NULL, NULL, 0.00);

-- --------------------------------------------------------

--
-- Table structure for table `user_interests`
--

CREATE TABLE `user_interests` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `interest_id` bigint UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_interests`
--

INSERT INTO `user_interests` (`id`, `user_id`, `interest_id`, `created_at`, `updated_at`) VALUES
(1, 7, 1, NULL, NULL),
(2, 7, 2, NULL, NULL),
(3, 7, 4, NULL, NULL),
(4, 8, 1, NULL, NULL),
(5, 8, 2, NULL, NULL),
(6, 8, 4, NULL, NULL),
(7, 9, 2, NULL, NULL),
(8, 9, 4, NULL, NULL),
(9, 9, 3, NULL, NULL),
(10, 9, 1, NULL, NULL),
(11, 10, 2, NULL, NULL),
(12, 10, 4, NULL, NULL),
(13, 10, 3, NULL, NULL),
(14, 10, 1, NULL, NULL),
(15, 12, 1, NULL, NULL),
(16, 12, 2, NULL, NULL),
(17, 12, 3, NULL, NULL),
(18, 13, 1, NULL, NULL),
(19, 13, 2, NULL, NULL),
(20, 13, 4, NULL, NULL),
(21, 14, 1, NULL, NULL),
(22, 14, 2, NULL, NULL),
(23, 14, 4, NULL, NULL),
(24, 15, 1, NULL, NULL),
(25, 15, 2, NULL, NULL),
(26, 15, 3, NULL, NULL),
(27, 16, 1, NULL, NULL),
(28, 16, 2, NULL, NULL),
(29, 16, 3, NULL, NULL),
(30, 17, 1, NULL, NULL),
(31, 17, 2, NULL, NULL),
(32, 17, 4, NULL, NULL),
(33, 20, 1, NULL, NULL),
(34, 20, 2, NULL, NULL),
(35, 20, 3, NULL, NULL),
(36, 21, 1, NULL, NULL),
(37, 21, 2, NULL, NULL),
(38, 21, 3, NULL, NULL),
(39, 22, 1, NULL, NULL),
(40, 22, 2, NULL, NULL),
(41, 22, 3, NULL, NULL),
(42, 23, 1, NULL, NULL),
(43, 23, 2, NULL, NULL),
(44, 23, 4, NULL, NULL),
(45, 24, 1, NULL, NULL),
(46, 24, 2, NULL, NULL),
(47, 24, 4, NULL, NULL),
(48, 26, 1, NULL, NULL),
(49, 26, 2, NULL, NULL),
(50, 26, 4, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `user_profiles`
--

CREATE TABLE `user_profiles` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `full_name` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `gender` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `birth_date` date DEFAULT NULL,
  `bio` text COLLATE utf8mb4_unicode_ci,
  `photo` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_profiles`
--

INSERT INTO `user_profiles` (`id`, `user_id`, `full_name`, `gender`, `birth_date`, `bio`, `photo`, `created_at`, `updated_at`) VALUES
(1, 7, NULL, 'male', '1927-01-03', NULL, 'profile-photos/1744888780_479992655_122147455496467064_512424759563337797_n.jpg', '2025-04-17 05:18:58', '2025-04-17 05:19:40'),
(2, 8, NULL, 'male', '1937-07-11', NULL, 'profile-photos/1744914388_479992655_122147455496467064_512424759563337797_n.jpg', '2025-04-17 12:26:03', '2025-04-17 12:26:28'),
(3, 9, NULL, 'male', '1930-07-04', NULL, 'profile-photos/1745065953_1000239252.png', '2025-04-19 12:32:04', '2025-04-19 12:32:33'),
(4, 10, NULL, 'male', '1930-08-09', 'Hesklnbtfxhiln', 'profile-photos/1745092242_1000241631.jpg', '2025-04-19 19:50:20', '2025-04-19 19:54:28'),
(5, 12, NULL, 'female', '1938-08-12', NULL, 'profile-photos/1746294536_adult-hand-dark.png', '2025-05-03 17:48:16', '2025-05-03 17:48:56'),
(6, 13, NULL, 'male', '1938-10-14', NULL, 'profile-photos/1747060612_Untitled-1.jpg', '2025-05-12 14:36:30', '2025-05-12 14:36:52'),
(7, 14, NULL, 'male', '1938-06-12', NULL, 'profile-photos/1747075985_d-sm.jpg', '2025-05-12 18:51:40', '2025-05-12 18:53:05'),
(8, 15, NULL, 'male', '1930-06-06', NULL, 'profile-photos/1747146986_1000000238.jpg', '2025-05-13 14:35:45', '2025-05-13 14:36:26'),
(9, 16, NULL, 'male', '1989-01-04', NULL, NULL, '2025-05-19 13:10:58', '2025-05-19 13:11:15'),
(10, 17, NULL, 'male', '2000-01-06', NULL, 'profile-photos/1747660435_VENG_LPX_BLK_01-500x500.png', '2025-05-19 13:12:51', '2025-05-19 13:13:55'),
(11, 20, NULL, 'male', '1968-01-01', NULL, 'profile-photos/1748324534_images.png', '2025-05-27 05:41:43', '2025-05-27 05:42:14'),
(12, 21, NULL, 'male', '1940-10-14', NULL, 'profile-photos/1748356257_betxwin_sports.webp', '2025-05-27 14:30:41', '2025-05-27 14:30:57'),
(13, 22, NULL, 'male', '1933-11-10', NULL, 'profile-photos/1749200144_milk for child.jpg', '2025-06-06 08:55:11', '2025-06-06 08:55:44'),
(14, 23, NULL, 'male', '1933-06-10', NULL, 'profile-photos/1749208759_WhatsApp Image 2025-02-23 at 9.29.15 PM (1).jpeg', '2025-06-06 05:18:52', '2025-06-06 05:19:19'),
(15, 24, NULL, 'male', '1935-11-14', NULL, 'profile-photos/1750274577_d9a09e35-ded7-4ab2-83d0-3adae8634491.png', '2025-06-18 13:22:37', '2025-06-18 13:22:58'),
(16, 26, NULL, 'male', '1934-03-10', NULL, 'profile-photos/1750344027_thumb.jpg', '2025-06-19 08:40:09', '2025-06-19 08:40:27');

-- --------------------------------------------------------

--
-- Table structure for table `withdrawals`
--

CREATE TABLE `withdrawals` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `status` enum('pending','processing','completed','rejected') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `notes` text COLLATE utf8mb4_unicode_ci,
  `processed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `cache`
--
ALTER TABLE `cache`
  ADD PRIMARY KEY (`key`);

--
-- Indexes for table `cache_locks`
--
ALTER TABLE `cache_locks`
  ADD PRIMARY KEY (`key`);

--
-- Indexes for table `camps`
--
ALTER TABLE `camps`
  ADD PRIMARY KEY (`id`),
  ADD KEY `camps_created_by_foreign` (`created_by`);

--
-- Indexes for table `camp_posts`
--
ALTER TABLE `camp_posts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `camp_posts_camp_id_foreign` (`camp_id`),
  ADD KEY `camp_posts_user_id_foreign` (`user_id`);

--
-- Indexes for table `camp_user`
--
ALTER TABLE `camp_user`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `camp_user_camp_id_user_id_unique` (`camp_id`,`user_id`),
  ADD KEY `camp_user_user_id_foreign` (`user_id`);

--
-- Indexes for table `chats`
--
ALTER TABLE `chats`
  ADD PRIMARY KEY (`id`),
  ADD KEY `chats_sender_id_foreign` (`sender_id`),
  ADD KEY `chats_receiver_id_foreign` (`receiver_id`);

--
-- Indexes for table `comments`
--
ALTER TABLE `comments`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `earnings`
--
ALTER TABLE `earnings`
  ADD PRIMARY KEY (`id`),
  ADD KEY `earnings_creator_id_foreign` (`creator_id`);

--
-- Indexes for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

--
-- Indexes for table `followers`
--
ALTER TABLE `followers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `followers_follower_id_followed_id_unique` (`follower_id`,`followed_id`),
  ADD KEY `followers_followed_id_foreign` (`followed_id`);

--
-- Indexes for table `groups`
--
ALTER TABLE `groups`
  ADD PRIMARY KEY (`id`),
  ADD KEY `groups_created_by_foreign` (`created_by`);

--
-- Indexes for table `interests`
--
ALTER TABLE `interests`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `jobs`
--
ALTER TABLE `jobs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `jobs_queue_index` (`queue`);

--
-- Indexes for table `job_batches`
--
ALTER TABLE `job_batches`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `livestreams`
--
ALTER TABLE `livestreams`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `livestreams_stream_key_unique` (`stream_key`),
  ADD KEY `livestreams_user_id_foreign` (`user_id`);

--
-- Indexes for table `livestream_comments`
--
ALTER TABLE `livestream_comments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `livestream_comments_user_id_foreign` (`user_id`),
  ADD KEY `livestream_comments_live_stream_id_created_at_index` (`live_stream_id`,`created_at`);

--
-- Indexes for table `live_streams`
--
ALTER TABLE `live_streams`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `live_streams_stream_key_unique` (`stream_key`),
  ADD KEY `live_streams_user_id_foreign` (`user_id`),
  ADD KEY `live_streams_post_id_foreign` (`post_id`);

--
-- Indexes for table `love`
--
ALTER TABLE `love`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `loves_user_id_post_id_unique` (`user_id`,`post_id`),
  ADD KEY `loves_post_id_foreign` (`post_id`);

--
-- Indexes for table `love_wallets`
--
ALTER TABLE `love_wallets`
  ADD PRIMARY KEY (`id`),
  ADD KEY `love_wallets_user_id_foreign` (`user_id`);

--
-- Indexes for table `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `notifications_user_id_foreign` (`user_id`);

--
-- Indexes for table `payout_requests`
--
ALTER TABLE `payout_requests`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `posts`
--
ALTER TABLE `posts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `posts_user_id_foreign` (`user_id`),
  ADD KEY `posts_camp_id_foreign` (`camp_id`);

--
-- Indexes for table `post_media`
--
ALTER TABLE `post_media`
  ADD PRIMARY KEY (`id`),
  ADD KEY `post_media_post_id_foreign` (`post_id`);

--
-- Indexes for table `revenues`
--
ALTER TABLE `revenues`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `search_logs`
--
ALTER TABLE `search_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `search_logs_user_id_foreign` (`user_id`);

--
-- Indexes for table `sessions`
--
ALTER TABLE `sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `sessions_user_id_index` (`user_id`),
  ADD KEY `sessions_last_activity_index` (`last_activity`);

--
-- Indexes for table `subscriptions`
--
ALTER TABLE `subscriptions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `subscriptions_subscriber_id_foreign` (`subscriber_id`),
  ADD KEY `subscriptions_creator_id_foreign` (`creator_id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_username_unique` (`username`),
  ADD UNIQUE KEY `users_email_unique` (`email`),
  ADD UNIQUE KEY `users_phone_unique` (`phone`);

--
-- Indexes for table `user_interests`
--
ALTER TABLE `user_interests`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_interests_user_id_foreign` (`user_id`),
  ADD KEY `user_interests_interest_id_foreign` (`interest_id`);

--
-- Indexes for table `user_profiles`
--
ALTER TABLE `user_profiles`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_profiles_user_id_foreign` (`user_id`);

--
-- Indexes for table `withdrawals`
--
ALTER TABLE `withdrawals`
  ADD PRIMARY KEY (`id`),
  ADD KEY `withdrawals_user_id_foreign` (`user_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `camps`
--
ALTER TABLE `camps`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `camp_posts`
--
ALTER TABLE `camp_posts`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `camp_user`
--
ALTER TABLE `camp_user`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=39;

--
-- AUTO_INCREMENT for table `chats`
--
ALTER TABLE `chats`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `comments`
--
ALTER TABLE `comments`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `earnings`
--
ALTER TABLE `earnings`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=33;

--
-- AUTO_INCREMENT for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `followers`
--
ALTER TABLE `followers`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `groups`
--
ALTER TABLE `groups`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `interests`
--
ALTER TABLE `interests`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `jobs`
--
ALTER TABLE `jobs`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=43;

--
-- AUTO_INCREMENT for table `livestreams`
--
ALTER TABLE `livestreams`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=50;

--
-- AUTO_INCREMENT for table `livestream_comments`
--
ALTER TABLE `livestream_comments`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `live_streams`
--
ALTER TABLE `live_streams`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=25;

--
-- AUTO_INCREMENT for table `love`
--
ALTER TABLE `love`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=33;

--
-- AUTO_INCREMENT for table `love_wallets`
--
ALTER TABLE `love_wallets`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=38;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `payout_requests`
--
ALTER TABLE `payout_requests`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `posts`
--
ALTER TABLE `posts`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=63;

--
-- AUTO_INCREMENT for table `post_media`
--
ALTER TABLE `post_media`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `revenues`
--
ALTER TABLE `revenues`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `search_logs`
--
ALTER TABLE `search_logs`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT for table `subscriptions`
--
ALTER TABLE `subscriptions`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=27;

--
-- AUTO_INCREMENT for table `user_interests`
--
ALTER TABLE `user_interests`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=51;

--
-- AUTO_INCREMENT for table `user_profiles`
--
ALTER TABLE `user_profiles`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT for table `withdrawals`
--
ALTER TABLE `withdrawals`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `camps`
--
ALTER TABLE `camps`
  ADD CONSTRAINT `camps_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`);

--
-- Constraints for table `camp_posts`
--
ALTER TABLE `camp_posts`
  ADD CONSTRAINT `camp_posts_camp_id_foreign` FOREIGN KEY (`camp_id`) REFERENCES `camps` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `camp_posts_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `camp_user`
--
ALTER TABLE `camp_user`
  ADD CONSTRAINT `camp_user_camp_id_foreign` FOREIGN KEY (`camp_id`) REFERENCES `camps` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `camp_user_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `chats`
--
ALTER TABLE `chats`
  ADD CONSTRAINT `chats_receiver_id_foreign` FOREIGN KEY (`receiver_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `chats_sender_id_foreign` FOREIGN KEY (`sender_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `earnings`
--
ALTER TABLE `earnings`
  ADD CONSTRAINT `earnings_creator_id_foreign` FOREIGN KEY (`creator_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `followers`
--
ALTER TABLE `followers`
  ADD CONSTRAINT `followers_followed_id_foreign` FOREIGN KEY (`followed_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `followers_follower_id_foreign` FOREIGN KEY (`follower_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `groups`
--
ALTER TABLE `groups`
  ADD CONSTRAINT `groups_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`);

--
-- Constraints for table `livestreams`
--
ALTER TABLE `livestreams`
  ADD CONSTRAINT `livestreams_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `livestream_comments`
--
ALTER TABLE `livestream_comments`
  ADD CONSTRAINT `livestream_comments_live_stream_id_foreign` FOREIGN KEY (`live_stream_id`) REFERENCES `live_streams` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `livestream_comments_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `live_streams`
--
ALTER TABLE `live_streams`
  ADD CONSTRAINT `live_streams_post_id_foreign` FOREIGN KEY (`post_id`) REFERENCES `posts` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `live_streams_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `love`
--
ALTER TABLE `love`
  ADD CONSTRAINT `loves_post_id_foreign` FOREIGN KEY (`post_id`) REFERENCES `posts` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `loves_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `love_wallets`
--
ALTER TABLE `love_wallets`
  ADD CONSTRAINT `love_wallets_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `notifications`
--
ALTER TABLE `notifications`
  ADD CONSTRAINT `notifications_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `posts`
--
ALTER TABLE `posts`
  ADD CONSTRAINT `posts_camp_id_foreign` FOREIGN KEY (`camp_id`) REFERENCES `camps` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `posts_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `post_media`
--
ALTER TABLE `post_media`
  ADD CONSTRAINT `post_media_post_id_foreign` FOREIGN KEY (`post_id`) REFERENCES `posts` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `search_logs`
--
ALTER TABLE `search_logs`
  ADD CONSTRAINT `search_logs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `subscriptions`
--
ALTER TABLE `subscriptions`
  ADD CONSTRAINT `subscriptions_creator_id_foreign` FOREIGN KEY (`creator_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `subscriptions_subscriber_id_foreign` FOREIGN KEY (`subscriber_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_interests`
--
ALTER TABLE `user_interests`
  ADD CONSTRAINT `user_interests_interest_id_foreign` FOREIGN KEY (`interest_id`) REFERENCES `interests` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_interests_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_profiles`
--
ALTER TABLE `user_profiles`
  ADD CONSTRAINT `user_profiles_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `withdrawals`
--
ALTER TABLE `withdrawals`
  ADD CONSTRAINT `withdrawals_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
