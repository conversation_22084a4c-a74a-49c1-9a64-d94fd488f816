<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Comment extends Model
{
    protected $fillable = [
        'user_id',
        'post_id',
        'content',
        'parent_id'
    ];

    protected $appends = ['can_edit', 'can_delete'];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function post()
    {
        return $this->belongsTo(Post::class);
    }

    public function replies()
    {
        return $this->hasMany(Comment::class, 'parent_id');
    }

    public function getCanEditAttribute()
    {
        return auth()->check() && $this->user_id === auth()->id() && 
               $this->created_at->diffInMinutes(now()) < 30; // Can edit within 30 mins
    }

    public function getCanDeleteAttribute()
    {
        return auth()->check() && 
               ($this->user_id === auth()->id() || 
                $this->post->user_id === auth()->id());
    }
} 