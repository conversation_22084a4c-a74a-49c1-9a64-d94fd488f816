@extends('layouts.app')

@section('content')
<div class="payment-success-container">
    <div class="success-icon">
        <i class="fas fa-check-circle"></i>
    </div>
    
    <h1>Payment Successful!</h1>
    
    <p class="success-message">{{ number_format($loveAmount) }} loves have been added to your wallet.</p>
    
    <div class="current-balance">
        <p>Your current balance:</p>
        <div class="balance-display">
            <i class="fas fa-heart"></i>
            <span>{{ number_format(Auth::user()->loveWallet->love_balance) }}</span>
        </div>
    </div>
    
    <div class="action-buttons">
        <a href="{{ route('loves.buy') }}" class="btn btn-secondary">Buy More Loves</a>
        <a href="{{ route('home') }}" class="btn btn-primary">Back to Home</a>
    </div>
</div>

<style>
.payment-success-container {
    max-width: 500px;
    margin: 50px auto;
    text-align: center;
    padding: 30px;
    background: #222;
    border-radius: 10px;
}

.success-icon {
    font-size: 80px;
    color: #4CAF50;
    margin-bottom: 20px;
}

h1 {
    margin-bottom: 20px;
}

.success-message {
    font-size: 18px;
    margin-bottom: 30px;
}

.current-balance {
    background: #333;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 30px;
}

.balance-display {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 24px;
    font-weight: bold;
}

.balance-display i {
    color: #ff0066;
}

.action-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.btn {
    padding: 12px 25px;
    border-radius: 5px;
    font-weight: bold;
    text-decoration: none;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #FFD700;
    color: black;
}

.btn-secondary {
    background: #444;
    color: white;
}

.btn:hover {
    transform: translateY(-3px);
}
</style>
@endsection 