<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Auth\Passwords\CanResetPassword;
use Illuminate\Contracts\Auth\CanResetPassword as CanResetPasswordContract;
use Illuminate\Support\Facades\Storage;

class User extends Authenticatable implements CanResetPasswordContract
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, CanResetPassword;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'username',
        'email',
        'phone',
        'password',
        'bio',
        'avatar',
        'gender',
        'birthdate',
        'onboarding_completed',
        'is_verified',
        'is_creator',
        'creator_bio',
        'creator_category',
        'subscription_price',
        'creator_balance',
        'signup_method',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'onboarding_completed' => 'boolean',
        ];
    }

    public function profile()
    {
        return $this->hasOne(UserProfile::class);
    }

    public function interests()
    {
        return $this->belongsToMany(Interest::class, 'user_interests');
    }

    public function posts()
    {
        return $this->hasMany(Post::class);
    }

    /**
     * Get all subscriptions made by this user to creators
     */
    public function subscriptions()
    {
        return $this->hasMany(Subscription::class, 'subscriber_id');
    }

    /**
     * Get all subscribers to this creator
     */
    public function subscribers()
    {
        return $this->hasMany(Subscription::class, 'creator_id');
    }

    /**
     * Get all earnings for this creator
     */
    public function earnings()
    {
        return $this->hasMany(Earning::class, 'creator_id');
    }

    /**
     * Check if user is subscribed to a creator
     */
    public function isSubscribedTo($creatorId)
    {
        return $this->subscriptions()
            ->where('creator_id', $creatorId)
            ->where('status', 'active')
            ->where('expires_at', '>', now())
            ->exists();
    }

    public function loveWallet()
    {
        return $this->hasOne(LoveWallet::class);
    }

    public function loves()
    {
        return $this->hasMany(Love::class);
    }

    public function hasEnoughLoves($count = 1)
    {
        return $this->loveWallet?->hasEnoughLoves($count) ?? false;
    }

    public function withdrawals()
    {
        return $this->hasMany(Withdrawal::class);
    }

    public function canWithdraw()
    {
        return $this->creator_balance >= 5.00;
    }

    /**
     * Get the users that are following this user
     */
    public function followers()
    {
        return $this->belongsToMany(User::class, 'followers', 'followed_id', 'follower_id')
            ->withTimestamps();
    }

    /**
     * Get the users that this user is following
     */
    public function following()
    {
        return $this->belongsToMany(User::class, 'followers', 'follower_id', 'followed_id')
            ->withTimestamps();
    }

   public function camps()
   {
    return $this->belongsToMany(Camp::class, 'camp_members', 'user_id', 'camp_id');
   }

   public function liveStreams()
   {
    return $this->hasMany(LiveStream::class);
   }

   public function getAvatarUrlAttribute()
   {
       return $this->avatar 
           ? Storage::disk('public')->url($this->avatar)
           : asset('images/default-avatar.jpg');
   }

   public function isFollowing(User $user)
   {
       return $this->following()->where('followed_id', $user->id)->exists();
   }

   public function approvedFollowers()
   {
       return $this->followers()->wherePivot('approved', true);
   }
}
