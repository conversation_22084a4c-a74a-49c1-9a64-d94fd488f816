<?php

namespace Database\Seeders;

use App\Models\Camp;
use App\Models\User;
use App\Models\Post;
use Illuminate\Database\Seeder;

class CampSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get some users or create them if they don't exist
        $users = User::take(5)->get();
        
        if ($users->count() < 5) {
            $users = User::factory(5)->create();
        }
        
        // Create 10 camps
        $camps = [];
        for ($i = 0; $i < 10; $i++) {
            $creator = $users->random();
            $isPrivate = rand(0, 1);
            
            $camp = Camp::create([
                'name' => 'Camp ' . ($i + 1),
                'description' => 'This is a ' . ($isPrivate ? 'private' : 'public') . ' camp for testing purposes.',
                'type' => $isPrivate ? 'private' : 'public',
                'created_by' => $creator->id,
            ]);
            
            // Add creator as a member
            $camp->members()->attach($creator->id);
            
            // Add some random members
            $members = $users->except($creator->id)->random(rand(1, 4));
            foreach ($members as $member) {
                $camp->members()->attach($member->id);
            }
            
            // Create some posts in the camp
            $postCount = rand(3, 8);
            for ($j = 0; $j < $postCount; $j++) {
                $poster = $camp->members->random();
                Post::create([
                    'camp_id' => $camp->id,
                    'user_id' => $poster->id,
                    'content' => 'This is post ' . ($j + 1) . ' in ' . $camp->name,
                ]);
            }
            
            $camps[] = $camp;
        }
    }
} 